package com.appblock.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.appblock.databinding.ItemAppBinding
import com.appblock.ui.AppSelectionActivity

class AppListAdapter(
    private val onAppSelectionChanged: (AppSelectionActivity.AppInfo, Boolean) -> Unit
) : ListAdapter<AppSelectionActivity.AppInfo, AppListAdapter.AppViewHolder>(AppDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val binding = ItemAppBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return AppViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class AppViewHolder(private val binding: ItemAppBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(appInfo: AppSelectionActivity.AppInfo) {
            binding.apply {
                // Set app info
                tvAppName.text = appInfo.appName
                tvPackageName.text = appInfo.packageName
                ivAppIcon.setImageDrawable(appInfo.icon)
                
                // Show system app indicator
                if (appInfo.isSystemApp) {
                    tvSystemApp.visibility = android.view.View.VISIBLE
                    tvSystemApp.text = "SYSTEM"
                } else {
                    tvSystemApp.visibility = android.view.View.GONE
                }
                
                // Set checkbox state without triggering listener
                checkboxBlocked.setOnCheckedChangeListener(null)
                checkboxBlocked.isChecked = appInfo.isBlocked
                
                // Set up click listeners
                root.setOnClickListener {
                    checkboxBlocked.isChecked = !checkboxBlocked.isChecked
                    onAppSelectionChanged(appInfo, checkboxBlocked.isChecked)
                }
                
                checkboxBlocked.setOnCheckedChangeListener { _, isChecked ->
                    onAppSelectionChanged(appInfo, isChecked)
                }
                
                // Visual feedback for blocked apps
                if (appInfo.isBlocked) {
                    root.alpha = 0.7f
                    root.setBackgroundColor(
                        androidx.core.content.ContextCompat.getColor(
                            root.context,
                            com.appblock.R.color.blocked_app_background
                        )
                    )
                } else {
                    root.alpha = 1.0f
                    root.setBackgroundColor(
                        androidx.core.content.ContextCompat.getColor(
                            root.context,
                            android.R.color.transparent
                        )
                    )
                }
            }
        }
    }

    private class AppDiffCallback : DiffUtil.ItemCallback<AppSelectionActivity.AppInfo>() {
        override fun areItemsTheSame(
            oldItem: AppSelectionActivity.AppInfo,
            newItem: AppSelectionActivity.AppInfo
        ): Boolean {
            return oldItem.packageName == newItem.packageName
        }

        override fun areContentsTheSame(
            oldItem: AppSelectionActivity.AppInfo,
            newItem: AppSelectionActivity.AppInfo
        ): Boolean {
            return oldItem == newItem
        }
    }
}
