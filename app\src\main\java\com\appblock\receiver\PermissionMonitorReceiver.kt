package com.appblock.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.appblock.data.SettingsManager
import com.appblock.utils.PermissionUtils

class PermissionMonitorReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "PermissionMonitor"
        const val ACTION_CHECK_PERMISSIONS = "com.appblock.CHECK_PERMISSIONS"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            ACTION_CHECK_PERMISSIONS -> {
                checkPermissions(context)
            }
            Intent.ACTION_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_ADDED,
            Intent.ACTION_PACKAGE_REMOVED -> {
                // Monitor for package changes that might affect permissions
                handlePackageChange(context, intent)
            }
        }
    }

    private fun checkPermissions(context: Context) {
        Log.d(TAG, "Checking permissions status")
        
        val settingsManager = SettingsManager(context)
        
        // Check if critical permissions are still granted
        val hasUsageStats = PermissionUtils.hasUsageStatsPermission(context)
        val hasAccessibility = PermissionUtils.isAccessibilityServiceEnabled(context)
        val hasDeviceAdmin = PermissionUtils.isDeviceAdminActive(context)
        
        Log.d(TAG, "Permission status - Usage Stats: $hasUsageStats, Accessibility: $hasAccessibility, Device Admin: $hasDeviceAdmin")
        
        // If app blocking is enabled but critical permissions are missing, log it
        if (settingsManager.isAppBlockingEnabled()) {
            if (!hasUsageStats || !hasAccessibility) {
                Log.w(TAG, "App blocking is enabled but critical permissions are missing")
                // Could send notification to parent here
            }
        }
        
        // Update last activity
        settingsManager.updateLastActivity()
    }

    private fun handlePackageChange(context: Context, intent: Intent) {
        val packageName = intent.dataString?.removePrefix("package:")
        Log.d(TAG, "Package change detected: $packageName, action: ${intent.action}")
        
        // If our own package was updated, restart services
        if (packageName == context.packageName) {
            Log.d(TAG, "Our package was updated, checking services")
            // Could restart services here if needed
        }
    }
}
