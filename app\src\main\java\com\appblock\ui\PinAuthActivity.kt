package com.appblock.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.appblock.R
import com.appblock.data.SettingsManager
import com.appblock.databinding.ActivityPinAuthBinding

class PinAuthActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "PinAuthActivity"
        private const val MAX_ATTEMPTS = 3
    }

    private lateinit var binding: ActivityPinAuthBinding
    private lateinit var settingsManager: SettingsManager

    private var blockedPackageName: String? = null
    private var action: String? = null
    private var attemptCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPinAuthBinding.inflate(layoutInflater)
        setContentView(binding.root)

        settingsManager = SettingsManager(this)

        // Get intent data
        blockedPackageName = intent.getStringExtra("blocked_package")
        action = intent.getStringExtra("action") ?: "temporary_allow"

        setupUI()
        updateUI()
    }

    private fun setupUI() {
        // Set up PIN input
        binding.etPin.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                if (s?.length == 4) {
                    // Auto-verify when 4 digits are entered
                    verifyPin(s.toString())
                }
            }
        })

        // Set up buttons
        binding.btnVerify.setOnClickListener {
            val pin = binding.etPin.text.toString()
            if (pin.length >= 4) {
                verifyPin(pin)
            } else {
                Toast.makeText(this, "Please enter at least 4 digits", Toast.LENGTH_SHORT).show()
            }
        }

        binding.btnCancel.setOnClickListener {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }

        // Set up number pad
        setupNumberPad()
    }

    private fun setupNumberPad() {
        val numberButtons = listOf(
            binding.btn0, binding.btn1, binding.btn2, binding.btn3,
            binding.btn4, binding.btn5, binding.btn6, binding.btn7,
            binding.btn8, binding.btn9
        )

        numberButtons.forEachIndexed { index, button ->
            button.setOnClickListener {
                val currentText = binding.etPin.text.toString()
                if (currentText.length < 6) { // Allow up to 6 digits
                    binding.etPin.setText(currentText + index)
                    binding.etPin.setSelection(binding.etPin.text?.length ?: 0)
                }
            }
        }

        binding.btnBackspace.setOnClickListener {
            val currentText = binding.etPin.text.toString()
            if (currentText.isNotEmpty()) {
                binding.etPin.setText(currentText.dropLast(1))
                binding.etPin.setSelection(binding.etPin.text?.length ?: 0)
            }
        }

        binding.btnClear.setOnClickListener {
            binding.etPin.setText("")
        }
    }

    private fun updateUI() {
        when (action) {
            "temporary_allow" -> {
                binding.tvTitle.text = "Enter Parent PIN"
                binding.tvMessage.text = "Enter your PIN to temporarily allow this app"
            }
            "request_time" -> {
                binding.tvTitle.text = "Request More Time"
                binding.tvMessage.text = "Enter parent PIN to request additional time"
            }
            else -> {
                binding.tvTitle.text = "Enter Parent PIN"
                binding.tvMessage.text = "PIN required to continue"
            }
        }

        // Show app name if available
        blockedPackageName?.let { packageName ->
            try {
                val appInfo = packageManager.getApplicationInfo(packageName, 0)
                val appName = packageManager.getApplicationLabel(appInfo).toString()
                binding.tvAppName.text = "App: $appName"
                binding.tvAppName.visibility = android.view.View.VISIBLE
            } catch (e: Exception) {
                binding.tvAppName.visibility = android.view.View.GONE
            }
        }
    }

    private fun verifyPin(pin: String) {
        if (settingsManager.verifyParentPin(pin)) {
            // PIN correct
            handleSuccessfulAuth()
        } else {
            // PIN incorrect
            handleFailedAuth()
        }
    }

    private fun handleSuccessfulAuth() {
        Log.d(TAG, "PIN verification successful")

        when (action) {
            "temporary_allow" -> {
                showTemporaryAllowDialog()
            }
            "request_time" -> {
                showTimeRequestDialog()
            }
            else -> {
                // Default: allow for 15 minutes
                returnResult(15 * 60 * 1000L) // 15 minutes in milliseconds
            }
        }
    }

    private fun handleFailedAuth() {
        attemptCount++

        if (attemptCount >= MAX_ATTEMPTS) {
            Toast.makeText(this, "Too many failed attempts", Toast.LENGTH_LONG).show()
            setResult(Activity.RESULT_CANCELED)
            finish()
            return
        }

        val remainingAttempts = MAX_ATTEMPTS - attemptCount
        Toast.makeText(this, "Incorrect PIN. $remainingAttempts attempts remaining", Toast.LENGTH_SHORT).show()

        // Clear PIN input
        binding.etPin.setText("")

        // Add visual feedback
        binding.etPin.error = "Incorrect PIN"
    }

    private fun showTemporaryAllowDialog() {
        val options = arrayOf(
            "5 minutes",
            "15 minutes",
            "30 minutes",
            "1 hour",
            "Until bedtime"
        )

        val durations = arrayOf(
            5 * 60 * 1000L,      // 5 minutes
            15 * 60 * 1000L,     // 15 minutes
            30 * 60 * 1000L,     // 30 minutes
            60 * 60 * 1000L,     // 1 hour
            getTimeUntilBedtime() // Until bedtime
        )

        AlertDialog.Builder(this)
            .setTitle("Allow app for how long?")
            .setItems(options) { _, which ->
                returnResult(durations[which])
            }
            .setNegativeButton("Cancel") { _, _ ->
                setResult(Activity.RESULT_CANCELED)
                finish()
            }
            .show()
    }

    private fun showTimeRequestDialog() {
        val options = arrayOf(
            "Add 15 minutes",
            "Add 30 minutes",
            "Add 1 hour",
            "Remove time limit for today"
        )

        val durations = arrayOf(
            15 * 60 * 1000L,     // 15 minutes
            30 * 60 * 1000L,     // 30 minutes
            60 * 60 * 1000L,     // 1 hour
            24 * 60 * 60 * 1000L // 24 hours (rest of day)
        )

        AlertDialog.Builder(this)
            .setTitle("Add time for this app?")
            .setItems(options) { _, which ->
                returnResult(durations[which])
            }
            .setNegativeButton("Cancel") { _, _ ->
                setResult(Activity.RESULT_CANCELED)
                finish()
            }
            .show()
    }

    private fun getTimeUntilBedtime(): Long {
        // Default bedtime: 9 PM
        val calendar = java.util.Calendar.getInstance()
        val currentHour = calendar.get(java.util.Calendar.HOUR_OF_DAY)

        return if (currentHour < 21) {
            // Before 9 PM - calculate time until 9 PM
            val bedtimeCalendar = java.util.Calendar.getInstance().apply {
                set(java.util.Calendar.HOUR_OF_DAY, 21)
                set(java.util.Calendar.MINUTE, 0)
                set(java.util.Calendar.SECOND, 0)
            }
            bedtimeCalendar.timeInMillis - System.currentTimeMillis()
        } else {
            // After 9 PM - allow until next day 9 PM
            24 * 60 * 60 * 1000L
        }
    }

    private fun returnResult(durationMs: Long) {
        val resultIntent = Intent().apply {
            putExtra("duration_ms", durationMs)
            putExtra("blocked_package", blockedPackageName)
        }

        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }

    override fun onBackPressed() {
        // Allow back button to cancel
        setResult(Activity.RESULT_CANCELED)
        super.onBackPressed()
    }
}
