<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_pin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter PIN (4-6 digits)"
            android:inputType="numberPassword"
            android:maxLength="6" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_confirm_pin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Confirm PIN"
            android:inputType="numberPassword"
            android:maxLength="6" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
