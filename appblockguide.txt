Android App Blocking Strategies
 Designing a robust parental-control blocker (without root or device-owner mode) means creatively using
 Android’s public APIs. A common approach is to monitor the foreground app and immediately obstruct it
 if it’s disallowed. One solution is to implement an AccessibilityService that listens for window-change
 events. The service’s 
onAccessibilityEvent callback will fire on every activity switch – specifically when
 the event type is 
TYPE_WINDOW_STATE_CHANGED . By checking 
1
 event.getPackageName() , the service
 knows which app just came to the foreground . When a “blocked” app launches, the service can instantly
 cover the screen with its own UI. For example, in 
onAccessibilityEvent you could write: 
@Override
 public void onAccessibilityEvent(AccessibilityEvent event) {
 if (event.getEventType() == TYPE_WINDOW_STATE_CHANGED) {
 String pkg = event.getPackageName().toString();
 if (isBlocked(pkg)) {
 // Show a full-screen overlay to block the app
 WindowManager wm = (WindowManager) getSystemService(WINDOW_SERVICE);
 FrameLayout overlay = new FrameLayout(this);
 WindowManager.LayoutParams lp = new WindowManager.LayoutParams();
 lp.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY;
 lp.flags = WindowManager.LayoutParams.FLAG_FULLSCREEN;
 lp.width = MATCH_PARENT; lp.height = MATCH_PARENT;
 wm.addView(overlay, lp);
 }
 }
 }
 This overlay can display a warning or require a PIN to dismiss. Critically, Android provides
 WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY which lets an AccessibilityService
 draw above all other apps (even system UI) without needing special “draw over apps” permission. In practice,
 overlays created this way will appear even on the Settings screen . A recent Android developer confirmed
 2
 that using 
TYPE_ACCESSIBILITY_OVERLAY indeed “draws everywhere … even on top of the settings app
 and system dialogs” without needing the normal SYSTEM_ALERT_WINDOW permission . In short, by
 using an accessibility service plus a full-screen overlay, the parental app can instantly block a target app’s
 interface as soon as it appears. 
• 
• 
1
 2
 Detecting apps via Accessibility: As noted, an AccessibilityService can catch every window change
 , making detection near-instant. 
Blocking via overlay: Use 
TYPE_ACCESSIBILITY_OVERLAY to cover the foreground with your
 own UI
 2
 . This intercepts all touches so the child cannot interact with the blocked app until the
 parent allows it. 
1
UsageStats and Background Monitoring
 Because an AccessibilityService can be disabled by the user, a good fallback is Android’s
 UsageStatsManager (with the 
PACKAGE_USAGE_STATS permission). This API lets your app poll which
 app is in the foreground. For example, periodically querying 
queryEvents(...) for events of type
 MOVE_TO_FOREGROUND can reveal the latest active package name
 3
 . Sample logic: 
UsageStatsManager usm =
 (UsageStatsManager)getSystemService(USAGE_STATS_SERVICE);
 long end = System.currentTimeMillis();
 long begin = end- 10_000; // last 10 seconds
 UsageEvents events = usm.queryEvents(begin, end);
 while (events.hasNextEvent()) {
 UsageEvents.Event ev = new UsageEvents.Event();
 events.getNextEvent(ev);
 if (ev.getEventType() == UsageEvents.Event.MOVE_TO_FOREGROUND) {
 String pkg = ev.getPackageName();
 if (isBlocked(pkg)) {
 // launch overlay or notification
 }
 }
 }
 3
 This requires the user to grant “Usage Access” for your app in Settings. The official docs confirm that
 queryEvents() returns events only when given this permission . UsageStats can run in a background
 worker or service, polling every few seconds. While this is not as instant as Accessibility, it provides a second
 line of defense. If the AccessibilityService is turned off or blocked, the UsageStats approach will still
 eventually catch the forbidden app and can then trigger the same overlay or lockout screen. (Note that
 queryEvents() holds only a short history, so we poll frequently – e.g. every 5–10 seconds). You can also
 use 
queryUsageStats() to see aggregated usage, but 
transitions. 
queryEvents() is more immediate for
 1
 Hybrid model: In practice, the app can combine both methods. For example, always use Accessibility if
 available (because it triggers immediately ), and meanwhile run a UsageStats watcher in the
 background as a backup. If the AccessibilityService is disabled or killed, the UsageStats monitor can detect
 the misuse shortly after. Conversely, if UsageStats permission is revoked, the app can rely on the
 AccessibilityService alone. This redundancy makes the blocker more resilient. 
• 
• 
Example flow: Accessibility sees app launch → overlay blocks it. If Accessibility is off, UsageStats
 catches the event after a few seconds → app brings up an alert or overlay. 
Rate-limiting: The overlay logic can also enforce time limits (e.g. block after X minutes) by tracking
 usage via 
queryUsageStats() and comparing elapsed time. 
2
Service Resilience and Anti-Tampering
 To resist tampering, the blocking logic itself should run as persistently as possible. Run your detection code
 inside a Service marked as foreground (with 
startForeground() ) so Android is less likely to kill it for
 resources. For example, start a minimal ongoing notification so the user sees that “Parental Control” is
 active – this also prevents easy silent killing by the OS. In your Service’s 
onStartCommand() , return
 START_STICKY to ask Android to restart it after being killed. Also request 
RECEIVE_BOOT_COMPLETED in
 the manifest so the service auto-starts on device boot. These steps help the app recover after reboots or
 resource kills, although they cannot override a user’s explicit “Force Stop.” 
You can also assign your service to a separate process (
 android:process=":remote" ) in the manifest.
 This way, if the main app/UI is killed, the monitoring service may continue running. If the service itself is
 killed, it can attempt to restart itself or schedule a task to bring itself back online. Unfortunately, due to
 Android’s design, a deliberate “Force Stop” by the user will terminate the entire app package (including all
 processes) and prevent it from restarting until next boot, so no strategy can fully block that action. The best
 mitigation is to make it hard for the user to trigger a Force Stop or uninstall in the first place (see next
 section). 
• 
• 
Lock Task Mode (Kiosk mode): Not allowed here (requires device-owner) but worth noting: device
owner apps can pin the screen to an app. We can’t use that since we’re on a regular app. 
Hide app icon: You may optionally hide the parental app’s launcher icon (using 
PackageManager.setComponentEnabledSetting ) so the child cannot easily find and force-stop/
 uninstall it. You can briefly un-hide it when needed (e.g. after parent authentication). 
Preventing Disabling and Uninstallation
 A key defensive measure is to make it difficult for a child to disable or remove the blocker. Two main tools
 help here:
 • 
Device Administration API: Prompt the parent to grant the app “Device administrator” status. This
 forces the user to go through Settings → Security → Device Admin Apps to disable it before
 uninstalling. While it still can be disabled, this extra step can be detected. For instance, your app can
 register 
a DeviceAdminReceiver and in its 
onDisabled() 
callback 
call
 DevicePolicyManager.lockNow() to immediately lock the screen or require the parent PIN .
 (This effectively halts any further use until the parent intervenes.) The Android docs confirm a device
admin app can lock or even wipe the device on certain triggers . In practice you’d use something
 like: 
4
 4
 DevicePolicyManager dpm =
 (DevicePolicyManager)getSystemService(DEVICE_POLICY_SERVICE);
 ComponentName admin = new ComponentName(context,
 MyDeviceAdminReceiver.class);
 dpm.lockNow(); // locks the device
 3
You don’t need to wipe data (that’s extreme) – locking is enough to deter a persistent child. The
 DeviceAdminReceiver lets you react if admin is toggled off, although note some versions of Android
 may not always call your code on disable. Regardless, requiring Device Admin adds friction to
 uninstall attempts. 
• 
• 
Interdiction of Settings/Uninstall UI: Use the AccessibilityService itself to block access to
 Settings. For example, if the service sees 
com.android.settings in the foreground (or detects
 views like “Uninstall” or “Disable” buttons via 
AccessibilityNodeInfo ), it can overlay a PIN-entry
 screen or immediately navigate back to home. This prevents the child from easily flipping the switch
 on your service. Similarly, if your own package manager sees that someone is attempting to open
 your App Info, you could throw up a password-protected activity on top. In short, treat access to the
 Settings UI as another “blocked app” – intercept it and demand the parent passcode before allowing
 the child to continue. This is a partial workaround since determined users can still open Settings, but
 it greatly raises the barrier. 
Monitor permission changes: The app can periodically check (using 
AppOpsManager or
 AccessibilityManager ) whether its critical permissions are still enabled. If it finds
 AccessibilityService or Usage Access has been revoked, it can again cover the screen or prompt the
 user to re-enable them. Android provides an 
AccessibilityStateChangeListener to catch
 global accessibility toggles
 5
 , which you can use to re-prompt the parent or re-activate the service. 
Overall, complete prevention of uninstall is impossible without root or device owner, but these methods
 make it quite inconvenient for the child. For example, if the child attempts a standard uninstall from
 Settings, they first must disable admin (which we intercept or detect) and turn off the accessibility service
 (which our listener can catch and then lock the device or show an alert). In practice, most parental-control
 apps use exactly this approach: device admin for uninstall deterrence and a persistent overlay to block UI
 changes. 
Implementation Tips
 • 
• 
Foreground/Background Threads: Keep the monitoring (Accessibility and/or UsageStats) in a
 background Service. Do minimal work on the main thread. Use efficient checks (e.g. only query
 UsageStats every few seconds). 
Overlay design: Your blocking UI can be a simple full-screen 
Activity or a View added via
 WindowManager. Ensure the overlay consumes all clicks (
 onTouch should return 
true or
 intercept events) so the user can’t “click through”. By using 
FLAG_FULLSCREEN and no 
• 
public 
FLAG_NOT_FOCUSABLE , the overlay grabs input. 
Code structure: Organize your service like:
 ``` // Pseudo-structure of the accessibility-based blocker
 class 
AppBlockService 
extends 
AccessibilityService 
{ 
@Override 
public 
void
 onAccessibilityEvent(AccessibilityEvent e) { if (e.getEventType()==TYPE_WINDOW_STATE_CHANGED) { String
 pkg = e.getPackageName().toString(); if (blockedApps.contains(pkg)) { showBlockOverlay(); } else
 { removeOverlayIfPresent(); } } } @Override public void onInterrupt() { } @Override public void
 onServiceConnected() { / set up, maybe run as foreground service / }
 4
private void showBlockOverlay() { /* addView(TYPE_ACCESSIBILITY_OVERLAY) */ }
 private void removeOverlayIfPresent() { /* removeView if showing */ }
 } 
``- **Recovery on Reboot:** In BOOT_COMPLETED`, restart your service so the child can’t escape by
 rebooting.- Parent Authentication: When dismissing a block (for example, after a screen time limit is reached),
 require the parent’s password or PIN. This should bring up a secure dialog or activity. 
1
 4
 2
 In summary, the most reliable method on non-root devices is an AccessibilityService that immediately
 overlays any forbidden app . Back this up with UsageStats polling for redundancy . Harden the
 solution by running as a foreground/boot-start service, using DeviceAdmin to lock the device on tampering
 , and intercepting settings screens with additional overlays or password prompts. These combined
 strategies operate within Android’s security model (no root or exploits) yet provide a robust barrier against
 casual bypass attempts. With well-structured code (as outlined above) and careful permission handling, a
 parental-control app can effectively block or limit chosen apps while making it hard for a child to simply
 disable it.
 3
 Sources: Techniques are based on Android’s official docs and developer Q&A: for example,
 AccessibilityServices can catch foreground app changes and use 
1
 2
 TYPE_ACCESSIBILITY_OVERLAY to
 block them . Android’s UsageStatsManager (with 
3
 PACKAGE_USAGE_STATS ) can poll foreground apps
 . The Device Administration API can lock or wipe the device if needed . These together guide the
 implementation strategies described above. 
4
 1
 android - How to monitoring app swaping in foreground? - Stack Overflow
 https://stackoverflow.com/questions/10826579/how-to-monitoring-app-swaping-in-foreground
 2
 android - How does AccessibilityService draw on top of other apps? - Stack Overflow
 https://stackoverflow.com/questions/67063026/how-does-accessibilityservice-draw-on-top-of-other-apps
 3
 UsageStatsManager  |  API reference  |  Android Developers
 https://developer.android.com/reference/android/app/usage/UsageStatsManager
 4
 Device administration overview  |  Android Enterprise  |  Android Developers
 https://developer.android.com/work/device-admin
 5
 kotlin - How do you detect when user turns off accessibility service in Android? - Stack Overflow
 https://stackoverflow.com/questions/50130830/how-do-you-detect-when-user-turns-off-accessibility-service-in-android
 5