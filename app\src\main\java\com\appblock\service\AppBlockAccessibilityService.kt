package com.appblock.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.widget.FrameLayout
import androidx.core.app.NotificationCompat
// import com.appblock.R // Will be generated
import com.appblock.data.BlockedAppsManager
// import com.appblock.ui.BlockOverlayActivity // Removed for core functionality
import com.appblock.utils.PermissionUtils
import com.appblock.utils.BrowserUtils

class AppBlockAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "AppBlockAccessibility"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "app_block_service"

        @Volatile
        private var instance: AppBlockAccessibilityService? = null

        fun getInstance(): AppBlockAccessibilityService? = instance

        fun isServiceRunning(): Boolean = instance != null
    }

    private lateinit var windowManager: WindowManager
    private var overlayView: View? = null
    private lateinit var blockedAppsManager: BlockedAppsManager
    private var currentBlockedPackage: String? = null
    private val mainHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private var currentBlockedUrl: String? = null

    override fun onCreate() {
        super.onCreate()
        instance = this
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        blockedAppsManager = BlockedAppsManager(this)

        Log.d(TAG, "AppBlockAccessibilityService created")
        startForegroundService()
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "🚀 Accessibility service connected")

        // Configure service info with AGGRESSIVE monitoring
        val info = AccessibilityServiceInfo().apply {
            eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED or
                        AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED or
                        AccessibilityEvent.TYPE_WINDOWS_CHANGED or
                        AccessibilityEvent.TYPE_VIEW_FOCUSED or
                        AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
            flags = AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS or
                   AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                   AccessibilityServiceInfo.FLAG_REQUEST_ENHANCED_WEB_ACCESSIBILITY or
                   AccessibilityServiceInfo.FLAG_REQUEST_FILTER_KEY_EVENTS
            notificationTimeout = 50 // Faster response
        }
        serviceInfo = info

        Log.d(TAG, "✅ Accessibility service fully configured with AGGRESSIVE monitoring!")
        Log.d(TAG, "🎯 HARDCODED TARGET: com,pinterest")

        // Start continuous monitoring
        startContinuousMonitoring()
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let { accessibilityEvent ->
            when (accessibilityEvent.eventType) {
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                    handleWindowStateChanged(accessibilityEvent)
                }
                AccessibilityEvent.TYPE_WINDOWS_CHANGED -> {
                    Log.d(TAG, "🔄 WINDOWS CHANGED EVENT - Checking all windows")
                    handleWindowsChanged(accessibilityEvent)
                }
                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                    handleWindowContentChanged(accessibilityEvent)
                }
                AccessibilityEvent.TYPE_VIEW_FOCUSED -> {
                    handleViewFocused(accessibilityEvent)
                }
                AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED -> {
                    handleViewAccessibilityFocused(accessibilityEvent)
                }
            }
        }
    }

    private fun handleWindowStateChanged(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString()

        if (packageName.isNullOrEmpty()) {
            Log.d(TAG, "⚠️ Empty package name in window state change")
            return
        }

        Log.d(TAG, "🔍 Window state changed: $packageName")

        // Check if device is in split screen mode
        val isInSplitScreen = isInSplitScreenMode()
        if (isInSplitScreen) {
            Log.d(TAG, "📱 SPLIT SCREEN MODE DETECTED!")
        }

        // HARDCODED TEST LOGGING (keep existing)
        if (packageName == "com,pinterest") {
            Log.d(TAG, "🎯 EXPENDITURE TRACKER DETECTED! Checking if should block...")
            if (isInSplitScreen) {
                Log.d(TAG, "🚨 EXPENDITURE TRACKER IN SPLIT SCREEN - BLOCKING!")
            }
        }

        // Check if this is a browser app
        if (BrowserUtils.isBrowserApp(packageName)) {
            Log.d(TAG, "🌐 Browser detected: $packageName")
            handleBrowserAccess(packageName, event)
        } else if (shouldUseVpnFallback(packageName)) {
            Log.d(TAG, "📱 App with potential WebView detected: $packageName")
            // VPN service will handle network-level blocking for this app
            // We just log it here for awareness
        } else {
            // Regular app blocking logic
            val shouldBlock = blockedAppsManager.isAppBlocked(packageName)
            Log.d(TAG, "📊 Block check result for $packageName: $shouldBlock")

            if (shouldBlock) {
                Log.d(TAG, "🚨 BLOCKING APP IMMEDIATELY: $packageName")
                if (isInSplitScreen) {
                    Log.d(TAG, "🔄 BLOCKING IN SPLIT SCREEN MODE")
                    blockAppInSplitScreen(packageName)
                } else {
                    blockAppImmediately(packageName)
                }
            } else {
                Log.d(TAG, "✅ App $packageName is allowed")
                // Remove overlay if current app is not blocked
                if (currentBlockedPackage == packageName) {
                    Log.d(TAG, "App $packageName is no longer blocked, removing overlay")
                    removeOverlay()
                }
            }
        }

        // Special handling for Settings app to prevent tampering
        if (packageName == "com.android.settings") {
            handleSettingsAccess()
        }
    }

    /**
 * Handle browser access and check for blocked websites
 */
    private fun handleBrowserAccess(packageName: String, event: AccessibilityEvent) {
        try {
            Log.d(TAG, "🌐 Handling browser access for: $packageName")
            
            // Get the root node to extract URL
            val rootNode = event.source ?: rootInActiveWindow
            
            if (rootNode != null) {
                // Extract URL from browser
                val url = BrowserUtils.extractUrlFromBrowser(packageName, rootNode)
                
                if (url != null && url.isNotBlank()) {
                    Log.d(TAG, "🔗 URL detected in $packageName: $url")
                    
                    // Check if this URL should be blocked
                    val shouldBlockWebsite = blockedAppsManager.isWebsiteBlocked(url)
                    Log.d(TAG, "🌐 Website block check for $url: $shouldBlockWebsite")
                    
                    if (shouldBlockWebsite) {
                        Log.d(TAG, "🚨 BLOCKING WEBSITE: $url in $packageName")
                        blockWebsiteImmediately(packageName, url)
                    } else {
                        Log.d(TAG, "✅ Website $url is allowed")
                        // Remove overlay if current website is not blocked
                        if (currentBlockedUrl == url) {
                            Log.d(TAG, "Website $url is no longer blocked, removing overlay")
                            removeOverlay()
                        }
                    }
                } else {
                    Log.d(TAG, "⚠️ Could not extract URL from $packageName")
                }
            } else {
                Log.d(TAG, "⚠️ No root node available for $packageName")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling browser access for $packageName", e)
        }
    }

    /**
     * Check if we should use VPN blocking as fallback
     */
    private fun shouldUseVpnFallback(packageName: String): Boolean {
        // Use VPN blocking for apps that might have WebViews but aren't browsers
        val appsWithWebViews = setOf(
            "com.instagram.android",
            "com.zhiliaoapp.musically", // TikTok
            "com.facebook.katana",      // Facebook
            "com.twitter.android",      // Twitter/X
            "com.snapchat.android",     // Snapchat
            "com.whatsapp",            // WhatsApp
            "com.discord",             // Discord
            "com.reddit.frontpage"     // Reddit
        )
        
        return appsWithWebViews.contains(packageName) && 
            blockedAppsManager.isVpnBlockingEnabled()
    }

    /**
     * Block a website immediately
     */
    private fun blockWebsiteImmediately(packageName: String, url: String) {
        Log.d(TAG, "🚨 IMMEDIATELY BLOCKING WEBSITE: $url in $packageName")
        currentBlockedPackage = packageName
        currentBlockedUrl = url
        
        // Remove any existing overlay first
        Log.d(TAG, "🧹 Removing any existing overlay...")
        removeOverlay()
        
        // Create new overlay for website blocking
        Log.d(TAG, "🎨 Creating new website block overlay for: $url")
        showWebsiteBlockOverlay(packageName, url)
        
        Log.d(TAG, "✅ Website block sequence completed for: $url")
    }

    /**
     * Show website blocking overlay
     */
    private fun showWebsiteBlockOverlay(packageName: String, url: String) {
        try {
            Log.d(TAG, "Creating website block overlay for: $url")
            
            // Create overlay view with visible content
            val frameLayout = FrameLayout(this).apply {
                setBackgroundColor(android.graphics.Color.parseColor("#FFFF4444")) // Red background
            }
            
            // Add a text view to show it's blocked
            val textView = android.widget.TextView(this).apply {
                text = "🚫 WEBSITE BLOCKED 🚫\n\n$url\n\nThis website is not allowed\n\nTap to go home"
                textSize = 20f
                setTextColor(android.graphics.Color.WHITE)
                gravity = android.view.Gravity.CENTER
                setPadding(32, 32, 32, 32)
            }
            frameLayout.addView(textView)
            
            // Store reference
            overlayView = frameLayout
            
            // Configure window parameters for accessibility overlay
            val params = WindowManager.LayoutParams().apply {
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY
                }
                
                flags = WindowManager.LayoutParams.FLAG_FULLSCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
            }
            
            // Set up overlay interactions
            setupWebsiteOverlayInteractions(overlayView!!, url)
            
            // Add overlay to window manager
            windowManager.addView(overlayView!!, params)
            
            Log.d(TAG, "Website block overlay shown for: $url")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing website block overlay", e)
            // Fallback: just go to home
            goToHome()
        }
    }

    /**
     * Setup interactions for website blocking overlay
     */
    private fun setupWebsiteOverlayInteractions(overlayView: View, url: String) {
        // Handle touch events and detect taps
        overlayView.setOnTouchListener { _, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    Log.d(TAG, "Touch DOWN on blocked website: $url")
                }
                android.view.MotionEvent.ACTION_UP -> {
                    Log.d(TAG, "Touch UP on blocked website: $url - Going to home!")
                    goToHome()
                }
                else -> {
                    Log.d(TAG, "Touch intercepted on blocked website: $url")
                }
            }
            true // Consume all touch events
        }
        
        // Make sure overlay is focusable to intercept all input
        overlayView.isFocusable = true
        overlayView.isFocusableInTouchMode = true
        overlayView.requestFocus()
    }

    private fun handleWindowContentChanged(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString()

        // Monitor for attempts to disable our service or uninstall
        if (packageName == "com.android.settings") {
            // Check if user is trying to access accessibility settings or app info
            checkForTamperingAttempt(event)
        }
    }

    private fun blockAppImmediately(packageName: String) {
        Log.d(TAG, "🚨 IMMEDIATELY BLOCKING APP: $packageName")
        currentBlockedPackage = packageName

        // Remove any existing overlay first
        Log.d(TAG, "🧹 Removing any existing overlay...")
        removeOverlay()

        // Create new overlay immediately
        Log.d(TAG, "🎨 Creating new overlay for: $packageName")
        showBlockOverlay(packageName)

        Log.d(TAG, "✅ Block sequence completed for: $packageName")
    }

    private fun isInSplitScreenMode(): Boolean {
        return try {
            // Method 1: Check if we have multiple interactive windows
            val windows = windows
            if (windows != null && windows.size > 1) {
                Log.d(TAG, "📱 Multiple windows detected: ${windows.size}")
                return true
            }

            // Method 2: Check activity manager for multi-window mode
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val runningTasks = activityManager.getRunningTasks(10)

            var visibleTasks = 0
            for (task in runningTasks) {
                // Count tasks that might be visible
                if (task.numActivities > 0) {
                    visibleTasks++
                }
            }

            Log.d(TAG, "📊 Visible tasks: $visibleTasks")
            visibleTasks > 1

        } catch (e: Exception) {
            Log.e(TAG, "Error checking split screen mode", e)
            false
        }
    }

    private fun blockAppInSplitScreen(packageName: String) {
        Log.d(TAG, "🚨 BLOCKING APP IN SPLIT SCREEN: $packageName")

        // Strategy 1: Create overlay that covers entire screen (not just the split)
        currentBlockedPackage = packageName
        removeOverlay()
        showFullScreenBlockOverlay(packageName)

        // Strategy 2: Also try to exit split screen mode
        exitSplitScreenMode()
    }

    private fun showBlockOverlay(packageName: String) {
        try {
            Log.d(TAG, "Creating block overlay for: $packageName")

            // Create overlay view with visible content
            val frameLayout = FrameLayout(this).apply {
                setBackgroundColor(android.graphics.Color.parseColor("#FFFF0000")) // Bright red
            }

            // Add a text view to show it's blocked
            val textView = android.widget.TextView(this).apply {
                text = "🚫 APP BLOCKED 🚫\n\n$packageName\n\nTap to go home"
                textSize = 24f
                setTextColor(android.graphics.Color.WHITE)
                gravity = android.view.Gravity.CENTER
                setPadding(32, 32, 32, 32)
            }
            frameLayout.addView(textView)

            // Store reference
            overlayView = frameLayout

            // Configure window parameters for accessibility overlay
            val params = WindowManager.LayoutParams().apply {
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY
                }

                flags = WindowManager.LayoutParams.FLAG_FULLSCREEN or
                       WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                       WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                       WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL

                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
            }

            // Set up overlay interactions
            setupOverlayInteractions(overlayView!!, packageName)

            // Add overlay to window manager
            windowManager.addView(overlayView!!, params)

            Log.d(TAG, "Block overlay shown for: $packageName")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing block overlay", e)
            // Fallback: just go to home
            goToHome()
        }
    }

    private fun setupOverlayInteractions(overlayView: View, packageName: String) {
        // Handle touch events and detect taps
        overlayView.setOnTouchListener { _, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    Log.d(TAG, "Touch DOWN on blocked app: $packageName")
                }
                android.view.MotionEvent.ACTION_UP -> {
                    Log.d(TAG, "Touch UP on blocked app: $packageName - Going to home!")
                    goToHome()
                }
                else -> {
                    Log.d(TAG, "Touch intercepted on blocked app: $packageName")
                }
            }
            true // Consume all touch events
        }

        // Make sure overlay is focusable to intercept all input
        overlayView.isFocusable = true
        overlayView.isFocusableInTouchMode = true
        overlayView.requestFocus()
    }

    private fun showFullScreenBlockOverlay(packageName: String) {
        try {
            Log.d(TAG, "📱 Creating FULL SCREEN block overlay for split screen: $packageName")

            // Create overlay that covers ENTIRE screen, not just app window
            val frameLayout = FrameLayout(this).apply {
                setBackgroundColor(android.graphics.Color.parseColor("#FFFF0000")) // Bright red
            }

            // Add text for split screen blocking
            val textView = android.widget.TextView(this).apply {
                text = "🚫 APP BLOCKED IN SPLIT SCREEN 🚫\n\n$packageName\n\nSplit screen mode is not allowed for blocked apps\n\nTap to go home"
                textSize = 20f
                setTextColor(android.graphics.Color.WHITE)
                gravity = android.view.Gravity.CENTER
                setPadding(32, 32, 32, 32)
            }
            frameLayout.addView(textView)

            overlayView = frameLayout

            // Create layout params that cover ENTIRE screen
            val params = WindowManager.LayoutParams().apply {
                type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                flags = WindowManager.LayoutParams.FLAG_FULLSCREEN or
                       WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                       WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                       WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                       WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                format = android.graphics.PixelFormat.TRANSLUCENT
                // Force it to cover everything
                x = 0
                y = 0
            }

            // Set up interactions
            setupOverlayInteractions(overlayView!!, packageName)

            // Add overlay to window manager
            windowManager.addView(overlayView!!, params)

            Log.d(TAG, "✅ Full screen block overlay shown for split screen: $packageName")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error showing full screen block overlay for $packageName", e)
            // Fallback to regular overlay
            showBlockOverlay(packageName)
        }
    }

    private fun exitSplitScreenMode() {
        try {
            Log.d(TAG, "🔄 Attempting to exit split screen mode...")

            // Method 1: Send home intent to exit split screen
            val homeIntent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            startActivity(homeIntent)

            Log.d(TAG, "✅ Sent home intent to exit split screen")

        } catch (e: Exception) {
            Log.e(TAG, "Error exiting split screen mode", e)
        }
    }

    private fun startContinuousMonitoring() {
        Log.d(TAG, "🔍 Starting continuous monitoring for split screen bypass...")

        // Start a background thread to continuously check for blocked apps
        val monitoringThread = Thread {
            while (true) {
                try {
                    Thread.sleep(1000) // Check every second
                    checkAllVisibleWindows()
                } catch (e: InterruptedException) {
                    Log.d(TAG, "Monitoring thread interrupted")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "Error in continuous monitoring", e)
                }
            }
        }
        monitoringThread.isDaemon = true
        monitoringThread.start()
    }

    private fun checkAllVisibleWindows() {
        try {
            val allWindows = windows
            if (allWindows != null) {
                for (window in allWindows) {
                    val windowPackage = window.root?.packageName?.toString()
                    if (windowPackage != null && blockedAppsManager.isAppBlocked(windowPackage)) {
                        Log.d(TAG, "🚨 CONTINUOUS MONITORING: Found blocked app in window: $windowPackage")

                        // Block immediately
                        mainHandler.post {
                            blockAppImmediately(windowPackage)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Silently handle - this runs frequently
        }
    }

    private fun handleWindowsChanged(event: AccessibilityEvent) {
        Log.d(TAG, "🔄 Windows changed - scanning all windows for blocked apps")

        try {
            val allWindows = windows
            if (allWindows != null) {
                Log.d(TAG, "📊 Total windows: ${allWindows.size}")

                for (window in allWindows) {
                    val windowPackage = window.root?.packageName?.toString()
                    if (windowPackage != null) {
                        Log.d(TAG, "🔍 Window package: $windowPackage")

                        if (blockedAppsManager.isAppBlocked(windowPackage)) {
                            Log.d(TAG, "🚨 WINDOWS CHANGED: Found blocked app: $windowPackage")
                            blockAppImmediately(windowPackage)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling windows changed", e)
        }
    }

    private fun handleViewFocused(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString()
        if (packageName != null && blockedAppsManager.isAppBlocked(packageName)) {
            Log.d(TAG, "🎯 VIEW FOCUSED: Blocked app got focus: $packageName")
            blockAppImmediately(packageName)
        }
    }

    private fun handleViewAccessibilityFocused(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString()
        if (packageName != null && blockedAppsManager.isAppBlocked(packageName)) {
            Log.d(TAG, "🎯 ACCESSIBILITY FOCUSED: Blocked app got accessibility focus: $packageName")
            blockAppImmediately(packageName)
        }
    }

    // Removed launchBlockActivity - using overlay only

    // Removed PIN authentication - simplified for core functionality

    private fun goToHome() {
        val homeIntent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_HOME)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        startActivity(homeIntent)
        removeOverlay()
    }

    private fun removeOverlay() {
        overlayView?.let { view ->
            try {
                windowManager.removeView(view)
                overlayView = null
                currentBlockedPackage = null
                currentBlockedUrl = null  // ADD THIS LINE
                Log.d(TAG, "Block overlay removed")
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay", e)
            }
        }
    }

    private fun handleSettingsAccess() {
        // Check if parental controls are enabled for settings blocking
        if (blockedAppsManager.isSettingsBlocked()) {
            Log.d(TAG, "Blocking Settings access")
            blockAppImmediately("com.android.settings")
        }
    }

    private fun checkForTamperingAttempt(event: AccessibilityEvent) {
        // Monitor for specific UI elements that indicate tampering attempts
        val source = event.source
        source?.let { node ->
            // Check for accessibility settings or app info pages
            val text = node.text?.toString()?.lowercase()
            if (text?.contains("accessibility") == true ||
                text?.contains("app info") == true ||
                text?.contains("uninstall") == true) {

                Log.w(TAG, "Potential tampering attempt detected")
                // Could trigger additional security measures here
            }
        }
    }

    private fun startForegroundService() {
        createNotificationChannel()

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("App Block Active")
            .setContentText("Parental controls are monitoring app usage")
            .setSmallIcon(android.R.drawable.ic_lock_lock)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "App Block Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notification for app blocking service"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted")
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        removeOverlay()
        Log.d(TAG, "AppBlockAccessibilityService destroyed")
    }

    // Public methods for external control
    fun temporarilyAllowApp(packageName: String, durationMs: Long) {
        blockedAppsManager.temporarilyAllowApp(packageName, durationMs)
        if (currentBlockedPackage == packageName) {
            removeOverlay()
        }
    }

    fun isAppCurrentlyBlocked(): String? = currentBlockedPackage

    // Get currently blocked URL
    fun getCurrentlyBlockedUrl(): String? = currentBlockedUrl
}
