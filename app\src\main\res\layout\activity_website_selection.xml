<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🌐 Blocked Websites"
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- Search -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:startIconDrawable="@drawable/ic_search">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Search blocked websites..." />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Quick Categories -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Quick Add Categories:"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/spinner_categories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp" />

            <!-- Website List -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Currently Blocked:"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <ListView
                android:id="@+id/lv_websites"
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:layout_marginBottom="16dp" />

            <!-- Instructions -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="• Tap a website to remove it from blocked list\n• Use + button to add new websites\n• Select categories for quick bulk adding"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:gravity="center" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_website"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        app:srcCompat="@drawable/ic_add" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>