<variant
    name="debug"
    package="com.appblock"
    minSdkVersion="21"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    partialResultsDir="build\intermediates\unit_test_lint_partial_results\debug\lintAnalyzeDebugUnitTest\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e17c7a599d156460901212f59609c5\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\test\AndroidManifest.xml"
        javaDirectories="src\test\java;src\testDebug\java;src\test\kotlin;src\testDebug\kotlin"
        assetsDirectories="src\test\assets;src\testDebug\assets"
        unitTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="UNIT_TEST">
  </artifact>
</variant>
