package com.appblock.data

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class SettingsManager(private val context: Context) {

    companion object {
        private const val TAG = "SettingsManager"
        private const val PREFS_NAME = "app_block_settings"
        
        // Settings keys
        private const val KEY_APP_BLOCKING_ENABLED = "app_blocking_enabled"
        private const val KEY_DEVICE_ADMIN_ENABLED = "device_admin_enabled"
        private const val KEY_PARENT_PIN = "parent_pin"
        private const val KEY_LOCK_ON_TAMPERING = "lock_on_tampering"
        private const val KEY_HIDE_APP_ICON = "hide_app_icon"
        private const val KEY_FIRST_SETUP_COMPLETE = "first_setup_complete"
        
        // Logging keys
        private const val KEY_TAMPERING_ATTEMPTS = "tampering_attempts"
        private const val KEY_BOOT_EVENTS = "boot_events"
        private const val KEY_LAST_ACTIVITY = "last_activity"
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    // App blocking settings
    fun isAppBlockingEnabled(): Boolean {
        return prefs.getBoolean(KEY_APP_BLOCKING_ENABLED, false)
    }

    fun setAppBlockingEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_APP_BLOCKING_ENABLED, enabled).apply()
        Log.d(TAG, "App blocking enabled: $enabled")
    }

    // Device admin settings
    fun isDeviceAdminEnabled(): Boolean {
        return prefs.getBoolean(KEY_DEVICE_ADMIN_ENABLED, false)
    }

    fun setDeviceAdminEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_DEVICE_ADMIN_ENABLED, enabled).apply()
        Log.d(TAG, "Device admin enabled: $enabled")
    }

    // Parent PIN management
    fun setParentPin(pin: String) {
        val hashedPin = hashPin(pin)
        prefs.edit().putString(KEY_PARENT_PIN, hashedPin).apply()
        Log.d(TAG, "Parent PIN updated")
    }

    fun verifyParentPin(pin: String): Boolean {
        val storedHash = prefs.getString(KEY_PARENT_PIN, null)
        if (storedHash == null) {
            Log.w(TAG, "No parent PIN set")
            return false
        }
        
        val inputHash = hashPin(pin)
        return storedHash == inputHash
    }

    fun hasParentPin(): Boolean {
        return prefs.getString(KEY_PARENT_PIN, null) != null
    }

    private fun hashPin(pin: String): String {
        // Simple hash for demo - in production use proper hashing like bcrypt
        return pin.hashCode().toString()
    }

    // Tampering protection settings
    fun shouldLockOnTampering(): Boolean {
        return prefs.getBoolean(KEY_LOCK_ON_TAMPERING, true)
    }

    fun setLockOnTampering(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_LOCK_ON_TAMPERING, enabled).apply()
        Log.d(TAG, "Lock on tampering: $enabled")
    }

    // App icon visibility
    fun shouldHideAppIcon(): Boolean {
        return prefs.getBoolean(KEY_HIDE_APP_ICON, false)
    }

    fun setHideAppIcon(hide: Boolean) {
        prefs.edit().putBoolean(KEY_HIDE_APP_ICON, hide).apply()
        Log.d(TAG, "Hide app icon: $hide")
    }

    // Setup status
    fun isFirstSetupComplete(): Boolean {
        return prefs.getBoolean(KEY_FIRST_SETUP_COMPLETE, false)
    }

    fun setFirstSetupComplete(complete: Boolean) {
        prefs.edit().putBoolean(KEY_FIRST_SETUP_COMPLETE, complete).apply()
        Log.d(TAG, "First setup complete: $complete")
    }

    // Logging and monitoring
    fun recordTamperingAttempt(timestamp: Long) {
        val attempts = getTamperingAttempts().toMutableList()
        attempts.add(timestamp)
        
        // Keep only last 50 attempts
        if (attempts.size > 50) {
            attempts.removeAt(0)
        }
        
        val json = gson.toJson(attempts)
        prefs.edit().putString(KEY_TAMPERING_ATTEMPTS, json).apply()
        
        Log.w(TAG, "Recorded tampering attempt at $timestamp")
    }

    fun getTamperingAttempts(): List<Long> {
        val json = prefs.getString(KEY_TAMPERING_ATTEMPTS, "[]")
        return try {
            gson.fromJson(json, object : TypeToken<List<Long>>() {}.type) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing tampering attempts", e)
            emptyList()
        }
    }

    fun recordBootEvent(timestamp: Long) {
        val events = getBootEvents().toMutableList()
        events.add(timestamp)
        
        // Keep only last 20 boot events
        if (events.size > 20) {
            events.removeAt(0)
        }
        
        val json = gson.toJson(events)
        prefs.edit().putString(KEY_BOOT_EVENTS, json).apply()
        
        Log.d(TAG, "Recorded boot event at $timestamp")
    }

    fun getBootEvents(): List<Long> {
        val json = prefs.getString(KEY_BOOT_EVENTS, "[]")
        return try {
            gson.fromJson(json, object : TypeToken<List<Long>>() {}.type) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing boot events", e)
            emptyList()
        }
    }

    fun updateLastActivity() {
        prefs.edit().putLong(KEY_LAST_ACTIVITY, System.currentTimeMillis()).apply()
    }

    fun getLastActivity(): Long {
        return prefs.getLong(KEY_LAST_ACTIVITY, 0L)
    }

    // Statistics
    fun getTamperingAttemptsCount(): Int {
        return getTamperingAttempts().size
    }

    fun getRecentTamperingAttempts(hoursBack: Int): List<Long> {
        val cutoffTime = System.currentTimeMillis() - (hoursBack * 60 * 60 * 1000L)
        return getTamperingAttempts().filter { it > cutoffTime }
    }

    fun getBootEventsCount(): Int {
        return getBootEvents().size
    }

    // Reset and cleanup
    fun resetAllSettings() {
        prefs.edit().clear().apply()
        Log.d(TAG, "All settings reset")
    }

    fun exportSettings(): String {
        val allSettings = prefs.all
        return gson.toJson(allSettings)
    }

    fun importSettings(json: String): Boolean {
        return try {
            val settings: Map<String, Any> = gson.fromJson(
                json,
                object : TypeToken<Map<String, Any>>() {}.type
            )
            
            val editor = prefs.edit()
            settings.forEach { (key, value) ->
                when (value) {
                    is Boolean -> editor.putBoolean(key, value)
                    is String -> editor.putString(key, value)
                    is Long -> editor.putLong(key, value)
                    is Int -> editor.putInt(key, value)
                    is Float -> editor.putFloat(key, value)
                }
            }
            editor.apply()
            
            Log.d(TAG, "Settings imported successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error importing settings", e)
            false
        }
    }
}
