07-03 13:25:19.333 20179 20179 D AppBlockAccessibility: Window state changed: com.zhiliaoapp.musically
07-03 13:25:24.139 20179 20179 D AppBlockAccessibility: Window state changed: com.zhiliaoapp.musically
07-03 13:25:29.474  1842  3346 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070735} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (30 skipped)
07-03 13:25:39.669  1842  3364 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070804} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (43 skipped)
07-03 13:25:49.834  1842  3364 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070810} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (4 skipped)
07-03 13:26:00.977  1842  2412 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070815} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (4 skipped)
07-03 13:26:12.875  1842  2412 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070826} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (10 skipped)
07-03 13:26:24.407  1842  3364 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070834} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (6 skipped)
07-03 13:26:37.504  1842  2412 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070846} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (7 skipped)
07-03 13:26:49.038  1842  3337 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070884} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (16 skipped)
07-03 13:27:01.725  1842  3337 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070892} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (4 skipped)
07-03 13:27:14.142  1842  3806 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070894} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (1 skipped)
07-03 13:27:14.756 20179 20179 D AppBlockAccessibility: Window state changed: com.google.android.inputmethod.latin
07-03 13:27:23.860 20179 20179 D AppBlockAccessibility: Window state changed: com.zhiliaoapp.musically
07-03 13:27:26.383  1842  3802 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070938} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (39 skipped)
07-03 13:27:37.503  1842  3802 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070971} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (23 skipped)
07-03 13:27:48.343  1842  3862 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3070977} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (2 skipped)
07-03 13:27:54.714 20179 20179 D AppBlockAccessibility: Window state changed: com.zhiliaoapp.musically
07-03 13:27:55.403 20179 20179 D AppBlockAccessibility: Window state changed: com.google.android.inputmethod.latin
07-03 13:28:02.334  1842  3320 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071011} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (22 skipped)
07-03 13:28:15.246  1842  3360 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071025} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (6 skipped)
07-03 13:28:31.125 20179 20179 D AppBlockAccessibility: Window state changed: com.android.systemui
07-03 13:28:31.245 20179 20179 D AppBlockAccessibility: Window state changed: com.android.systemui
07-03 13:28:31.482 20179 20179 D AppBlockAccessibility: Window state changed: com.android.systemui
07-03 13:28:32.468  1842  3360 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071028} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (0 skipped)
07-03 13:28:32.975 20179 20179 D AppBlockAccessibility: Window state changed: com.android.systemui
07-03 13:28:36.120 20179 20179 D AppBlockAccessibility: Window state changed: com.zhiliaoapp.musically
07-03 13:28:47.525  1842  3360 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071076} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (20 skipped)
07-03 13:29:14.477  1842  1965 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071079} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (2 skipped)
07-03 13:29:14.563 12267 12267 I Finsky  : [2] VerifyApps: Install-time verification requested for package com.appblock, PackageManager id = 262, Verifier id = bd936e02-8bca-4203-b4fc-5ce369c255eb
07-03 13:29:14.752 12267 22854 I Finsky  : [4443] VerifyApps: Anti-malware verification task started for package=com.appblock
07-03 13:29:15.028 12267 12346 I Finsky  : [4263] VerifyApps: Starting APK Analysis scan for com.appblock.
07-03 13:29:15.029 12267 12346 I Finsky  : [4263] Scanning package com.appblock contents with client side protections. File: [zapXwWZeetRtkp6EytbKwDNLP6Fh_E2tUuxDJSUGdr0]
07-03 13:29:15.604 12267 22883 I Finsky  : [4447] VerifyApps: Verification package=com.appblock, id=262, response=0, upload_requested=false, run_post_install=false
07-03 13:29:15.606 12267 22883 I Finsky  : [4447] VerifyApps: PAM result saving to datastore delayed for package: com.appblock
07-03 13:29:17.194 20179 20179 D AppBlockAccessibility: Window state changed: com.android.systemui
07-03 13:29:17.520 12267 12346 I Finsky  : [4263] VerifyApps: APK Analysis scan finished for com.appblock. Verdict: SAFE
07-03 13:29:17.539 12267 22854 I Finsky  : [4443] VerifyApps: Starting required split types check for com.appblock.
07-03 13:29:17.544 12267 22854 I Finsky  : [4443] VerifyApps: Required split types check successful for com.appblock.
07-03 13:29:17.569 12267 22854 I Finsky  : [4443] VerifyApps V31SignatureVerification: Successful verification for the package: com.appblock using APK Signature Scheme v3
07-03 13:29:17.571 12267 12267 I Finsky  : [2] VerifyApps: Install-time verification complete: id=262, package_name=com.appblock
07-03 13:29:17.591  1842  1965 I ActivityManager: Force stopping com.appblock appid=10274 user=-1: installPackageLI
07-03 13:29:17.592  1842  1965 I ActivityManager: Killing 20566:com.appblock:monitor/u0a274 (adj 200): stop com.appblock due to installPackageLI
07-03 13:29:17.593  1842  2016 I PackageManager: Update package com.appblock code path from /data/app/~~s5kc8E7dvs5p3Bg-1UpUng==/com.appblock-fZNJO1P2Toi-JNoNpy-zqQ== to /data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==; Retain data and using new
07-03 13:29:17.595  1842  1965 I ActivityManager: Killing 20179:com.appblock/u0a274 (adj 100): stop com.appblock due to installPackageLI
07-03 13:29:17.757  1842  3843 V ActivityManager: Got obituary of 20566:com.appblock:monitor
07-03 13:29:17.757  1842  1864 V ActivityManager: Got obituary of 20179:com.appblock
07-03 13:29:17.786  2271  2353 D PeopleSpaceWidgetMgr: Sbn doesn't contain valid PeopleTileKey: null/0/com.appblock
07-03 13:29:17.804  2271  2353 D PeopleSpaceWidgetMgr: Sbn doesn't contain valid PeopleTileKey: null/0/com.appblock
07-03 13:29:17.867  1842  2016 I ActivityManager: Force stopping com.appblock appid=10274 user=0: pkg removed
07-03 13:29:17.897  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.appblock flg=0x4000010 (has extras) } to com.android.vending/com.google.android.finsky.packagemonitor.impl.PackageMonitorReceiverImpl$RegisteredReceiver
07-03 13:29:17.897  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.appblock flg=0x4000010 (has extras) } to com.android.vending/com.google.android.finsky.packagemonitor.backgroundimpl.BackgroundPackageMonitorReceiverImpl$RegisteredReceiver
07-03 13:29:17.901  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.appblock flg=0x4000010 (has extras) } to com.android.vending/com.google.android.finsky.instantapps.appmanagement.InstantAppRemoveMonitor
07-03 13:29:17.901  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.chimera.GmsIntentOperationService$PersistentTrustedReceiver
07-03 13:29:17.901  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.games.chimera.GamesSystemBroadcastReceiverProxy
07-03 13:29:17.901  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.videos/com.google.android.libraries.social.peoplekit.thirdparty.viewcontrollers.ThirdPartyReceiver
07-03 13:29:17.913  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.packageinstaller/com.android.packageinstaller.PackageInstalledReceiver
07-03 13:29:17.914  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.android.vending/com.google.android.finsky.packagemonitor.impl.PackageMonitorReceiverImpl$RegisteredReceiver
07-03 13:29:17.914  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.android.vending/com.google.android.finsky.packagemonitor.backgroundimpl.BackgroundPackageMonitorReceiverImpl$RegisteredReceiver
07-03 13:29:17.914  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.apps.nbu.files/.documentbrowser.browselanding.rootcontainerlist.receiver.PackageChangeReceiver_Receiver
07-03 13:29:17.922  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.ext.services/com.android.adservices.service.common.PackageChangedReceiver
07-03 13:29:17.936  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.chimera.GmsIntentOperationService$PersistentTrustedReceiver
07-03 13:29:17.954  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.games.chimera.GamesSystemBroadcastReceiverProxy
07-03 13:29:17.955  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.gass.chimera.PackageChangeBroadcastReceiver
07-03 13:29:17.956  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.facebook.katana/com.facebook.feed.platformads.AppInstallReceiver
07-03 13:29:17.956  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.appblock flg=0x4000010 (has extras) } to com.zhiliaoapp.musically/com.ss.android.common.applog.HotsoonReceiver
07-03 13:29:17.980  1842  1950 D CompanionDeviceManagerService: onPackageModified(packageName = com.appblock)
07-03 13:29:18.006  1842  1842 I Telecom : CarModeTracker: Package com.appblock is not tracked.: SSH.oR@1hQ
07-03 13:29:18.052 12267 12267 I Finsky  : [2] AIM: AppInfoCacheUpdater -> invalidating apps: [com.appblock]
07-03 13:29:18.066 12267 12323 I Finsky  : [4249] AIM: AppInfoManager-Perf > OnDeviceAppInfo > cacheHitCount=0, cacheMissCount=1. Missed  in cache (limit 10) : [com.appblock]
07-03 13:29:18.148 12267 12267 I Finsky  : [2] AIM: AppInfoCacheUpdater -> invalidating apps: [com.appblock]
07-03 13:29:18.168  2451  2451 D ImsResolver: maybeAddedImsService, packageName: com.appblock
07-03 13:29:18.256 12267 12336 I Finsky  : [4256] AIM: AppInfoManager-Perf > OnDeviceAppInfo > cacheHitCount=0, cacheMissCount=1. Missed  in cache (limit 10) : [com.appblock]
07-03 13:29:18.889  1842  2011 E VerityUtils: Failed to measure fs-verity, errno 1: /data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.apk
07-03 13:29:18.933 31047 22922 W SQLiteLog: (28) double-quoted string literal: "com.appblock"
07-03 13:29:18.949  1842  2011 E VerityUtils: Failed to measure fs-verity, errno 1: /data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.apk
07-03 13:29:22.493  1842  3862 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.appblock flg=0x4000010 (has extras) } to com.appblock/.receiver.BootReceiver
07-03 13:29:22.495  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.apps.nbu.files/.documentbrowser.browselanding.rootcontainerlist.receiver.PackageChangeReceiver_Receiver
07-03 13:29:22.495  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.chimera.GmsIntentOperationService$PersistentTrustedReceiver
07-03 13:29:22.495  1842  1965 W BroadcastQueue: Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.appblock flg=0x4000010 (has extras) } to com.google.android.gms/.gass.chimera.PackageChangeBroadcastReceiver
07-03 13:29:23.279  1842  1966 I ActivityManager: Start proc 23062:com.appblock/u0a274 for service {com.appblock/com.appblock.service.AppBlockAccessibilityService}
07-03 13:29:23.300 23062 23062 I com.appblock: Late-enabling -Xcheck:jni
07-03 13:29:23.683 23062 23062 I com.appblock: Using CollectorTypeCC GC.
07-03 13:29:23.687 23062 23062 W com.appblock: Mismatch between instruction set variant of device (ISA: Arm64 Feature string: -a53,crc,lse,fp16,dotprod,-sve) and features returned by the hardware (ISA: Arm64 Feature string: -a53,crc,lse,fp16,-dotprod,-sve)
07-03 13:29:23.834 23062 23062 W ziparchive: Unable to open '/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.dm': No such file or directory
07-03 13:29:23.834 23062 23062 W ziparchive: Unable to open '/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.dm': No such file or directory
07-03 13:29:24.194 23062 23062 D nativeloader: Configuring clns-4 for other apk /data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.appblock
07-03 13:29:24.218 23062 23062 V GraphicsEnvironment: ANGLE Developer option for 'com.appblock' set to: 'default'
07-03 13:29:24.218 23062 23062 V GraphicsEnvironment: ANGLE GameManagerService for com.appblock: false
07-03 13:29:24.397 23062 23062 D AppBlockAccessibility: AppBlockAccessibilityService created
07-03 13:29:24.430 23062 23062 D AppBlockAccessibility: ≡ƒÜÇ Accessibility service connected
07-03 13:29:24.432 23062 23062 D AppBlockAccessibility: Γ£à Accessibility service fully configured and ready to block apps!
07-03 13:29:24.432 23062 23062 D AppBlockAccessibility: ≡ƒÄ» HARDCODED TARGET: com.shulesoft.expenditure_tracker
07-03 13:29:25.664  1842  3346 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071226} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (12 skipped)
07-03 13:29:25.804  3266  3266 I AiAiEcho: AppFetcherImpl onPackageChanged com.appblock.
07-03 13:29:25.860  3266  3266 I AiAiEcho: AppIndexer Package:[com.appblock] UserProfile:[0] Enabled:[true].
07-03 13:29:25.860  3266  3266 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.appblock], userId:[0], reason:[package is updated.].
07-03 13:29:29.820 23062 23130 D ProfileInstaller: Installing profile for com.appblock
07-03 13:29:34.669  2271  2271 D InterruptionStateProvider: No bubble up: not allowed to bubble: 0|com.appblock|1001|null|10274
07-03 13:29:34.670  2271  2271 D InterruptionStateProvider: No heads up: unimportant notification: 0|com.appblock|1001|null|10274
07-03 13:29:34.670  2271  2353 D PeopleSpaceWidgetMgr: Sbn doesn't contain valid PeopleTileKey: null/0/com.appblock
07-03 13:29:35.668  1842  3360 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071338} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (102 skipped)
07-03 13:29:43.539 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:43.540 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:43.544 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:43.680 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.googlequicksearchbox
07-03 13:29:43.680 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.googlequicksearchbox: false
07-03 13:29:43.680 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.googlequicksearchbox is allowed
07-03 13:29:43.952 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:43.953 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:43.953 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:45.077 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:45.077 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:45.078 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:46.112  1842  3861 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071372} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (19 skipped)
07-03 13:29:48.596 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:48.596 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:48.597 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:49.196 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:49.196 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:49.196 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:49.754  1842  3360 I ActivityTaskManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.appblock/.MainActivity bnds=[439,645][640,946]} from uid 10186
07-03 13:29:50.111  1842  1966 I ActivityManager: Start proc 23195:com.appblock:monitor/u0a274 for service {com.appblock/com.appblock.service.UsageStatsMonitorService}
07-03 13:29:50.185 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.appblock
07-03 13:29:50.185 23062 23062 D BlockedAppsManager: ≡ƒôï Checking blocked status for com.appblock: false
07-03 13:29:50.185 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.appblock: false
07-03 13:29:50.185 23062 23062 D AppBlockAccessibility: Γ£à App com.appblock is allowed
07-03 13:29:50.319 23195 23195 W ziparchive: Unable to open '/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.dm': No such file or directory
07-03 13:29:50.320 23195 23195 W ziparchive: Unable to open '/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.dm': No such file or directory
07-03 13:29:50.812  1842  1950 W ziparchive: Unable to open '/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.dm': No such file or directory
07-03 13:29:50.816  1842  1950 I ActivityTaskManager: Displayed com.appblock/.MainActivity: +1s55ms
07-03 13:29:50.829 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.appblock
07-03 13:29:50.829 23062 23062 D BlockedAppsManager: ≡ƒôï Checking blocked status for com.appblock: false
07-03 13:29:50.829 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.appblock: false
07-03 13:29:50.829 23062 23062 D AppBlockAccessibility: Γ£à App com.appblock is allowed
07-03 13:29:50.830 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:50.830 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:50.830 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:50.830 23195 23195 D nativeloader: Configuring clns-4 for other apk /data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~ioyJbuFCk8-i_-VZ3vOdaw==/com.appblock-AEqrs7sshzfL2O1E1547GA==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.appblock
07-03 13:29:50.849 11786 11786 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1248 onStartInput(EditorInfo{EditorInfo{packageName=com.appblock, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=-1, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
07-03 13:29:50.884 23195 23195 V GraphicsEnvironment: ANGLE Developer option for 'com.appblock' set to: 'default'
07-03 13:29:50.886 23195 23195 V GraphicsEnvironment: ANGLE GameManagerService for com.appblock: false
07-03 13:29:51.014 23195 23224 D UsageStatsMonitor: Foreground app changed to: com.appblock
07-03 13:29:51.015 23195 23224 D BlockedAppsManager: ≡ƒôï Checking blocked status for com.appblock: false
07-03 13:29:51.197  1842  3360 W InputManager-JNI: Input channel object 'd03ba76 Splash Screen com.appblock (client)' was disposed without first being removed with the input manager!
07-03 13:29:51.233  2271  2271 D InterruptionStateProvider: No bubble up: not allowed to bubble: 0|com.appblock|1002|null|10274
07-03 13:29:51.234  2271  2271 D InterruptionStateProvider: No heads up: unimportant notification: 0|com.appblock|1002|null|10274
07-03 13:29:51.234  2271  2353 D PeopleSpaceWidgetMgr: Sbn doesn't contain valid PeopleTileKey: null/0/com.appblock
07-03 13:29:51.243  2271  2271 D InterruptionStateProvider: No bubble up: not allowed to bubble: 0|com.appblock|1002|null|10274
07-03 13:29:51.243  2271  2271 D InterruptionStateProvider: No heads up: unimportant notification: 0|com.appblock|1002|null|10274
07-03 13:29:51.244  2271  2353 D PeopleSpaceWidgetMgr: Sbn doesn't contain valid PeopleTileKey: null/0/com.appblock
07-03 13:29:54.220  1842  3862 W NotificationService: Toast already killed. pkg=com.appblock token=android.os.BinderProxy@a8deac5
07-03 13:29:55.559 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:55.559 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:55.559 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:56.206  1842  2215 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071413} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (10 skipped)
07-03 13:29:56.749 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:56.750 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:56.750 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:56.789  1842  3791 W NotificationService: Toast already killed. pkg=com.appblock token=android.os.BinderProxy@2568707
07-03 13:29:57.217 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:57.217 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:57.217 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:58.429 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.shulesoft.expenditure_tracker
07-03 13:29:58.430 23062 23062 D AppBlockAccessibility: ≡ƒÄ» EXPENDITURE TRACKER DETECTED! Checking if should block...
07-03 13:29:58.430 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.shulesoft.expenditure_tracker: true
07-03 13:29:58.430 23062 23062 D AppBlockAccessibility: ≡ƒÜ¿ BLOCKING APP IMMEDIATELY: com.shulesoft.expenditure_tracker
07-03 13:29:58.430 23062 23062 D AppBlockAccessibility: ≡ƒÜ¿ IMMEDIATELY BLOCKING APP: com.shulesoft.expenditure_tracker
07-03 13:29:58.430 23062 23062 D AppBlockAccessibility: ≡ƒº╣ Removing any existing overlay...
07-03 13:29:58.431 23062 23062 D AppBlockAccessibility: ≡ƒÄ¿ Creating new overlay for: com.shulesoft.expenditure_tracker
07-03 13:29:58.431 23062 23062 D AppBlockAccessibility: Creating block overlay for: com.shulesoft.expenditure_tracker
07-03 13:29:58.463 23062 23062 D AppBlockAccessibility: Block overlay shown for: com.shulesoft.expenditure_tracker
07-03 13:29:58.463 23062 23062 D AppBlockAccessibility: Γ£à Block sequence completed for: com.shulesoft.expenditure_tracker
07-03 13:29:58.667 11786 11786 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1248 onStartInput(EditorInfo{EditorInfo{packageName=com.appblock, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=-1, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
07-03 13:29:58.693 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.appblock
07-03 13:29:58.694 23062 23062 D BlockedAppsManager: ≡ƒôï Checking blocked status for com.appblock: false
07-03 13:29:58.694 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.appblock: false
07-03 13:29:58.694 23062 23062 D AppBlockAccessibility: Γ£à App com.appblock is allowed
07-03 13:29:58.997 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:29:58.998 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:29:58.998 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:29:59.368  1842  3360 W NotificationService: Toast already killed. pkg=com.appblock token=android.os.BinderProxy@ff514fb
07-03 13:30:01.256 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.google.android.apps.nexuslauncher
07-03 13:30:01.256 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.google.android.apps.nexuslauncher: false
07-03 13:30:01.256 23062 23062 D AppBlockAccessibility: Γ£à App com.google.android.apps.nexuslauncher is allowed
07-03 13:30:01.962  1842  3360 W NotificationService: Toast already killed. pkg=com.appblock token=android.os.BinderProxy@8926ebc
07-03 13:30:01.982 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:02.066 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:02.962 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:03.037 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.005 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.046 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.062 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.063  1842  3861 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [147e493 com.appblock (server), ]
07-03 13:30:06.064 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.672 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.713 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.729 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.730  1842  3346 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [147e493 com.appblock (server), ]
07-03 13:30:06.730 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:06.837  1842  1953 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071442} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (12 skipped)
07-03 13:30:07.307 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:07.349 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:07.350  1842  3360 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [147e493 com.appblock (server), ]
07-03 13:30:07.351 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:07.672 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.shulesoft.expenditure_tracker
07-03 13:30:07.673 23062 23062 D AppBlockAccessibility: ≡ƒÄ» EXPENDITURE TRACKER DETECTED! Checking if should block...
07-03 13:30:07.673 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.shulesoft.expenditure_tracker: true
07-03 13:30:07.673 23062 23062 D AppBlockAccessibility: ≡ƒÜ¿ BLOCKING APP IMMEDIATELY: com.shulesoft.expenditure_tracker
07-03 13:30:07.673 23062 23062 D AppBlockAccessibility: ≡ƒÜ¿ IMMEDIATELY BLOCKING APP: com.shulesoft.expenditure_tracker
07-03 13:30:07.673 23062 23062 D AppBlockAccessibility: ≡ƒº╣ Removing any existing overlay...
07-03 13:30:07.677 23062 23062 D AppBlockAccessibility: Block overlay removed
07-03 13:30:07.677 23062 23062 D AppBlockAccessibility: ≡ƒÄ¿ Creating new overlay for: com.shulesoft.expenditure_tracker
07-03 13:30:07.677 23062 23062 D AppBlockAccessibility: Creating block overlay for: com.shulesoft.expenditure_tracker
07-03 13:30:07.697 23062 23062 D AppBlockAccessibility: Block overlay shown for: com.shulesoft.expenditure_tracker
07-03 13:30:07.698 23062 23062 D AppBlockAccessibility: Γ£à Block sequence completed for: com.shulesoft.expenditure_tracker
07-03 13:30:07.702  1842  3791 W InputManager-JNI: Input channel object '147e493 com.appblock (client)' was disposed without first being removed with the input manager!
07-03 13:30:07.763 11786 11786 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1248 onStartInput(EditorInfo{EditorInfo{packageName=com.appblock, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=-1, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
07-03 13:30:07.833 23062 23062 D AppBlockAccessibility: ≡ƒöì Window state changed: com.appblock
07-03 13:30:07.834 23062 23062 D BlockedAppsManager: ≡ƒôï Checking blocked status for com.appblock: false
07-03 13:30:07.834 23062 23062 D AppBlockAccessibility: ≡ƒôè Block check result for com.appblock: false
07-03 13:30:07.834 23062 23062 D AppBlockAccessibility: Γ£à App com.appblock is allowed
07-03 13:30:08.554 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:08.602 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:08.602  1842  3360 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:08.603 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:10.083 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:10.140 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:10.140  1842  3791 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:10.141 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:15.722 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:15.764 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:15.765  1842  3861 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:15.766 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.368 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.398 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.399  1842  3346 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:16.399 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.849 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.867 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.884 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.884  1842  3346 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:16.884 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:16.969  1842  1953 W ProcessStats: Tracking association SourceState{b81d3bb system/1000 BTopFgs #3071461} whose proc state 2 is better than process ProcessState{26c50d8 com.appblock/10274 pkg=com.appblock} proc state 3 (17 skipped)
07-03 13:30:17.976 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:18.012 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:18.012  1842  3861 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:18.013 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:18.456 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:18.493 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:18.510 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:18.510  1842  2215 I InputDispatcher: Monitor swipe-up (server) is stealing touch from [c70c1f2 com.appblock (server), ]
07-03 13:30:18.511 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:19.437 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:19.465 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:19.521 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:19.972 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.040 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.149 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.238 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.532 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.619 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.767 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:20.848 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.036 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.109 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.268 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.340 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.541 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.557 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.591 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:21.605 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:22.467 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:22.545 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:23.062 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker
07-03 13:30:23.153 23062 23062 D AppBlockAccessibility: Touch intercepted on blocked app: com.shulesoft.expenditure_tracker