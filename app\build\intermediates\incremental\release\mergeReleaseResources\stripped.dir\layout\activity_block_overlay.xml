<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/block_overlay_background"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="32dp"
        android:background="@drawable/block_card_background"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- App Icon and Name -->
        <ImageView
            android:id="@+id/iv_app_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp"
            android:src="@drawable/ic_block" />

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="8dp"
            android:text="App Name"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Block Message -->
        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="@string/app_blocked_message"
            android:textSize="16sp" />

        <!-- Remaining Time (if applicable) -->
        <TextView
            android:id="@+id/tv_remaining_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:textColor="@color/warning_orange"
            android:textSize="14sp"
            android:visibility="gone" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_enter_pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/enter_pin"
                app:icon="@drawable/ic_lock" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_request_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/request_time"
                android:visibility="gone"
                app:icon="@drawable/ic_access_time" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_go_home"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/go_home"
                app:icon="@drawable/ic_home" />

        </LinearLayout>

        <!-- Source Info (for debugging) -->
        <TextView
            android:id="@+id/tv_source"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:text="Detected by: unknown"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            android:visibility="gone" />

    </LinearLayout>

</FrameLayout>
