<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_app" modulePackage="com.appblock" filePath="app\src\main\res\layout\item_app.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_app_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="14"/></Target><Target id="@+id/iv_app_icon" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="17" endOffset="44"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="42"/></Target><Target id="@+id/tv_system_app" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="51" endOffset="43"/></Target><Target id="@+id/tv_package_name" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="62" endOffset="37"/></Target><Target id="@+id/checkbox_blocked" view="CheckBox"><Expressions/><location startLine="67" startOffset="4" endLine="72" endOffset="42"/></Target></Targets></Layout>