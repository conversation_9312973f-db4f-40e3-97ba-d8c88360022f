<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="accent">#FFFF5722</color>
    <color name="black">#FF000000</color>
    <color name="block_card_background">#FFFFFF</color>
    <color name="block_overlay_background">#E6000000</color>
    <color name="blocked_app_background">#FFEBEE</color>
    <color name="error_red">#FFF44336</color>
    <color name="pin_button_background">#FFF5F5F5</color>
    <color name="pin_button_pressed">#FFE0E0E0</color>
    <color name="primary">#FF2196F3</color>
    <color name="primary_dark">#FF1976D2</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success_green">#FF4CAF50</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning_orange">#FFFF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="accessibility_service_description">App Block uses this service to monitor which apps are opened and block restricted apps by showing an overlay screen. This service does not collect or store any personal data.</string>
    <string name="action_about">About</string>
    <string name="action_blocked_count">Blocked Count</string>
    <string name="action_deselect_all">Deselect All</string>
    <string name="action_logs">Logs</string>
    <string name="action_search">Search</string>
    <string name="action_select_all">Select All</string>
    <string name="app_blocked_message">This app is currently blocked by parental controls</string>
    <string name="app_blocked_title">App Blocked</string>
    <string name="app_blocking">App Blocking</string>
    <string name="app_name">App Block</string>
    <string name="blocked_apps">Blocked Apps</string>
    <string name="cancel">Cancel</string>
    <string name="clear">Clear</string>
    <string name="delete">Delete</string>
    <string name="device_admin">Device Admin</string>
    <string name="disable">Disable</string>
    <string name="enable">Enable</string>
    <string name="enter_parent_pin">Enter Parent PIN</string>
    <string name="enter_pin">Enter PIN</string>
    <string name="go_home">Go Home</string>
    <string name="no">No</string>
    <string name="notification_text">Parental controls are monitoring app usage</string>
    <string name="notification_title">App Block Active</string>
    <string name="ok">OK</string>
    <string name="permissions">Permissions</string>
    <string name="request_time">Request Time</string>
    <string name="save">Save</string>
    <string name="search_apps">Search apps...</string>
    <string name="select_apps_to_block">Select Apps to Block</string>
    <string name="settings">Settings</string>
    <string name="setup_pin">Setup PIN</string>
    <string name="show_all">All</string>
    <string name="show_system_apps">System</string>
    <string name="show_user_apps">User</string>
    <string name="verify">Verify</string>
    <string name="yes">Yes</string>
    <style name="PinButtonStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">60dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:background">@drawable/pin_button_background</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="Theme.AppBlock" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="Theme.AppBlock.Dialog" parent="Theme.Material3.DayNight.Dialog">
        <item name="colorPrimary">@color/primary</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="Theme.AppBlock.Fullscreen" parent="Theme.AppBlock">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/block_overlay_background</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>
</resources>