<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.2" type="partial_results">
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.MAIN (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
                            line="361"
                            column="30"
                            startOffset="14375"
                            endLine="361"
                            endColumn="56"
                            endOffset="14401"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
                            line="462"
                            column="26"
                            startOffset="18231"
                            endLine="462"
                            endColumn="52"
                            endOffset="18257"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/UsageStatsMonitorService.kt"
                            line="149"
                            column="30"
                            startOffset="5325"
                            endLine="149"
                            endColumn="56"
                            endOffset="5351"/>
                        <location id="3"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
                            line="140"
                            column="26"
                            startOffset="5294"
                            endLine="140"
                            endColumn="52"
                            endOffset="5320"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.appblock.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.appblock.receiver.BootReceiver"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="508"
            endLine="14"
            endColumn="25"
            endOffset="521"/>
        <location id="R.color.error_red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="897"
            endLine="24"
            endColumn="28"
            endOffset="913"/>
        <location id="R.color.primary_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="459"
            endLine="13"
            endColumn="31"
            endOffset="478"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="29"
            endOffset="79"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="109"
            endLine="4"
            endColumn="29"
            endOffset="126"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="29"
            endOffset="173"/>
        <location id="R.color.success_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="796"
            endLine="22"
            endColumn="32"
            endOffset="816"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="203"
            endLine="6"
            endColumn="27"
            endOffset="218"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="248"
            endLine="7"
            endColumn="27"
            endOffset="263"/>
        <location id="R.drawable.ic_monitor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_monitor.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="489"/>
        <location id="R.drawable.ic_shield"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_shield.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="644"/>
        <location id="R.layout.block_overlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/block_overlay.xml"
            line="3"
            column="1"
            startOffset="104"
            endLine="75"
            endColumn="15"
            endOffset="2821"/>
        <location id="R.string.app_blocked_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="780"
            endLine="21"
            endColumn="37"
            endOffset="804"/>
        <location id="R.string.app_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="63"
            endLine="3"
            endColumn="28"
            endOffset="78"/>
        <location id="R.string.delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2366"
            endLine="55"
            endColumn="26"
            endOffset="2379"/>
        <location id="R.string.disable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2284"
            endLine="53"
            endColumn="27"
            endOffset="2298"/>
        <location id="R.string.enable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="2242"
            endLine="52"
            endColumn="26"
            endOffset="2255"/>
        <location id="R.string.no"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2208"
            endLine="51"
            endColumn="22"
            endOffset="2217"/>
        <location id="R.string.notification_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="1679"
            endLine="38"
            endColumn="37"
            endOffset="1703"/>
        <location id="R.string.notification_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1615"
            endLine="37"
            endColumn="38"
            endOffset="1640"/>
        <location id="R.string.ok"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="2138"
            endLine="49"
            endColumn="22"
            endOffset="2147"/>
        <location id="R.string.save"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="2328"
            endLine="54"
            endColumn="24"
            endOffset="2339"/>
        <location id="R.string.search_apps"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="693"
            endLine="18"
            endColumn="31"
            endOffset="711"/>
        <location id="R.string.select_apps_to_block"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="482"
            endLine="14"
            endColumn="40"
            endOffset="509"/>
        <location id="R.string.yes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="2172"
            endLine="50"
            endColumn="23"
            endOffset="2182"/>
        <location id="R.style.Theme_AppBlock"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="4"
            column="12"
            startOffset="146"
            endLine="4"
            endColumn="33"
            endOffset="167"/>
        <location id="R.style.Theme_AppBlock_Dialog"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="28"
            column="12"
            startOffset="1356"
            endLine="28"
            endColumn="40"
            endOffset="1384"/>
        <location id="R.style.Theme_AppBlock_Fullscreen"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="19"
            column="12"
            startOffset="897"
            endLine="19"
            endColumn="44"
            endOffset="929"/>
        <location id="R.xml.backup_rules"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="23"
            endOffset="180"/>
        <location id="R.xml.data_extraction_rules"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="25"
            endOffset="381"/>
        <entry
            name="model"
            string="attr[colorOnPrimary(R),colorPrimary(R),actionBarSize(R),colorPrimaryVariant(E)],color[block_card_background(U),primary(U),pin_button_pressed(U),pin_button_background(U),warning_orange(U),white(U),block_overlay_background(U),error_red(D),purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),black(U),primary_dark(D),accent(D),blocked_app_background(U),success_green(D)],drawable[block_card_background(U),ic_access_time(U),ic_admin_panel_settings(U),ic_android(U),ic_block(U),ic_deselect_all(U),ic_home(U),ic_info(U),ic_list(U),ic_lock(U),ic_monitor(D),ic_search(U),ic_security(U),ic_select_all(U),ic_settings(U),ic_shield(D),pin_button_background(U),system_app_badge(U)],id[toolbar(U),btn_show_all(D),btn_show_user_apps(D),btn_show_system_apps(D),tv_app_count(D),progress_bar(D),recycler_view(D),iv_app_icon(D),tv_app_name(D),tv_message(D),tv_remaining_time(D),btn_enter_pin(D),btn_request_time(D),btn_go_home(D),tv_source(D),switch_app_blocking(D),card_permissions(D),tv_permission_status(D),card_blocked_apps(D),tv_blocked_apps_count(D),card_settings(D),btn_setup_pin(D),btn_device_admin(D),tv_title(D),et_pin(U),btn_1(D),btn_2(D),btn_3(D),btn_4(D),btn_5(D),btn_6(D),btn_7(D),btn_8(D),btn_9(D),btn_clear(D),btn_0(D),btn_backspace(D),btn_cancel(D),btn_verify(D),et_confirm_pin(U),tv_system_app(D),tv_package_name(D),checkbox_blocked(D),action_search(U),action_select_all(U),action_deselect_all(U),action_blocked_count(U),action_logs(U),action_about(U)],layout[activity_app_selection(U),activity_block_overlay(U),activity_main(U),activity_pin_auth(U),block_overlay(D),dialog_pin_setup(U),dialog_pin_verify(U),item_app(U)],menu[app_selection_menu(U),main_menu(U)],string[appbar_scrolling_view_behavior(R),show_all(U),show_user_apps(U),show_system_apps(U),app_blocked_message(U),enter_pin(U),request_time(U),go_home(U),app_blocking(U),permissions(U),blocked_apps(U),settings(U),setup_pin(U),device_admin(U),enter_parent_pin(U),clear(U),cancel(U),verify(U),app_blocked_title(D),action_search(U),action_select_all(U),action_deselect_all(U),action_blocked_count(U),action_logs(U),action_about(U),app_name(D),select_apps_to_block(D),search_apps(D),accessibility_service_description(U),notification_title(D),notification_text(D),ok(D),yes(D),no(D),enable(D),disable(D),save(D),delete(D)],style[ThemeOverlay_AppCompat_Dark_ActionBar(R),ThemeOverlay_AppCompat_Light(R),Widget_Material3_Button_TextButton(R),PinButtonStyle(U),Theme_AppBlock(D),Theme_Material3_DayNight(E),Theme_AppBlock_Fullscreen(D),Theme_AppBlock_Dialog(D),Theme_Material3_DayNight_Dialog(E)],xml[accessibility_service_config(U),device_admin(U),backup_rules(D),data_extraction_rules(D)];16^4^5,17^0,18^0,19^1,1a^1,1b^0,1c^0,1d^0,1e^0,1f^0,20^0,21^0,22^1,23^0,24^1,25^0,26^6^7,27^8,59^89^1^2^8a^63^9^64^8b^65^66,5a^a^16^1a^67^8^68^1f^69^17^6a^1c^8b,5b^89^1^2^8a^63^6b^22^6c^1a^6d^24^6e^6f^1f^70^18,5c^9^71^5^8c^72^73^8b^74,5d^a^16^1a^b^75^67^68^1f^6a^1c^8b,5e^5,5f^5,60^19^27^9,61^21^76^23^77^1b^78^1d^79,62^1e^7a^1d^7b,8c^26^11,8d^8e^5^12^9^f^10^11^3,8f^8d^a,90^91^5,92^7f;;;"/>
    </map>

</incidents>
