package com.appblock.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.appblock.R
import com.appblock.data.BlockedAppsManager
import com.appblock.data.SettingsManager
import com.appblock.databinding.ActivityMainBinding
import com.appblock.service.UsageStatsMonitorService
import com.appblock.utils.PermissionUtils

class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
        private const val REQUEST_DEVICE_ADMIN = 1001
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var settingsManager: SettingsManager
    private lateinit var blockedAppsManager: BlockedAppsManager

    private val deviceAdminLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            settingsManager.setDeviceAdminEnabled(true)
            Toast.makeText(this, "Device administrator enabled", Toast.LENGTH_SHORT).show()
            updateUI()
        } else {
            Toast.makeText(this, "Device administrator is required for full protection", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        settingsManager = SettingsManager(this)
        blockedAppsManager = BlockedAppsManager(this)

        setupUI()
        checkFirstTimeSetup()
        updateUI()
    }

    private fun setupUI() {
        setSupportActionBar(binding.toolbar)

        // Setup click listeners
        binding.cardPermissions.setOnClickListener {
            showPermissionsDialog()
        }

        binding.cardBlockedApps.setOnClickListener {
            startActivity(Intent(this, AppSelectionActivity::class.java))
        }

        binding.cardSettings.setOnClickListener {
            // Open settings activity (to be implemented)
            Toast.makeText(this, "Settings coming soon", Toast.LENGTH_SHORT).show()
        }

        binding.switchAppBlocking.setOnCheckedChangeListener { _, isChecked ->
            toggleAppBlocking(isChecked)
        }

        binding.btnSetupPin.setOnClickListener {
            showPinSetupDialog()
        }

        binding.btnDeviceAdmin.setOnClickListener {
            requestDeviceAdmin()
        }
    }

    private fun checkFirstTimeSetup() {
        if (!settingsManager.isFirstSetupComplete()) {
            showFirstTimeSetupDialog()
        }
    }

    private fun showFirstTimeSetupDialog() {
        AlertDialog.Builder(this)
            .setTitle("Welcome to App Block")
            .setMessage("This app helps parents control which apps children can use. To work effectively, it needs several permissions. Would you like to set it up now?")
            .setPositiveButton("Setup Now") { _, _ ->
                showPermissionsDialog()
            }
            .setNegativeButton("Later") { _, _ ->
                // User can set up later
            }
            .setCancelable(false)
            .show()
    }

    private fun showPermissionsDialog() {
        val permissions = PermissionUtils.getMissingPermissions(this)

        if (permissions.isEmpty()) {
            Toast.makeText(this, "All permissions are granted!", Toast.LENGTH_SHORT).show()
            return
        }

        val message = "The following permissions are required:\n\n" +
                permissions.joinToString("\n") { "• $it" } +
                "\n\nWould you like to grant them now?"

        AlertDialog.Builder(this)
            .setTitle("Required Permissions")
            .setMessage(message)
            .setPositiveButton("Grant Permissions") { _, _ ->
                requestMissingPermissions()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun requestMissingPermissions() {
        // Request Usage Stats permission
        if (!PermissionUtils.hasUsageStatsPermission(this)) {
            try {
                startActivity(PermissionUtils.openUsageStatsSettings(this))
                Toast.makeText(this, "Please enable Usage Access for App Block", Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Log.e(TAG, "Error opening usage stats settings", e)
            }
        }

        // Request Accessibility Service
        if (!PermissionUtils.isAccessibilityServiceEnabled(this)) {
            try {
                startActivity(PermissionUtils.openAccessibilitySettings(this))
                Toast.makeText(this, "Please enable App Block Accessibility Service", Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Log.e(TAG, "Error opening accessibility settings", e)
            }
        }

        // Request Overlay permission
        if (!PermissionUtils.canDrawOverlays(this)) {
            try {
                startActivity(PermissionUtils.openOverlaySettings(this))
                Toast.makeText(this, "Please allow App Block to display over other apps", Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Log.e(TAG, "Error opening overlay settings", e)
            }
        }
    }

    private fun requestDeviceAdmin() {
        if (PermissionUtils.isDeviceAdminActive(this)) {
            Toast.makeText(this, "Device administrator is already enabled", Toast.LENGTH_SHORT).show()
            return
        }

        AlertDialog.Builder(this)
            .setTitle("Enable Device Administrator")
            .setMessage("Device administrator permission helps protect the app from being easily uninstalled or disabled. This adds an extra layer of security for parental controls.")
            .setPositiveButton("Enable") { _, _ ->
                val intent = PermissionUtils.requestDeviceAdmin(this)
                deviceAdminLauncher.launch(intent)
            }
            .setNegativeButton("Skip", null)
            .show()
    }

    private fun showPinSetupDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_pin_setup, null)
        val pinInput = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.et_pin)
        val confirmPinInput = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.et_confirm_pin)

        AlertDialog.Builder(this)
            .setTitle("Set Parent PIN")
            .setView(dialogView)
            .setPositiveButton("Set PIN") { _, _ ->
                val pin = pinInput.text.toString()
                val confirmPin = confirmPinInput.text.toString()

                if (pin.length < 4) {
                    Toast.makeText(this, "PIN must be at least 4 digits", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                if (pin != confirmPin) {
                    Toast.makeText(this, "PINs do not match", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                settingsManager.setParentPin(pin)
                Toast.makeText(this, "Parent PIN set successfully", Toast.LENGTH_SHORT).show()
                updateUI()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun toggleAppBlocking(enabled: Boolean) {
        if (enabled) {
            // Check if all required permissions are granted
            if (!PermissionUtils.hasAllCriticalPermissions(this)) {
                binding.switchAppBlocking.isChecked = false
                showPermissionsDialog()
                return
            }

            // Check if PIN is set
            if (!settingsManager.hasParentPin()) {
                binding.switchAppBlocking.isChecked = false
                Toast.makeText(this, "Please set a parent PIN first", Toast.LENGTH_SHORT).show()
                showPinSetupDialog()
                return
            }

            // Enable app blocking
            settingsManager.setAppBlockingEnabled(true)
            startMonitoringServices()
            Toast.makeText(this, "App blocking enabled", Toast.LENGTH_SHORT).show()

        } else {
            // Disable app blocking (require PIN)
            if (settingsManager.hasParentPin()) {
                showPinVerificationDialog {
                    settingsManager.setAppBlockingEnabled(false)
                    stopMonitoringServices()
                    Toast.makeText(this, "App blocking disabled", Toast.LENGTH_SHORT).show()
                }
            } else {
                settingsManager.setAppBlockingEnabled(false)
                stopMonitoringServices()
            }
        }

        updateUI()
    }

    private fun showPinVerificationDialog(onSuccess: () -> Unit) {
        val dialogView = layoutInflater.inflate(R.layout.dialog_pin_verify, null)
        val pinInput = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.et_pin)

        AlertDialog.Builder(this)
            .setTitle("Enter Parent PIN")
            .setView(dialogView)
            .setPositiveButton("Verify") { _, _ ->
                val pin = pinInput.text.toString()
                if (settingsManager.verifyParentPin(pin)) {
                    onSuccess()
                } else {
                    Toast.makeText(this, "Incorrect PIN", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel") { _, _ ->
                // Reset switch state
                binding.switchAppBlocking.isChecked = settingsManager.isAppBlockingEnabled()
            }
            .setCancelable(false)
            .show()
    }

    private fun startMonitoringServices() {
        // Start Usage Stats monitoring service
        val serviceIntent = Intent(this, UsageStatsMonitorService::class.java)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }

        Log.d(TAG, "Started monitoring services")
    }

    private fun stopMonitoringServices() {
        // Stop Usage Stats monitoring service
        val serviceIntent = Intent(this, UsageStatsMonitorService::class.java)
        stopService(serviceIntent)

        Log.d(TAG, "Stopped monitoring services")
    }

    private fun updateUI() {
        // Update permission status
        val permissions = PermissionUtils.getPermissionStatusSummary(this)
        val allGranted = permissions.values.all { it }

        binding.tvPermissionStatus.text = if (allGranted) {
            "All permissions granted ✓"
        } else {
            "Missing permissions (${permissions.values.count { !it }})"
        }

        // Update blocked apps count
        val blockedAppsCount = blockedAppsManager.getBlockedApps().size
        binding.tvBlockedAppsCount.text = "$blockedAppsCount apps blocked"

        // Update app blocking switch
        binding.switchAppBlocking.isChecked = settingsManager.isAppBlockingEnabled()

        // Update PIN status
        binding.btnSetupPin.text = if (settingsManager.hasParentPin()) {
            "Change PIN"
        } else {
            "Set PIN"
        }

        // Update device admin status
        binding.btnDeviceAdmin.text = if (PermissionUtils.isDeviceAdminActive(this)) {
            "Device Admin: Enabled ✓"
        } else {
            "Enable Device Admin"
        }

        binding.btnDeviceAdmin.isEnabled = !PermissionUtils.isDeviceAdminActive(this)
    }

    override fun onResume() {
        super.onResume()
        updateUI()
        settingsManager.updateLastActivity()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_logs -> {
                // Show logs activity
                Toast.makeText(this, "Logs coming soon", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_about -> {
                showAboutDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showAboutDialog() {
        AlertDialog.Builder(this)
            .setTitle("About App Block")
            .setMessage("App Block v1.0\n\nA parental control app that helps manage children's app usage using Android's accessibility and usage stats APIs.\n\nDeveloped following Android security best practices.")
            .setPositiveButton("OK", null)
            .show()
    }
}
