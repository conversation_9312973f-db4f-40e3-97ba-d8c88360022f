C:\Users\<USER>\Documents\augment-projects\appblock\app\lint-baseline.xml: Information: 4 errors and 118 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml [LintBaseline]
C:\Users\<USER>\Documents\augment-projects\appblock\app\lint-baseline.xml: Information: 1 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with android.lintOptions.checkDependencies=true. Unmatched issue types: SwitchIntDef [LintBaselineFixed]

   Explanation for issues of type "LintBaselineFixed":
   If a lint baseline describes a problem which is no longer reported, then
   the problem has either been fixed, or perhaps the issue type has been
   disabled. In any case, the entry can be removed from the baseline (such
   that if the issue is reintroduced at some point, lint will complain rather
   than just silently starting to match the old baseline entry again.)

C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:327: Warning: Field requires API level 22 (current min is 21): android.view.WindowManager.LayoutParams#TYPE_ACCESSIBILITY_OVERLAY [InlinedApi]
                type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InlinedApi":
   This check scans through all the Android API field references in the
   application and flags certain constants, such as static final integers and
   Strings, which were introduced in later versions. These will actually be
   copied into the class files rather than being referenced, which means that
   the value is available even when running on older devices. In some cases
   that's fine, and in other cases it can result in a runtime crash or
   incorrect behavior. It depends on the context, so consider the code
   carefully and decide whether it's safe and can be suppressed or whether the
   code needs to be guarded.

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:84: Warning: Switch statement on an int with known associated constant missing case AccessibilityEvent.TYPE_ANNOUNCEMENT, AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT, AccessibilityEvent.TYPE_GESTURE_DETECTION_END, AccessibilityEvent.TYPE_GESTURE_DETECTION_START, AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED, AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE, AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END, AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START, AccessibilityEvent.TYPE_TOUCH_INTERACTION_END, AccessibilityEvent.TYPE_TOUCH_INTERACTION_START, AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED, AccessibilityEvent.TYPE_VIEW_CLICKED, AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED, AccessibilityEvent.TYPE_VIEW_HOVER_ENTER, AccessibilityEvent.TYPE_VIEW_HOVER_EXIT, AccessibilityEvent.TYPE_VIEW_LONG_CLICKED, AccessibilityEvent.TYPE_VIEW_SCROLLED, AccessibilityEvent.TYPE_VIEW_SELECTED, AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL, AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED, AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED, AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY [SwitchIntDef]
            when (accessibilityEvent.eventType) {
            ~~~~

   Explanation for issues of type "SwitchIntDef":
   This check warns if a switch statement does not explicitly include all the
   values declared by the typedef @IntDef declaration.

C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:234: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                text = "🚫 APP BLOCKED 🚫\n\n$packageName\n\nTap to go home"
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:234: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                text = "🚫 APP BLOCKED 🚫\n\n$packageName\n\nTap to go home"
                        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:234: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                text = "🚫 APP BLOCKED 🚫\n\n$packageName\n\nTap to go home"
                                                             ~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:315: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                text = "🚫 APP BLOCKED IN SPLIT SCREEN 🚫\n\n$packageName\n\nSplit screen mode is not allowed for blocked apps\n\nTap to go home"
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:315: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                text = "🚫 APP BLOCKED IN SPLIT SCREEN 🚫\n\n$packageName\n\nSplit screen mode is not allowed for blocked apps\n\nTap to go home"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:315: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                text = "🚫 APP BLOCKED IN SPLIT SCREEN 🚫\n\n$packageName\n\nSplit screen mode is not allowed for blocked apps\n\nTap to go home"
                                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\service\AppBlockAccessibilityService.kt:315: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                text = "🚫 APP BLOCKED IN SPLIT SCREEN 🚫\n\n$packageName\n\nSplit screen mode is not allowed for blocked apps\n\nTap to go home"
                                                                                                                                  ~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\MainActivity.kt:94: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Blocked apps: 0"
                    ~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\MainActivity.kt:116: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        statusText.text = "Status: ⚙️ Setup Required"
                           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\MainActivity.kt:117: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        blockedCountText.text = "Please complete setup first"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\MainActivity.kt:224: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        blockedCountText.text = "Blocked apps: $blockedCount"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\MainActivity.kt:224: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        blockedCountText.text = "Blocked apps: $blockedCount"
                                 ~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:55: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "🔒 App Block Setup"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:65: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "To block apps effectively, App Block needs special permissions. Please grant the following permissions:"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:82: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Checking permissions..."
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:92: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Continue to App"
                    ~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:183: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            text = "$title ${if (isGranted) "✅" else "❌"}"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:210: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                text = "Grant Permission"
                        ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:286: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        tvStatus.text = "Permissions: $grantedCount/$totalCount granted"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:286: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvStatus.text = "Permissions: $grantedCount/$totalCount granted"
                         ~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:286: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvStatus.text = "Permissions: $grantedCount/$totalCount granted"
                                                               ~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:290: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            btnContinue.text = "✅ Continue to App"
                                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:291: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            tvStatus.text = "${tvStatus.text} - Ready to block apps!"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:291: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvStatus.text = "${tvStatus.text} - Ready to block apps!"
                                             ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:294: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            btnContinue.text = "❌ Need Critical Permissions"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:295: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            tvStatus.text = "${tvStatus.text} - Missing critical permissions"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\java\com\appblock\ui\PermissionSetupActivity.kt:295: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvStatus.text = "${tvStatus.text} - Missing critical permissions"
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

0 errors, 29 warnings (4 errors, 118 warnings filtered by baseline lint-baseline.xml)
