<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.2" type="incidents">

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="13"
            column="9"
            startOffset="225"
            endLine="13"
            endColumn="21"
            endOffset="237"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="6"
            column="22"
            startOffset="247"
            endLine="6"
            endColumn="75"
            endOffset="300"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="25"
            column="13"
            startOffset="1113"
            endLine="25"
            endColumn="38"
            endOffset="1138"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="42"
            column="20"
            startOffset="890"
            endLine="42"
            endColumn="51"
            endOffset="921"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="43"
            column="20"
            startOffset="941"
            endLine="43"
            endColumn="56"
            endOffset="977"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.9.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.9.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="44"
            column="20"
            startOffset="997"
            endLine="44"
            endColumn="64"
            endOffset="1041"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="45"
            column="20"
            startOffset="1061"
            endLine="45"
            endColumn="70"
            endOffset="1111"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.6.1 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="46"
            column="20"
            startOffset="1131"
            endLine="46"
            endColumn="69"
            endOffset="1180"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.1 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="47"
            column="20"
            startOffset="1200"
            endLine="47"
            endColumn="70"
            endOffset="1250"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.6.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="48"
            column="20"
            startOffset="1270"
            endLine="48"
            endColumn="71"
            endOffset="1321"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.6.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="49"
            column="20"
            startOffset="1341"
            endLine="49"
            endColumn="65"
            endOffset="1386"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.8.1 is available: 2.10.2">
        <fix-replace
            description="Change to 2.10.2"
            family="Update versions"
            oldString="2.8.1"
            replacement="2.10.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="50"
            column="20"
            startOffset="1406"
            endLine="50"
            endColumn="58"
            endOffset="1444"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="55"
            column="31"
            startOffset="1636"
            endLine="55"
            endColumn="62"
            endOffset="1667"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="56"
            column="31"
            startOffset="1698"
            endLine="56"
            endColumn="75"
            endOffset="1742"/>
    </incident>

    <incident
        id="SwitchIntDef"
        severity="warning"
        message="Switch statement on an `int` with known associated constant missing case `AccessibilityEvent.TYPE_ANNOUNCEMENT`, `AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_END`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_START`, `AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED`, `AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_END`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_START`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED`, `AccessibilityEvent.TYPE_VIEW_CLICKED`, `AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED`, `AccessibilityEvent.TYPE_VIEW_HOVER_ENTER`, `AccessibilityEvent.TYPE_VIEW_HOVER_EXIT`, `AccessibilityEvent.TYPE_VIEW_LONG_CLICKED`, `AccessibilityEvent.TYPE_VIEW_SCROLLED`, `AccessibilityEvent.TYPE_VIEW_SELECTED`, `AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL`, `AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY`">
        <fix-data cases="android.view.accessibility.AccessibilityEvent.TYPE_ANNOUNCEMENT, android.view.accessibility.AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT, android.view.accessibility.AccessibilityEvent.TYPE_GESTURE_DETECTION_END, android.view.accessibility.AccessibilityEvent.TYPE_GESTURE_DETECTION_START, android.view.accessibility.AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_INTERACTION_END, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_INTERACTION_START, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_HOVER_ENTER, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_HOVER_EXIT, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_LONG_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_SCROLLED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_SELECTED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="84"
            column="13"
            startOffset="3374"
            endLine="84"
            endColumn="17"
            endOffset="3378"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/error_red&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/error_red&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/error_red"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/block_overlay.xml"
            line="27"
            column="13"
            startOffset="1034"
            endLine="27"
            endColumn="44"
            endOffset="1065"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Set drawableStartCompat=&quot;@drawable/ic_security&quot; and Delete drawableStart">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_security&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_security"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="100"
            column="25"
            startOffset="4248"
            endLine="100"
            endColumn="70"
            endOffset="4293"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Set drawableStartCompat=&quot;@drawable/ic_block&quot; and Delete drawableStart">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_block&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_block"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="139"
            column="25"
            startOffset="5953"
            endLine="139"
            endColumn="67"
            endOffset="5995"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Set drawableStartCompat=&quot;@drawable/ic_settings&quot; and Delete drawableStart">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_settings&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_settings"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="178"
            column="25"
            startOffset="7640"
            endLine="178"
            endColumn="70"
            endOffset="7685"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/AppSelectionActivity.kt"
            line="271"
            column="17"
            startOffset="9782"
            endLine="271"
            endColumn="54"
            endOffset="9819"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/AppSelectionActivity.kt"
            line="295"
            column="17"
            startOffset="10781"
            endLine="295"
            endColumn="54"
            endOffset="10818"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `AppBlockAccessibilityService` which has field `overlayView` pointing to `View`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="31"
            column="9"
            startOffset="1097"
            endLine="32"
            endColumn="67"
            endOffset="1173"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (907 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="10"
            column="27"
            startOffset="344"
            endLine="10"
            endColumn="934"
            endOffset="1251"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/block_overlay_background` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_block_overlay.xml"
            line="6"
            column="5"
            startOffset="252"
            endLine="6"
            endColumn="57"
            endOffset="304"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="6"
            column="5"
            startOffset="253"
            endLine="6"
            endColumn="38"
            endOffset="286"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/block_overlay_background` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/block_overlay.xml"
            line="7"
            column="5"
            startOffset="317"
            endLine="7"
            endColumn="57"
            endOffset="369"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml"
            line="5"
            column="5"
            startOffset="197"
            endLine="5"
            endColumn="64"
            endOffset="256"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="32"
            startOffset="712"
            endLine="18"
            endColumn="46"
            endOffset="726"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml"
            line="51"
            column="17"
            startOffset="1842"
            endLine="51"
            endColumn="40"
            endOffset="1865"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="282"
            column="40"
            startOffset="11020"
            endLine="296"
            endColumn="10"
            endOffset="11605"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_block_overlay.xml"
            line="20"
            column="10"
            startOffset="726"
            endLine="20"
            endColumn="19"
            endOffset="735"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/block_overlay.xml"
            line="21"
            column="10"
            startOffset="784"
            endLine="21"
            endColumn="19"
            endOffset="793"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml"
            line="12"
            column="6"
            startOffset="408"
            endLine="12"
            endColumn="15"
            endOffset="417"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="234"
            column="24"
            startOffset="9057"
            endLine="234"
            endColumn="77"
            endOffset="9110"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="234"
            column="25"
            startOffset="9058"
            endLine="234"
            endColumn="42"
            endOffset="9075"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="234"
            column="62"
            startOffset="9095"
            endLine="234"
            endColumn="76"
            endOffset="9109"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="315"
            column="24"
            startOffset="12362"
            endLine="315"
            endColumn="146"
            endOffset="12484"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="315"
            column="25"
            startOffset="12363"
            endLine="315"
            endColumn="58"
            endOffset="12396"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="315"
            column="78"
            startOffset="12416"
            endLine="315"
            endColumn="127"
            endOffset="12465"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="315"
            column="131"
            startOffset="12469"
            endLine="315"
            endColumn="145"
            endOffset="12483"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/adapter/AppListAdapter.kt"
            line="36"
            column="41"
            startOffset="1503"
            endLine="36"
            endColumn="47"
            endOffset="1509"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/AppSelectionActivity.kt"
            line="154"
            column="35"
            startOffset="5548"
            endLine="154"
            endColumn="62"
            endOffset="5575"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/AppSelectionActivity.kt"
            line="154"
            column="56"
            startOffset="5569"
            endLine="154"
            endColumn="61"
            endOffset="5574"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="111"
            column="52"
            startOffset="4069"
            endLine="111"
            endColumn="92"
            endOffset="4109"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="111"
            column="53"
            startOffset="4070"
            endLine="111"
            endColumn="75"
            endOffset="4092"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="111"
            column="83"
            startOffset="4100"
            endLine="111"
            endColumn="91"
            endOffset="4108"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="115"
            column="47"
            startOffset="4346"
            endLine="115"
            endColumn="76"
            endOffset="4375"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="123"
            column="43"
            startOffset="4791"
            endLine="123"
            endColumn="72"
            endOffset="4820"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="128"
            column="33"
            startOffset="4929"
            endLine="128"
            endColumn="55"
            endOffset="4951"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/BlockOverlayActivity.kt"
            line="128"
            column="34"
            startOffset="4930"
            endLine="128"
            endColumn="47"
            endOffset="4943"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="55"
            column="21"
            startOffset="1486"
            endLine="55"
            endColumn="49"
            endOffset="1514"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="64"
            column="21"
            startOffset="1772"
            endLine="64"
            endColumn="41"
            endOffset="1792"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="75"
            column="21"
            startOffset="2101"
            endLine="75"
            endColumn="38"
            endOffset="2118"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="86"
            column="21"
            startOffset="2410"
            endLine="86"
            endColumn="40"
            endOffset="2429"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="94"
            column="21"
            startOffset="2641"
            endLine="94"
            endColumn="36"
            endOffset="2656"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="116"
            column="28"
            startOffset="3404"
            endLine="116"
            endColumn="53"
            endOffset="3429"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="117"
            column="34"
            startOffset="3464"
            endLine="117"
            endColumn="61"
            endOffset="3491"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="224"
            column="33"
            startOffset="7419"
            endLine="224"
            endColumn="62"
            endOffset="7448"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/MainActivity.kt"
            line="224"
            column="34"
            startOffset="7420"
            endLine="224"
            endColumn="48"
            endOffset="7434"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/MainActivity.kt"
            line="303"
            column="43"
            startOffset="11349"
            endLine="303"
            endColumn="75"
            endOffset="11381"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/MainActivity.kt"
            line="303"
            column="61"
            startOffset="11367"
            endLine="303"
            endColumn="74"
            endOffset="11380"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="55"
            column="21"
            startOffset="1608"
            endLine="55"
            endColumn="39"
            endOffset="1626"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="65"
            column="21"
            startOffset="1980"
            endLine="65"
            endColumn="124"
            endOffset="2083"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="82"
            column="21"
            startOffset="2666"
            endLine="82"
            endColumn="44"
            endOffset="2689"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="92"
            column="21"
            startOffset="3045"
            endLine="92"
            endColumn="36"
            endOffset="3060"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="183"
            column="20"
            startOffset="6576"
            endLine="183"
            endColumn="59"
            endOffset="6615"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="210"
            column="25"
            startOffset="7550"
            endLine="210"
            endColumn="41"
            endOffset="7566"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="286"
            column="25"
            startOffset="10896"
            endLine="286"
            endColumn="73"
            endOffset="10944"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="286"
            column="26"
            startOffset="10897"
            endLine="286"
            endColumn="39"
            endOffset="10910"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="286"
            column="64"
            startOffset="10935"
            endLine="286"
            endColumn="72"
            endOffset="10943"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="290"
            column="33"
            startOffset="11065"
            endLine="290"
            endColumn="50"
            endOffset="11082"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="291"
            column="29"
            startOffset="11112"
            endLine="291"
            endColumn="70"
            endOffset="11153"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="291"
            column="46"
            startOffset="11129"
            endLine="291"
            endColumn="69"
            endOffset="11152"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="294"
            column="33"
            startOffset="11245"
            endLine="294"
            endColumn="60"
            endOffset="11272"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="295"
            column="29"
            startOffset="11302"
            endLine="295"
            endColumn="78"
            endOffset="11351"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PermissionSetupActivity.kt"
            line="295"
            column="46"
            startOffset="11319"
            endLine="295"
            endColumn="77"
            endOffset="11350"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="88"
            column="43"
            startOffset="2922"
            endLine="88"
            endColumn="62"
            endOffset="2941"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="110"
            column="41"
            startOffset="3613"
            endLine="110"
            endColumn="57"
            endOffset="3629"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="111"
            column="43"
            startOffset="3673"
            endLine="111"
            endColumn="87"
            endOffset="3717"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="114"
            column="41"
            startOffset="3805"
            endLine="114"
            endColumn="58"
            endOffset="3822"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="115"
            column="43"
            startOffset="3866"
            endLine="115"
            endColumn="86"
            endOffset="3909"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="118"
            column="41"
            startOffset="3987"
            endLine="118"
            endColumn="57"
            endOffset="4003"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="119"
            column="43"
            startOffset="4047"
            endLine="119"
            endColumn="67"
            endOffset="4071"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="128"
            column="42"
            startOffset="4409"
            endLine="128"
            endColumn="57"
            endOffset="4424"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/PinAuthActivity.kt"
            line="128"
            column="43"
            startOffset="4410"
            endLine="128"
            endColumn="48"
            endOffset="4415"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/SimpleAppSelectionActivity.kt"
            line="53"
            column="21"
            startOffset="1505"
            endLine="53"
            endColumn="41"
            endOffset="1525"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 apps&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_app_selection.xml"
            line="68"
            column="13"
            startOffset="2785"
            endLine="68"
            endColumn="34"
            endOffset="2806"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;App Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_block_overlay.xml"
            line="34"
            column="13"
            startOffset="1280"
            endLine="34"
            endColumn="36"
            endOffset="1303"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Detected by: unknown&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_block_overlay.xml"
            line="101"
            column="13"
            startOffset="3975"
            endLine="101"
            endColumn="48"
            endOffset="4010"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enable parental controls&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="64"
            column="29"
            startOffset="2721"
            endLine="64"
            endColumn="68"
            endOffset="2760"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Checking permissions...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="111"
            column="25"
            startOffset="4801"
            endLine="111"
            endColumn="63"
            endOffset="4839"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 apps blocked&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="150"
            column="25"
            startOffset="6505"
            endLine="150"
            endColumn="54"
            endOffset="6534"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Configure app settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="188"
            column="25"
            startOffset="8127"
            endLine="188"
            endColumn="62"
            endOffset="8164"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Setup&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="202"
            column="17"
            startOffset="8663"
            endLine="202"
            endColumn="37"
            endOffset="8683"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your PIN to continue&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="28"
            column="9"
            startOffset="994"
            endLine="28"
            endColumn="50"
            endOffset="1035"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="55"
            column="13"
            startOffset="2009"
            endLine="55"
            endColumn="37"
            endOffset="2033"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="79"
            column="17"
            startOffset="2789"
            endLine="79"
            endColumn="33"
            endOffset="2805"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="84"
            column="17"
            startOffset="2932"
            endLine="84"
            endColumn="33"
            endOffset="2948"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="89"
            column="17"
            startOffset="3075"
            endLine="89"
            endColumn="33"
            endOffset="3091"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;4&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="102"
            column="17"
            startOffset="3441"
            endLine="102"
            endColumn="33"
            endOffset="3457"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="107"
            column="17"
            startOffset="3584"
            endLine="107"
            endColumn="33"
            endOffset="3600"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;6&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="112"
            column="17"
            startOffset="3727"
            endLine="112"
            endColumn="33"
            endOffset="3743"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;7&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="125"
            column="17"
            startOffset="4093"
            endLine="125"
            endColumn="33"
            endOffset="4109"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;8&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="130"
            column="17"
            startOffset="4236"
            endLine="130"
            endColumn="33"
            endOffset="4252"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;9&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="135"
            column="17"
            startOffset="4379"
            endLine="135"
            endColumn="33"
            endOffset="4395"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="153"
            column="17"
            startOffset="4916"
            endLine="153"
            endColumn="33"
            endOffset="4932"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⌫&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml"
            line="158"
            column="17"
            startOffset="5067"
            endLine="158"
            endColumn="33"
            endOffset="5083"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter PIN (4-6 digits)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_setup.xml"
            line="20"
            column="13"
            startOffset="804"
            endLine="20"
            endColumn="50"
            endOffset="841"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Confirm PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_setup.xml"
            line="36"
            column="13"
            startOffset="1445"
            endLine="36"
            endColumn="39"
            endOffset="1471"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_verify.xml"
            line="19"
            column="13"
            startOffset="761"
            endLine="19"
            endColumn="37"
            endOffset="785"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;App Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml"
            line="38"
            column="17"
            startOffset="1297"
            endLine="38"
            endColumn="40"
            endOffset="1320"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SYSTEM&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml"
            line="49"
            column="17"
            startOffset="1755"
            endLine="49"
            endColumn="38"
            endOffset="1776"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;com.example.app&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml"
            line="61"
            column="13"
            startOffset="2153"
            endLine="61"
            endColumn="43"
            endOffset="2183"/>
    </incident>

</incidents>
