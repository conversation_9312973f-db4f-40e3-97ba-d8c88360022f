{"logs": [{"outputFile": "com.appblock.app-mergeDebugResources-54:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6034af7fb018680d3c013049f167ec16\\transformed\\preference-1.2.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,476,645,725", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "169,256,336,471,640,720,796"}, "to": {"startLines": "48,50,105,107,110,111,112", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4517,4652,9105,9265,9583,9752,9832", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "4581,4734,9180,9395,9747,9827,9903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\94c90974a14a16e04a1c279783b656e5\\transformed\\material-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1092,1187,1272,1334,1422,1484,1553,1616,1689,1752,1806,1927,1984,2046,2100,2177,2314,2399,2481,2616,2697,2778,2869,2924,2975,3041,3114,3194,3285,3360,3437,3506,3583,3671,3760,3853,3946,4020,4100,4194,4245,4311,4395,4483,4545,4609,4672,4787,4897,5003,5112,5171,5226", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,90,54,50,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,58,54,79", "endOffsets": "263,347,428,505,604,699,798,938,1021,1087,1182,1267,1329,1417,1479,1548,1611,1684,1747,1801,1922,1979,2041,2095,2172,2309,2394,2476,2611,2692,2773,2864,2919,2970,3036,3109,3189,3280,3355,3432,3501,3578,3666,3755,3848,3941,4015,4095,4189,4240,4306,4390,4478,4540,4604,4667,4782,4892,4998,5107,5166,5221,5301"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3012,3096,3177,3254,3353,4195,4294,4434,4586,4739,4834,4919,4981,5069,5131,5200,5263,5336,5399,5453,5574,5631,5693,5747,5824,5961,6046,6128,6263,6344,6425,6516,6571,6622,6688,6761,6841,6932,7007,7084,7153,7230,7318,7407,7500,7593,7667,7747,7841,7892,7958,8042,8130,8192,8256,8319,8434,8544,8650,8759,8818,9185", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,106", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,90,54,50,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,58,54,79", "endOffsets": "313,3091,3172,3249,3348,3443,4289,4429,4512,4647,4829,4914,4976,5064,5126,5195,5258,5331,5394,5448,5569,5626,5688,5742,5819,5956,6041,6123,6258,6339,6420,6511,6566,6617,6683,6756,6836,6927,7002,7079,7148,7225,7313,7402,7495,7588,7662,7742,7836,7887,7953,8037,8125,8187,8251,8314,8429,8539,8645,8754,8813,8868,9260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78c237f2989eef627b8efaab812c395e\\transformed\\core-1.10.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3546,3648,3747,3849,3958,4065,9482", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3541,3643,3742,3844,3953,4060,4190,9578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aa3c339c6f6dd0717546ddbb353d969b\\transformed\\navigation-ui-2.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,122", "endOffsets": "159,282"}, "to": {"startLines": "103,104", "startColumns": "4,4", "startOffsets": "8873,8982", "endColumns": "108,122", "endOffsets": "8977,9100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\01183fe8ec5b70d0914d8fb6bc2ec50c\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,9400", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,9477"}}]}]}