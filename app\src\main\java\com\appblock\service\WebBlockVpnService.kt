package com.appblock.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.VpnService
import android.os.Build
import android.os.ParcelFileDescriptor
import android.util.Log
import androidx.core.app.NotificationCompat
import com.appblock.MainActivity
import com.appblock.data.BlockedAppsManager
import java.io.FileInputStream
import java.io.FileOutputStream
import java.net.InetSocketAddress
import java.nio.ByteBuffer
import java.nio.channels.DatagramChannel
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

class WebBlockVpnService : VpnService() {

    companion object {
        private const val TAG = "WebBlockVpnService"
        private const val VPN_ADDRESS = "********"
        private const val VPN_ROUTE = "0.0.0.0"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "web_block_vpn_service"
        
        @Volatile
        private var instance: WebBlockVpnService? = null
        
        fun getInstance(): WebBlockVpnService? = instance
        
        fun isServiceRunning(): Boolean = instance != null
        
        /**
         * Start the VPN service
         */
        fun startVpnService(context: Context) {
            val intent = Intent(context, WebBlockVpnService::class.java)
            intent.action = "START_VPN"
            context.startForegroundService(intent)
        }
        
        /**
         * Stop the VPN service
         */
        fun stopVpnService(context: Context) {
            val intent = Intent(context, WebBlockVpnService::class.java)
            intent.action = "STOP_VPN"
            context.startService(intent)
        }
    }

    private lateinit var blockedAppsManager: BlockedAppsManager
    private var vpnInterface: ParcelFileDescriptor? = null
    private val isRunning = AtomicBoolean(false)
    private val vpnThread = AtomicReference<Thread>()
    
    // DNS servers to use (Google DNS as fallback)
    private val dnsServers = listOf("*******", "*******")

    override fun onCreate() {
        super.onCreate()
        instance = this
        blockedAppsManager = BlockedAppsManager(this)
        Log.d(TAG, "WebBlockVpnService created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            "START_VPN" -> {
                Log.d(TAG, "Starting VPN service")
                startVpn()
            }
            "STOP_VPN" -> {
                Log.d(TAG, "Stopping VPN service")
                stopVpn()
            }
        }
        return START_STICKY
    }

    private fun startVpn() {
        if (isRunning.get()) {
            Log.d(TAG, "VPN already running")
            return
        }

        try {
            // Create VPN interface
            vpnInterface = createVpnInterface()
            
            if (vpnInterface != null) {
                isRunning.set(true)
                startForegroundService()
                
                // Start VPN thread
                val thread = Thread { runVpn() }
                thread.name = "VPN-Thread"
                vpnThread.set(thread)
                thread.start()
                
                Log.d(TAG, "✅ VPN service started successfully")
            } else {
                Log.e(TAG, "❌ Failed to create VPN interface")
                stopSelf()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error starting VPN", e)
            stopSelf()
        }
    }

    private fun createVpnInterface(): ParcelFileDescriptor? {
        return try {
            val builder = Builder()
                .setSession("AppBlock VPN")
                .addAddress(VPN_ADDRESS, 32)
                .addRoute(VPN_ROUTE, 0)
                .setMtu(1500)
                .setBlocking(true)
            
            // Add DNS servers
            for (dns in dnsServers) {
                builder.addDnsServer(dns)
            }
            
            // Allow our own app to bypass VPN to prevent loops
            try {
                builder.addDisallowedApplication(packageName)
            } catch (e: Exception) {
                Log.w(TAG, "Could not disallow own package", e)
            }
            
            val vpnInterface = builder.establish()
            Log.d(TAG, "VPN interface created: $vpnInterface")
            vpnInterface
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating VPN interface", e)
            null
        }
    }

    private fun runVpn() {
        Log.d(TAG, "🚀 VPN thread started")
        
        val vpnInput = FileInputStream(vpnInterface!!.fileDescriptor)
        val vpnOutput = FileOutputStream(vpnInterface!!.fileDescriptor)
        
        val buffer = ByteBuffer.allocate(32767)
        
        try {
            while (isRunning.get() && !Thread.currentThread().isInterrupted) {
                // Read packet from VPN interface
                val length = vpnInput.read(buffer.array())
                
                if (length > 0) {
                    buffer.limit(length)
                    
                    // Process the packet
                    val shouldBlock = processPacket(buffer)
                    
                    if (!shouldBlock) {
                        // Forward packet if not blocked
                        forwardPacket(buffer, vpnOutput)
                    } else {
                        Log.d(TAG, "🚫 Packet blocked by VPN filter")
                    }
                    
                    buffer.clear()
                }
            }
        } catch (e: Exception) {
            if (isRunning.get()) {
                Log.e(TAG, "Error in VPN thread", e)
            }
        } finally {
            try {
                vpnInput.close()
                vpnOutput.close()
            } catch (e: Exception) {
                Log.e(TAG, "Error closing VPN streams", e)
            }
        }
        
        Log.d(TAG, "🛑 VPN thread stopped")
    }

    private fun processPacket(packet: ByteBuffer): Boolean {
        try {
            // Parse IP header
            val version = (packet.get(0).toInt() and 0xF0) shr 4
            if (version != 4) {
                // Only handle IPv4 for now
                return false
            }
            
            val protocol = packet.get(9).toInt() and 0xFF
            
            when (protocol) {
                17 -> { // UDP
                    return processUdpPacket(packet)
                }
                6 -> { // TCP
                    return processTcpPacket(packet)
                }
                else -> {
                    // Allow other protocols
                    return false
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing packet", e)
            return false
        }
    }

    private fun processUdpPacket(packet: ByteBuffer): Boolean {
        try {
            // Get IP header length
            val ipHeaderLength = (packet.get(0).toInt() and 0x0F) * 4
            
            // Get destination port from UDP header
            val destPort = ((packet.get(ipHeaderLength + 2).toInt() and 0xFF) shl 8) or
                          (packet.get(ipHeaderLength + 3).toInt() and 0xFF)
            
            // Check if this is a DNS request (port 53)
            if (destPort == 53) {
                return processDnsRequest(packet, ipHeaderLength)
            }
            
            return false
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing UDP packet", e)
            return false
        }
    }

    private fun processTcpPacket(packet: ByteBuffer): Boolean {
        try {
            // Get IP header length
            val ipHeaderLength = (packet.get(0).toInt() and 0x0F) * 4
            
            // Get destination IP
            val destIp = String.format(
                "%d.%d.%d.%d",
                packet.get(16).toInt() and 0xFF,
                packet.get(17).toInt() and 0xFF,
                packet.get(18).toInt() and 0xFF,
                packet.get(19).toInt() and 0xFF
            )
            
            // Get destination port
            val destPort = ((packet.get(ipHeaderLength + 2).toInt() and 0xFF) shl 8) or
                          (packet.get(ipHeaderLength + 3).toInt() and 0xFF)
            
            // Check if this is HTTP/HTTPS traffic
            if (destPort == 80 || destPort == 443) {
                Log.d(TAG, "🌐 HTTP/HTTPS traffic to $destIp:$destPort")
                
                // For now, we'll use IP-based blocking
                // In a more advanced implementation, we could do deep packet inspection
                return isIpBlocked(destIp)
            }
            
            return false
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing TCP packet", e)
            return false
        }
    }

    private fun processDnsRequest(packet: ByteBuffer, ipHeaderLength: Int): Boolean {
        try {
            // Extract DNS query from packet
            val dnsQuery = extractDnsQuery(packet, ipHeaderLength)
            
            if (dnsQuery != null) {
                Log.d(TAG, "🔍 DNS query for: $dnsQuery")
                
                // Check if domain should be blocked
                val shouldBlock = blockedAppsManager.isWebsiteBlocked(dnsQuery)
                
                if (shouldBlock) {
                    Log.d(TAG, "🚫 DNS query blocked for: $dnsQuery")
                    return true
                }
            }
            
            return false
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing DNS request", e)
            return false
        }
    }

    private fun extractDnsQuery(packet: ByteBuffer, ipHeaderLength: Int): String? {
        try {
            // Skip IP header, UDP header (8 bytes), and DNS header (12 bytes)
            var offset = ipHeaderLength + 8 + 12
            
            val domain = StringBuilder()
            
            while (offset < packet.limit()) {
                val length = packet.get(offset).toInt() and 0xFF
                
                if (length == 0) break
                
                if (domain.isNotEmpty()) {
                    domain.append(".")
                }
                
                offset++
                
                for (i in 0 until length) {
                    if (offset >= packet.limit()) break
                    domain.append((packet.get(offset).toInt() and 0xFF).toChar())
                    offset++
                }
            }
            
            return if (domain.isNotEmpty()) domain.toString() else null
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting DNS query", e)
            return null
        }
    }

    private fun isIpBlocked(ip: String): Boolean {
        // This is a simplified check - in practice, you'd want to maintain
        // a mapping of blocked domains to their IP addresses
        // For now, we'll just log the IP and allow it
        Log.d(TAG, "IP check for: $ip (allowing for now)")
        return false
    }

    private fun forwardPacket(packet: ByteBuffer, vpnOutput: FileOutputStream) {
        try {
            // In a real implementation, you would forward the packet to the actual network
            // For this simplified version, we'll just drop allowed packets
            // A full implementation would require a more complex networking setup
            
            Log.v(TAG, "Forwarding packet (${packet.limit()} bytes)")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error forwarding packet", e)
        }
    }

    private fun stopVpn() {
        Log.d(TAG, "Stopping VPN...")
        
        isRunning.set(false)
        
        // Interrupt VPN thread
        vpnThread.get()?.interrupt()
        
        // Close VPN interface
        vpnInterface?.close()
        vpnInterface = null
        
        // Stop foreground service
        stopForeground(true)
        stopSelf()
        
        Log.d(TAG, "✅ VPN stopped")
    }

    private fun startForegroundService() {
        createNotificationChannel()

        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Website Blocking Active")
            .setContentText("VPN is filtering network traffic to block websites")
            .setSmallIcon(android.R.drawable.ic_menu_view)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Website Block VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notification for VPN-based website blocking service"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        stopVpn()
        Log.d(TAG, "WebBlockVpnService destroyed")
    }

    override fun onRevoke() {
        super.onRevoke()
        Log.d(TAG, "VPN permission revoked")
        stopVpn()
    }
}