com/appblock/MainActivity#com/appblock/MainActivity$Companion$com/appblock/data/BlockedAppsManagerAcom/appblock/data/BlockedAppsManager$loadCaches$blockedAppsList$1Dcom/appblock/data/BlockedAppsManager$loadCaches$temporaryAllowsMap$1?com/appblock/data/BlockedAppsManager$loadCaches$timeLimitsMap$1.com/appblock/data/BlockedAppsManager$Companion2com/appblock/data/BlockedAppsManager$TimeLimitInfo!com/appblock/data/SettingsManager8com/appblock/data/SettingsManager$getTamperingAttempts$11com/appblock/data/SettingsManager$getBootEvents$1;com/appblock/data/SettingsManager$importSettings$settings$1+com/appblock/data/SettingsManager$Companion1com/appblock/receiver/AppBlockDeviceAdminReceiver;com/appblock/receiver/AppBlockDeviceAdminReceiver$Companion"com/appblock/receiver/BootReceiver,com/appblock/receiver/BootReceiver$Companion/com/appblock/receiver/PermissionMonitorReceiver9com/appblock/receiver/PermissionMonitorReceiver$Companion1com/appblock/service/AppBlockAccessibilityService;com/appblock/service/AppBlockAccessibilityService$Companion-com/appblock/service/UsageStatsMonitorService7com/appblock/service/UsageStatsMonitorService$Companion'com/appblock/test/CoreFunctionalityTest1com/appblock/test/CoreFunctionalityTest$Companion$com/appblock/ui/AppSelectionActivity.com/appblock/ui/AppSelectionActivity$setupUI$1/com/appblock/ui/AppSelectionActivity$loadApps$16com/appblock/ui/AppSelectionActivity$loadApps$1$apps$1Jcom/appblock/ui/AppSelectionActivity$loadInstalledApps$$inlined$sortedBy$1@com/appblock/ui/AppSelectionActivity$handleAppSelectionChanged$1:com/appblock/ui/AppSelectionActivity$onCreateOptionsMenu$1.com/appblock/ui/AppSelectionActivity$Companion,com/appblock/ui/AppSelectionActivity$AppInfo$com/appblock/ui/BlockOverlayActivity.com/appblock/ui/BlockOverlayActivity$Companioncom/appblock/ui/MainActivity<com/appblock/ui/MainActivity$showPermissionsDialog$message$10com/appblock/ui/MainActivity$toggleAppBlocking$1&com/appblock/ui/MainActivity$Companioncom/appblock/ui/PinAuthActivity)com/appblock/ui/PinAuthActivity$setupUI$1)com/appblock/ui/PinAuthActivity$Companion*com/appblock/ui/SimpleAppSelectionActivity5com/appblock/ui/SimpleAppSelectionActivity$loadApps$1<com/appblock/ui/SimpleAppSelectionActivity$loadApps$1$apps$1Pcom/appblock/ui/SimpleAppSelectionActivity$loadInstalledApps$$inlined$sortedBy$14com/appblock/ui/SimpleAppSelectionActivity$Companion2com/appblock/ui/SimpleAppSelectionActivity$AppInfo9com/appblock/ui/SimpleAppSelectionActivity$AppListAdapter&com/appblock/ui/adapter/AppListAdapter4com/appblock/ui/adapter/AppListAdapter$AppViewHolder6com/appblock/ui/adapter/AppListAdapter$AppDiffCallback"com/appblock/utils/PermissionUtils'com/appblock/ui/PermissionSetupActivity1com/appblock/ui/PermissionSetupActivity$Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                