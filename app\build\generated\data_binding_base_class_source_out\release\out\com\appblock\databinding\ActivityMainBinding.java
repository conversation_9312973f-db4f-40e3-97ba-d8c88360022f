// Generated by view binder compiler. Do not edit!
package com.appblock.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.appblock.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnDeviceAdmin;

  @NonNull
  public final MaterialButton btnSetupPin;

  @NonNull
  public final MaterialCardView cardBlockedApps;

  @NonNull
  public final MaterialCardView cardPermissions;

  @NonNull
  public final MaterialCardView cardSettings;

  @NonNull
  public final SwitchMaterial switchAppBlocking;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvBlockedAppsCount;

  @NonNull
  public final TextView tvPermissionStatus;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnDeviceAdmin, @NonNull MaterialButton btnSetupPin,
      @NonNull MaterialCardView cardBlockedApps, @NonNull MaterialCardView cardPermissions,
      @NonNull MaterialCardView cardSettings, @NonNull SwitchMaterial switchAppBlocking,
      @NonNull Toolbar toolbar, @NonNull TextView tvBlockedAppsCount,
      @NonNull TextView tvPermissionStatus) {
    this.rootView = rootView;
    this.btnDeviceAdmin = btnDeviceAdmin;
    this.btnSetupPin = btnSetupPin;
    this.cardBlockedApps = cardBlockedApps;
    this.cardPermissions = cardPermissions;
    this.cardSettings = cardSettings;
    this.switchAppBlocking = switchAppBlocking;
    this.toolbar = toolbar;
    this.tvBlockedAppsCount = tvBlockedAppsCount;
    this.tvPermissionStatus = tvPermissionStatus;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_device_admin;
      MaterialButton btnDeviceAdmin = ViewBindings.findChildViewById(rootView, id);
      if (btnDeviceAdmin == null) {
        break missingId;
      }

      id = R.id.btn_setup_pin;
      MaterialButton btnSetupPin = ViewBindings.findChildViewById(rootView, id);
      if (btnSetupPin == null) {
        break missingId;
      }

      id = R.id.card_blocked_apps;
      MaterialCardView cardBlockedApps = ViewBindings.findChildViewById(rootView, id);
      if (cardBlockedApps == null) {
        break missingId;
      }

      id = R.id.card_permissions;
      MaterialCardView cardPermissions = ViewBindings.findChildViewById(rootView, id);
      if (cardPermissions == null) {
        break missingId;
      }

      id = R.id.card_settings;
      MaterialCardView cardSettings = ViewBindings.findChildViewById(rootView, id);
      if (cardSettings == null) {
        break missingId;
      }

      id = R.id.switch_app_blocking;
      SwitchMaterial switchAppBlocking = ViewBindings.findChildViewById(rootView, id);
      if (switchAppBlocking == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_blocked_apps_count;
      TextView tvBlockedAppsCount = ViewBindings.findChildViewById(rootView, id);
      if (tvBlockedAppsCount == null) {
        break missingId;
      }

      id = R.id.tv_permission_status;
      TextView tvPermissionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionStatus == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, btnDeviceAdmin, btnSetupPin,
          cardBlockedApps, cardPermissions, cardSettings, switchAppBlocking, toolbar,
          tvBlockedAppsCount, tvPermissionStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
