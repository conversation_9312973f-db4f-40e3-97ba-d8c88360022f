<lint-module
    format="1"
    dir="C:\Users\<USER>\Documents\augment-projects\appblock\app"
    name=":app"
    type="APP"
    maven="AppBlock:app:unspecified"
    agpVersion="8.8.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
