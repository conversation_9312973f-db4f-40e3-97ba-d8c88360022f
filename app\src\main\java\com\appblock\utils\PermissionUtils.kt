package com.appblock.utils

import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import com.appblock.service.AppBlockAccessibilityService

object PermissionUtils {

    private const val TAG = "PermissionUtils"

    /**
     * Check if the app has Usage Stats permission
     */
    fun hasUsageStatsPermission(context: Context): Boolean {
        val appOpsManager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        val mode = appOpsManager.checkOpNoThrow(
            AppOpsManager.OPSTR_GET_USAGE_STATS,
            android.os.Process.myUid(),
            context.packageName
        )
        return mode == AppOpsManager.MODE_ALLOWED
    }

    /**
     * Open Usage Stats settings page
     */
    fun openUsageStatsSettings(context: Context): Intent {
        return Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
    }

    /**
     * Check if Accessibility Service is enabled
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        val accessibilityEnabled = try {
            Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            Log.e(TAG, "Error finding accessibility setting", e)
            0
        }

        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            
            if (!services.isNullOrEmpty()) {
                val serviceName = "${context.packageName}/${AppBlockAccessibilityService::class.java.name}"
                return services.contains(serviceName)
            }
        }
        
        return false
    }

    /**
     * Open Accessibility Settings page
     */
    fun openAccessibilitySettings(context: Context): Intent {
        return Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
    }

    /**
     * Check if the app can draw over other apps (System Alert Window permission)
     */
    fun canDrawOverlays(context: Context): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Permission not required on older versions
        }
    }

    /**
     * Open overlay permission settings
     */
    fun openOverlaySettings(context: Context): Intent {
        return Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            android.net.Uri.parse("package:${context.packageName}")
        )
    }

    /**
     * Check if all critical permissions are granted
     */
    fun hasAllCriticalPermissions(context: Context): Boolean {
        return hasUsageStatsPermission(context) && 
               isAccessibilityServiceEnabled(context)
    }

    /**
     * Get list of missing permissions
     */
    fun getMissingPermissions(context: Context): List<String> {
        val missing = mutableListOf<String>()
        
        if (!hasUsageStatsPermission(context)) {
            missing.add("Usage Stats Access")
        }
        
        if (!isAccessibilityServiceEnabled(context)) {
            missing.add("Accessibility Service")
        }
        
        if (!canDrawOverlays(context)) {
            missing.add("Draw Over Other Apps")
        }
        
        return missing
    }

    /**
     * Check if device admin is active
     */
    fun isDeviceAdminActive(context: Context): Boolean {
        return com.appblock.receiver.AppBlockDeviceAdminReceiver.isDeviceAdminActive(context)
    }

    /**
     * Request device admin activation
     */
    fun requestDeviceAdmin(context: Context): Intent {
        return com.appblock.receiver.AppBlockDeviceAdminReceiver.requestDeviceAdminActivation(context)
    }

    /**
     * Check notification permission (Android 13+)
     */
    fun hasNotificationPermission(context: Context): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            androidx.core.content.ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == android.content.pm.PackageManager.PERMISSION_GRANTED
        } else {
            true // Not required on older versions
        }
    }

    /**
     * Get permission status summary
     */
    fun getPermissionStatusSummary(context: Context): Map<String, Boolean> {
        return mapOf(
            "Usage Stats" to hasUsageStatsPermission(context),
            "Accessibility Service" to isAccessibilityServiceEnabled(context),
            "Draw Over Apps" to canDrawOverlays(context),
            "Device Admin" to isDeviceAdminActive(context),
            "Notifications" to hasNotificationPermission(context)
        )
    }

    /**
     * Log current permission status
     */
    fun logPermissionStatus(context: Context) {
        val status = getPermissionStatusSummary(context)
        Log.d(TAG, "Permission Status:")
        status.forEach { (permission, granted) ->
            Log.d(TAG, "  $permission: ${if (granted) "GRANTED" else "DENIED"}")
        }
    }
}
