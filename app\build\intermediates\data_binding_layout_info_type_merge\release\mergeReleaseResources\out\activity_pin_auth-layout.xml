<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pin_auth" modulePackage="com.appblock" filePath="app\src\main\res\layout\activity_pin_auth.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_pin_auth_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="188" endOffset="14"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/tv_message" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="28" endOffset="33"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="31" startOffset="4" endLine="39" endOffset="35"/></Target><Target id="@+id/et_pin" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="38"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="75" startOffset="12" endLine="78" endOffset="34"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="80" startOffset="12" endLine="83" endOffset="34"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="85" startOffset="12" endLine="88" endOffset="34"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="98" startOffset="12" endLine="101" endOffset="34"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="103" startOffset="12" endLine="106" endOffset="34"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="108" startOffset="12" endLine="111" endOffset="34"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="121" startOffset="12" endLine="124" endOffset="34"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="126" startOffset="12" endLine="129" endOffset="34"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="131" startOffset="12" endLine="134" endOffset="34"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="144" startOffset="12" endLine="147" endOffset="46"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="149" startOffset="12" endLine="152" endOffset="34"/></Target><Target id="@+id/btn_backspace" view="Button"><Expressions/><location startLine="154" startOffset="12" endLine="157" endOffset="34"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="169" startOffset="8" endLine="176" endOffset="43"/></Target><Target id="@+id/btn_verify" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="178" startOffset="8" endLine="184" endOffset="43"/></Target></Targets></Layout>