  Manifest android  R android  
permission android.Manifest  POST_NOTIFICATIONS android.Manifest.permission  color 	android.R  drawable 	android.R  id 	android.R  transparent android.R.color  ic_lock_lock android.R.drawable  ic_menu_view android.R.drawable  home android.R.id  AccessibilityService android.accessibilityservice  AccessibilityServiceInfo android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  AccessibilityServiceInfo 1android.accessibilityservice.AccessibilityService  AppBlockAccessibilityService 1android.accessibilityservice.AccessibilityService  BlockedAppsManager 1android.accessibilityservice.AccessibilityService  Boolean 1android.accessibilityservice.AccessibilityService  Build 1android.accessibilityservice.AccessibilityService  
CHANNEL_ID 1android.accessibilityservice.AccessibilityService  Context 1android.accessibilityservice.AccessibilityService  	Exception 1android.accessibilityservice.AccessibilityService  FrameLayout 1android.accessibilityservice.AccessibilityService  Gravity 1android.accessibilityservice.AccessibilityService  Intent 1android.accessibilityservice.AccessibilityService  Log 1android.accessibilityservice.AccessibilityService  Long 1android.accessibilityservice.AccessibilityService  NOTIFICATION_ID 1android.accessibilityservice.AccessibilityService  NotificationChannel 1android.accessibilityservice.AccessibilityService  NotificationCompat 1android.accessibilityservice.AccessibilityService  NotificationManager 1android.accessibilityservice.AccessibilityService  PixelFormat 1android.accessibilityservice.AccessibilityService  String 1android.accessibilityservice.AccessibilityService  Suppress 1android.accessibilityservice.AccessibilityService  TAG 1android.accessibilityservice.AccessibilityService  View 1android.accessibilityservice.AccessibilityService  Volatile 1android.accessibilityservice.AccessibilityService  
WindowManager 1android.accessibilityservice.AccessibilityService  android 1android.accessibilityservice.AccessibilityService  apply 1android.accessibilityservice.AccessibilityService  blockApp 1android.accessibilityservice.AccessibilityService  checkForTamperingAttempt 1android.accessibilityservice.AccessibilityService  contains 1android.accessibilityservice.AccessibilityService  createNotificationChannel 1android.accessibilityservice.AccessibilityService  getSystemService 1android.accessibilityservice.AccessibilityService  goToHome 1android.accessibilityservice.AccessibilityService  handleSettingsAccess 1android.accessibilityservice.AccessibilityService  handleWindowContentChanged 1android.accessibilityservice.AccessibilityService  handleWindowStateChanged 1android.accessibilityservice.AccessibilityService  instance 1android.accessibilityservice.AccessibilityService  invoke 1android.accessibilityservice.AccessibilityService  isAppCurrentlyBlocked 1android.accessibilityservice.AccessibilityService  
isNullOrEmpty 1android.accessibilityservice.AccessibilityService  java 1android.accessibilityservice.AccessibilityService  let 1android.accessibilityservice.AccessibilityService  	lowercase 1android.accessibilityservice.AccessibilityService  onCreate 1android.accessibilityservice.AccessibilityService  	onDestroy 1android.accessibilityservice.AccessibilityService  onServiceConnected 1android.accessibilityservice.AccessibilityService  
removeOverlay 1android.accessibilityservice.AccessibilityService  setupOverlayInteractions 1android.accessibilityservice.AccessibilityService  showBlockOverlay 1android.accessibilityservice.AccessibilityService  
startActivity 1android.accessibilityservice.AccessibilityService  startForeground 1android.accessibilityservice.AccessibilityService  startForegroundService 1android.accessibilityservice.AccessibilityService  AccessibilityEvent 5android.accessibilityservice.AccessibilityServiceInfo  AccessibilityServiceInfo 5android.accessibilityservice.AccessibilityServiceInfo  FEEDBACK_GENERIC 5android.accessibilityservice.AccessibilityServiceInfo  FLAG_REPORT_VIEW_IDS 5android.accessibilityservice.AccessibilityServiceInfo  !FLAG_RETRIEVE_INTERACTIVE_WINDOWS 5android.accessibilityservice.AccessibilityServiceInfo  apply 5android.accessibilityservice.AccessibilityServiceInfo  
eventTypes 5android.accessibilityservice.AccessibilityServiceInfo  feedbackType 5android.accessibilityservice.AccessibilityServiceInfo  flags 5android.accessibilityservice.AccessibilityServiceInfo  getAPPLY 5android.accessibilityservice.AccessibilityServiceInfo  getApply 5android.accessibilityservice.AccessibilityServiceInfo  notificationTimeout 5android.accessibilityservice.AccessibilityServiceInfo  Activity android.app  
AppOpsManager android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  Service android.app  Activity android.app.Activity  ActivityAppSelectionBinding android.app.Activity  ActivityBlockOverlayBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityPinAuthBinding android.app.Activity  ActivityResultContracts android.app.Activity  AlertDialog android.app.Activity  AppInfo android.app.Activity  AppListAdapter android.app.Activity  AppSelectionActivity android.app.Activity  ApplicationInfo android.app.Activity  BaseAdapter android.app.Activity  BlockedAppsManager android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  Button android.app.Activity  CharSequence android.app.Activity  CoreFunctionalityTest android.app.Activity  CoroutineScope android.app.Activity  Dispatchers android.app.Activity  Editable android.app.Activity  	Exception android.app.Activity  	ImageView android.app.Activity  Int android.app.Activity  Intent android.app.Activity  LinearLayout android.app.Activity  LinearLayoutManager android.app.Activity  List android.app.Activity  ListView android.app.Activity  Log android.app.Activity  Long android.app.Activity  MAX_ATTEMPTS android.app.Activity  Menu android.app.Activity  MenuItem android.app.Activity  PackageManager android.app.Activity  PermissionUtils android.app.Activity  PinAuthActivity android.app.Activity  ProgressBar android.app.Activity  R android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  
SearchView android.app.Activity  SettingsManager android.app.Activity  String android.app.Activity  
SupervisorJob android.app.Activity  System android.app.Activity  TAG android.app.Activity  TextView android.app.Activity  TextWatcher android.app.Activity  Toast android.app.Activity  Unit android.app.Activity  UsageStatsMonitorService android.app.Activity  View android.app.Activity  
WindowManager android.app.Activity  all android.app.Activity  allApps android.app.Activity  android android.app.Activity  appListAdapter android.app.Activity  apply android.app.Activity  arrayOf android.app.Activity  binding android.app.Activity  blockedPackageName android.app.Activity  cancel android.app.Activity  checkFirstTimeSetup android.app.Activity  checkPermissions android.app.Activity  com android.app.Activity  contains android.app.Activity  count android.app.Activity  createLayout android.app.Activity  createUI android.app.Activity  deselectAllVisibleApps android.app.Activity  dropLast android.app.Activity  filter android.app.Activity  
filterApps android.app.Activity  finish android.app.Activity  forEachIndexed android.app.Activity  getTimeUntilBedtime android.app.Activity  goToHome android.app.Activity  handleAppSelectionChanged android.app.Activity  handleFailedAuth android.app.Activity  handleSuccessfulAuth android.app.Activity  invoke android.app.Activity  isBlank android.app.Activity  
isNotEmpty android.app.Activity  isSystemApp android.app.Activity  java android.app.Activity  joinToString android.app.Activity  launch android.app.Activity  launchPinAuthentication android.app.Activity  let android.app.Activity  listOf android.app.Activity  listView android.app.Activity  loadApps android.app.Activity  loadInstalledApps android.app.Activity  	lowercase android.app.Activity  map android.app.Activity  
mutableListOf android.app.Activity  
onBackPressed android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onNewIntent android.app.Activity  onOptionsItemSelected android.app.Activity  onPause android.app.Activity  onResume android.app.Activity  openAppSelection android.app.Activity  progressBar android.app.Activity  registerForActivityResult android.app.Activity  requestDeviceAdmin android.app.Activity  requestMissingPermissions android.app.Activity  requestMoreTime android.app.Activity  returnResult android.app.Activity  runCoreTests android.app.Activity  selectAllVisibleApps android.app.Activity  setContentView android.app.Activity  	setIntent android.app.Activity  	setResult android.app.Activity  setSupportActionBar android.app.Activity  
setupListView android.app.Activity  setupNumberPad android.app.Activity  setupUI android.app.Activity  showAboutDialog android.app.Activity  showBlockedAppsCount android.app.Activity  showFirstTimeSetupDialog android.app.Activity  showPermissionsDialog android.app.Activity  showPinSetupDialog android.app.Activity  showPinVerificationDialog android.app.Activity  showSystemAppWarning android.app.Activity  showTemporaryAllowDialog android.app.Activity  showTimeRequestDialog android.app.Activity  sortedBy android.app.Activity  
startActivity android.app.Activity  startForegroundService android.app.Activity  startMonitoringIfReady android.app.Activity  startMonitoringServices android.app.Activity  startService android.app.Activity  stopMonitoringServices android.app.Activity  stopService android.app.Activity  toSet android.app.Activity  toString android.app.Activity  toggleAppBlocking android.app.Activity  updateAppInList android.app.Activity  updateBlockedAppInfo android.app.Activity  updateStatus android.app.Activity  updateUI android.app.Activity  	verifyPin android.app.Activity  withContext android.app.Activity  MODE_ALLOWED android.app.AppOpsManager  OPSTR_GET_USAGE_STATS android.app.AppOpsManager  checkOpNoThrow android.app.AppOpsManager  apply android.app.NotificationChannel  description android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  setDescription android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  AccessibilityEvent android.app.Service  AccessibilityServiceInfo android.app.Service  AppBlockAccessibilityService android.app.Service  BlockedAppsManager android.app.Service  Boolean android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  	Exception android.app.Service  	Executors android.app.Service  FrameLayout android.app.Service  Gravity android.app.Service  Handler android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  InterruptedException android.app.Service  Log android.app.Service  Long android.app.Service  Looper android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  POLLING_INTERVAL_SECONDS android.app.Service  PermissionUtils android.app.Service  PixelFormat android.app.Service  START_STICKY android.app.Service  ScheduledExecutorService android.app.Service  String android.app.Service  Suppress android.app.Service  System android.app.Service  TAG android.app.Service  TimeUnit android.app.Service  UsageEvents android.app.Service  UsageStatsManager android.app.Service  UsageStatsMonitorService android.app.Service  View android.app.Service  Volatile android.app.Service  
WindowManager android.app.Service  android android.app.Service  apply android.app.Service  blockApp android.app.Service  blockAppViaActivity android.app.Service  checkForTamperingAttempt android.app.Service  contains android.app.Service  createNotificationChannel android.app.Service  find android.app.Service  getSystemService android.app.Service  goToHome android.app.Service  handleBlockedApp android.app.Service  handleSettingsAccess android.app.Service  handleWindowContentChanged android.app.Service  handleWindowStateChanged android.app.Service  instance android.app.Service  invoke android.app.Service  isAppCurrentlyBlocked android.app.Service  
isNullOrEmpty android.app.Service  java android.app.Service  let android.app.Service  	lowercase android.app.Service  monitorForegroundApp android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  onServiceConnected android.app.Service  
removeOverlay android.app.Service  setupOverlayInteractions android.app.Service  showBlockOverlay android.app.Service  
startActivity android.app.Service  startForeground android.app.Service  startForegroundService android.app.Service  startMonitoring android.app.Service  DeviceAdminReceiver android.app.admin  DevicePolicyManager android.app.admin  AppBlockDeviceAdminReceiver %android.app.admin.DeviceAdminReceiver  Boolean %android.app.admin.DeviceAdminReceiver  CharSequence %android.app.admin.DeviceAdminReceiver  
ComponentName %android.app.admin.DeviceAdminReceiver  Context %android.app.admin.DeviceAdminReceiver  DevicePolicyManager %android.app.admin.DeviceAdminReceiver  	Exception %android.app.admin.DeviceAdminReceiver  Intent %android.app.admin.DeviceAdminReceiver  Log %android.app.admin.DeviceAdminReceiver  SettingsManager %android.app.admin.DeviceAdminReceiver  System %android.app.admin.DeviceAdminReceiver  TAG %android.app.admin.DeviceAdminReceiver  Toast %android.app.admin.DeviceAdminReceiver  android %android.app.admin.DeviceAdminReceiver  com %android.app.admin.DeviceAdminReceiver  getComponentName %android.app.admin.DeviceAdminReceiver  handleTamperingAttempt %android.app.admin.DeviceAdminReceiver  invoke %android.app.admin.DeviceAdminReceiver  java %android.app.admin.DeviceAdminReceiver  
lockDevice %android.app.admin.DeviceAdminReceiver  
onDisabled %android.app.admin.DeviceAdminReceiver  	onEnabled %android.app.admin.DeviceAdminReceiver  onPasswordChanged %android.app.admin.DeviceAdminReceiver  onPasswordFailed %android.app.admin.DeviceAdminReceiver  onPasswordSucceeded %android.app.admin.DeviceAdminReceiver  restartProtectionServices %android.app.admin.DeviceAdminReceiver  sendTamperingNotification %android.app.admin.DeviceAdminReceiver  ACTION_ADD_DEVICE_ADMIN %android.app.admin.DevicePolicyManager  EXTRA_ADD_EXPLANATION %android.app.admin.DevicePolicyManager  EXTRA_DEVICE_ADMIN %android.app.admin.DevicePolicyManager  
isAdminActive %android.app.admin.DevicePolicyManager  lockNow %android.app.admin.DevicePolicyManager  UsageEvents android.app.usage  
UsageStats android.app.usage  UsageStatsManager android.app.usage  Event android.app.usage.UsageEvents  getNextEvent android.app.usage.UsageEvents  hasNextEvent android.app.usage.UsageEvents  MOVE_TO_FOREGROUND #android.app.usage.UsageEvents.Event  equals #android.app.usage.UsageEvents.Event  	eventType #android.app.usage.UsageEvents.Event  getEVENTType #android.app.usage.UsageEvents.Event  getEventType #android.app.usage.UsageEvents.Event  getLET #android.app.usage.UsageEvents.Event  getLet #android.app.usage.UsageEvents.Event  getPACKAGEName #android.app.usage.UsageEvents.Event  getPackageName #android.app.usage.UsageEvents.Event  getTIMEStamp #android.app.usage.UsageEvents.Event  getTimeStamp #android.app.usage.UsageEvents.Event  let #android.app.usage.UsageEvents.Event  packageName #android.app.usage.UsageEvents.Event  setEventType #android.app.usage.UsageEvents.Event  setPackageName #android.app.usage.UsageEvents.Event  setTimeStamp #android.app.usage.UsageEvents.Event  	timeStamp #android.app.usage.UsageEvents.Event  getPACKAGEName android.app.usage.UsageStats  getPackageName android.app.usage.UsageStats  getTOTALTimeInForeground android.app.usage.UsageStats  getTotalTimeInForeground android.app.usage.UsageStats  packageName android.app.usage.UsageStats  setPackageName android.app.usage.UsageStats  setTotalTimeInForeground android.app.usage.UsageStats  totalTimeInForeground android.app.usage.UsageStats  INTERVAL_DAILY #android.app.usage.UsageStatsManager  queryEvents #android.app.usage.UsageStatsManager  queryUsageStats #android.app.usage.UsageStatsManager  BroadcastReceiver android.content  
ComponentName android.content  ContentResolver android.content  Context android.content  DialogInterface android.content  Intent android.content  SharedPreferences android.content  ACTION_CHECK_PERMISSIONS !android.content.BroadcastReceiver  AppBlockDeviceAdminReceiver !android.content.BroadcastReceiver  Boolean !android.content.BroadcastReceiver  Build !android.content.BroadcastReceiver  CharSequence !android.content.BroadcastReceiver  
ComponentName !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  DevicePolicyManager !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  InterruptedException !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  PermissionUtils !android.content.BroadcastReceiver  SettingsManager !android.content.BroadcastReceiver  System !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  Thread !android.content.BroadcastReceiver  Toast !android.content.BroadcastReceiver  UsageStatsMonitorService !android.content.BroadcastReceiver  android !android.content.BroadcastReceiver  checkPermissions !android.content.BroadcastReceiver  com !android.content.BroadcastReceiver  contains !android.content.BroadcastReceiver  getComponentName !android.content.BroadcastReceiver  handleAppUpdated !android.content.BroadcastReceiver  handleBootCompleted !android.content.BroadcastReceiver  handlePackageChange !android.content.BroadcastReceiver  handleTamperingAttempt !android.content.BroadcastReceiver  invoke !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  
lockDevice !android.content.BroadcastReceiver  
onDisabled !android.content.BroadcastReceiver  	onEnabled !android.content.BroadcastReceiver  onPasswordChanged !android.content.BroadcastReceiver  onPasswordFailed !android.content.BroadcastReceiver  onPasswordSucceeded !android.content.BroadcastReceiver  removePrefix !android.content.BroadcastReceiver  restartProtectionServices !android.content.BroadcastReceiver  sendTamperingNotification !android.content.BroadcastReceiver  startProtectionServices !android.content.BroadcastReceiver  startProtectionServicesDelayed !android.content.BroadcastReceiver  APP_OPS_SERVICE android.content.Context  AccessibilityEvent android.content.Context  AccessibilityServiceInfo android.content.Context  Activity android.content.Context  ActivityAppSelectionBinding android.content.Context  ActivityBlockOverlayBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityPinAuthBinding android.content.Context  ActivityResultContracts android.content.Context  AlertDialog android.content.Context  AppBlockAccessibilityService android.content.Context  AppInfo android.content.Context  AppListAdapter android.content.Context  AppSelectionActivity android.content.Context  ApplicationInfo android.content.Context  BaseAdapter android.content.Context  BlockedAppsManager android.content.Context  Boolean android.content.Context  Build android.content.Context  Bundle android.content.Context  Button android.content.Context  
CHANNEL_ID android.content.Context  CharSequence android.content.Context  Context android.content.Context  CoreFunctionalityTest android.content.Context  CoroutineScope android.content.Context  DEVICE_POLICY_SERVICE android.content.Context  Dispatchers android.content.Context  Editable android.content.Context  	Exception android.content.Context  	Executors android.content.Context  FrameLayout android.content.Context  Gravity android.content.Context  Handler android.content.Context  IBinder android.content.Context  	ImageView android.content.Context  Int android.content.Context  Intent android.content.Context  InterruptedException android.content.Context  LinearLayout android.content.Context  LinearLayoutManager android.content.Context  List android.content.Context  ListView android.content.Context  Log android.content.Context  Long android.content.Context  Looper android.content.Context  MAX_ATTEMPTS android.content.Context  MODE_PRIVATE android.content.Context  Menu android.content.Context  MenuItem android.content.Context  NOTIFICATION_ID android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  POLLING_INTERVAL_SECONDS android.content.Context  PackageManager android.content.Context  PermissionUtils android.content.Context  PinAuthActivity android.content.Context  PixelFormat android.content.Context  ProgressBar android.content.Context  R android.content.Context  START_STICKY android.content.Context  ScheduledExecutorService android.content.Context  
SearchView android.content.Context  SettingsManager android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  Suppress android.content.Context  System android.content.Context  TAG android.content.Context  TextView android.content.Context  TextWatcher android.content.Context  TimeUnit android.content.Context  Toast android.content.Context  USAGE_STATS_SERVICE android.content.Context  Unit android.content.Context  UsageEvents android.content.Context  UsageStatsManager android.content.Context  UsageStatsMonitorService android.content.Context  View android.content.Context  Volatile android.content.Context  WINDOW_SERVICE android.content.Context  
WindowManager android.content.Context  all android.content.Context  allApps android.content.Context  android android.content.Context  appListAdapter android.content.Context  apply android.content.Context  arrayOf android.content.Context  binding android.content.Context  blockApp android.content.Context  blockAppViaActivity android.content.Context  blockedPackageName android.content.Context  cancel android.content.Context  checkFirstTimeSetup android.content.Context  checkForTamperingAttempt android.content.Context  checkPermissions android.content.Context  com android.content.Context  contains android.content.Context  contentResolver android.content.Context  count android.content.Context  createLayout android.content.Context  createNotificationChannel android.content.Context  createUI android.content.Context  deselectAllVisibleApps android.content.Context  dropLast android.content.Context  filter android.content.Context  
filterApps android.content.Context  find android.content.Context  finish android.content.Context  forEachIndexed android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getPACKAGEName android.content.Context  getPackageName android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  getTimeUntilBedtime android.content.Context  goToHome android.content.Context  handleAppSelectionChanged android.content.Context  handleBlockedApp android.content.Context  handleFailedAuth android.content.Context  handleSettingsAccess android.content.Context  handleSuccessfulAuth android.content.Context  handleWindowContentChanged android.content.Context  handleWindowStateChanged android.content.Context  instance android.content.Context  invoke android.content.Context  isAppCurrentlyBlocked android.content.Context  isBlank android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  isSystemApp android.content.Context  java android.content.Context  joinToString android.content.Context  launch android.content.Context  launchPinAuthentication android.content.Context  let android.content.Context  listOf android.content.Context  listView android.content.Context  loadApps android.content.Context  loadInstalledApps android.content.Context  	lowercase android.content.Context  map android.content.Context  monitorForegroundApp android.content.Context  
mutableListOf android.content.Context  
onBackPressed android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  onNewIntent android.content.Context  onOptionsItemSelected android.content.Context  onPause android.content.Context  onResume android.content.Context  onServiceConnected android.content.Context  openAppSelection android.content.Context  packageName android.content.Context  progressBar android.content.Context  registerForActivityResult android.content.Context  
removeOverlay android.content.Context  requestDeviceAdmin android.content.Context  requestMissingPermissions android.content.Context  requestMoreTime android.content.Context  returnResult android.content.Context  runCoreTests android.content.Context  selectAllVisibleApps android.content.Context  setContentResolver android.content.Context  setContentView android.content.Context  	setIntent android.content.Context  setPackageName android.content.Context  	setResult android.content.Context  setSupportActionBar android.content.Context  
setupListView android.content.Context  setupNumberPad android.content.Context  setupOverlayInteractions android.content.Context  setupUI android.content.Context  showAboutDialog android.content.Context  showBlockOverlay android.content.Context  showBlockedAppsCount android.content.Context  showFirstTimeSetupDialog android.content.Context  showPermissionsDialog android.content.Context  showPinSetupDialog android.content.Context  showPinVerificationDialog android.content.Context  showSystemAppWarning android.content.Context  showTemporaryAllowDialog android.content.Context  showTimeRequestDialog android.content.Context  sortedBy android.content.Context  
startActivity android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startMonitoring android.content.Context  startMonitoringIfReady android.content.Context  startMonitoringServices android.content.Context  startService android.content.Context  stopMonitoringServices android.content.Context  stopService android.content.Context  toSet android.content.Context  toString android.content.Context  toggleAppBlocking android.content.Context  updateAppInList android.content.Context  updateBlockedAppInfo android.content.Context  updateStatus android.content.Context  updateUI android.content.Context  	verifyPin android.content.Context  withContext android.content.Context  AccessibilityEvent android.content.ContextWrapper  AccessibilityServiceInfo android.content.ContextWrapper  Activity android.content.ContextWrapper  ActivityAppSelectionBinding android.content.ContextWrapper  ActivityBlockOverlayBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityPinAuthBinding android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  AppBlockAccessibilityService android.content.ContextWrapper  AppInfo android.content.ContextWrapper  AppListAdapter android.content.ContextWrapper  AppSelectionActivity android.content.ContextWrapper  ApplicationInfo android.content.ContextWrapper  BaseAdapter android.content.ContextWrapper  BlockedAppsManager android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  Button android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  CharSequence android.content.ContextWrapper  Context android.content.ContextWrapper  CoreFunctionalityTest android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  Editable android.content.ContextWrapper  	Exception android.content.ContextWrapper  	Executors android.content.ContextWrapper  FrameLayout android.content.ContextWrapper  Gravity android.content.ContextWrapper  Handler android.content.ContextWrapper  IBinder android.content.ContextWrapper  	ImageView android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  InterruptedException android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  List android.content.ContextWrapper  ListView android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  Looper android.content.ContextWrapper  MAX_ATTEMPTS android.content.ContextWrapper  Menu android.content.ContextWrapper  MenuItem android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  POLLING_INTERVAL_SECONDS android.content.ContextWrapper  PackageManager android.content.ContextWrapper  PermissionUtils android.content.ContextWrapper  PinAuthActivity android.content.ContextWrapper  PixelFormat android.content.ContextWrapper  ProgressBar android.content.ContextWrapper  R android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  ScheduledExecutorService android.content.ContextWrapper  
SearchView android.content.ContextWrapper  SettingsManager android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  Suppress android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  TextView android.content.ContextWrapper  TextWatcher android.content.ContextWrapper  TimeUnit android.content.ContextWrapper  Toast android.content.ContextWrapper  Unit android.content.ContextWrapper  UsageEvents android.content.ContextWrapper  UsageStatsManager android.content.ContextWrapper  UsageStatsMonitorService android.content.ContextWrapper  View android.content.ContextWrapper  Volatile android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  all android.content.ContextWrapper  allApps android.content.ContextWrapper  android android.content.ContextWrapper  appListAdapter android.content.ContextWrapper  apply android.content.ContextWrapper  arrayOf android.content.ContextWrapper  binding android.content.ContextWrapper  blockApp android.content.ContextWrapper  blockAppViaActivity android.content.ContextWrapper  blockedPackageName android.content.ContextWrapper  cancel android.content.ContextWrapper  checkFirstTimeSetup android.content.ContextWrapper  checkForTamperingAttempt android.content.ContextWrapper  checkPermissions android.content.ContextWrapper  com android.content.ContextWrapper  contains android.content.ContextWrapper  count android.content.ContextWrapper  createLayout android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  createUI android.content.ContextWrapper  deselectAllVisibleApps android.content.ContextWrapper  dropLast android.content.ContextWrapper  filter android.content.ContextWrapper  
filterApps android.content.ContextWrapper  find android.content.ContextWrapper  finish android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  getSystemService android.content.ContextWrapper  getTimeUntilBedtime android.content.ContextWrapper  goToHome android.content.ContextWrapper  handleAppSelectionChanged android.content.ContextWrapper  handleBlockedApp android.content.ContextWrapper  handleFailedAuth android.content.ContextWrapper  handleSettingsAccess android.content.ContextWrapper  handleSuccessfulAuth android.content.ContextWrapper  handleWindowContentChanged android.content.ContextWrapper  handleWindowStateChanged android.content.ContextWrapper  instance android.content.ContextWrapper  invoke android.content.ContextWrapper  isAppCurrentlyBlocked android.content.ContextWrapper  isBlank android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  isSystemApp android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  launch android.content.ContextWrapper  launchPinAuthentication android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  listView android.content.ContextWrapper  loadApps android.content.ContextWrapper  loadInstalledApps android.content.ContextWrapper  	lowercase android.content.ContextWrapper  map android.content.ContextWrapper  monitorForegroundApp android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  
onBackPressed android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  onNewIntent android.content.ContextWrapper  onOptionsItemSelected android.content.ContextWrapper  onPause android.content.ContextWrapper  onResume android.content.ContextWrapper  onServiceConnected android.content.ContextWrapper  openAppSelection android.content.ContextWrapper  progressBar android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  
removeOverlay android.content.ContextWrapper  requestDeviceAdmin android.content.ContextWrapper  requestMissingPermissions android.content.ContextWrapper  requestMoreTime android.content.ContextWrapper  returnResult android.content.ContextWrapper  runCoreTests android.content.ContextWrapper  selectAllVisibleApps android.content.ContextWrapper  setContentView android.content.ContextWrapper  	setIntent android.content.ContextWrapper  	setResult android.content.ContextWrapper  setSupportActionBar android.content.ContextWrapper  
setupListView android.content.ContextWrapper  setupNumberPad android.content.ContextWrapper  setupOverlayInteractions android.content.ContextWrapper  setupUI android.content.ContextWrapper  showAboutDialog android.content.ContextWrapper  showBlockOverlay android.content.ContextWrapper  showBlockedAppsCount android.content.ContextWrapper  showFirstTimeSetupDialog android.content.ContextWrapper  showPermissionsDialog android.content.ContextWrapper  showPinSetupDialog android.content.ContextWrapper  showPinVerificationDialog android.content.ContextWrapper  showSystemAppWarning android.content.ContextWrapper  showTemporaryAllowDialog android.content.ContextWrapper  showTimeRequestDialog android.content.ContextWrapper  sortedBy android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startForeground android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startMonitoring android.content.ContextWrapper  startMonitoringIfReady android.content.ContextWrapper  startMonitoringServices android.content.ContextWrapper  startService android.content.ContextWrapper  stopMonitoringServices android.content.ContextWrapper  stopService android.content.ContextWrapper  toSet android.content.ContextWrapper  toString android.content.ContextWrapper  toggleAppBlocking android.content.ContextWrapper  updateAppInList android.content.ContextWrapper  updateBlockedAppInfo android.content.ContextWrapper  updateStatus android.content.ContextWrapper  updateUI android.content.ContextWrapper  	verifyPin android.content.ContextWrapper  withContext android.content.ContextWrapper  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  ACTION_BOOT_COMPLETED android.content.Intent  ACTION_MAIN android.content.Intent  ACTION_MY_PACKAGE_REPLACED android.content.Intent  ACTION_PACKAGE_ADDED android.content.Intent  ACTION_PACKAGE_REMOVED android.content.Intent  ACTION_PACKAGE_REPLACED android.content.Intent  
CATEGORY_HOME android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  action android.content.Intent  addCategory android.content.Intent  apply android.content.Intent  blockedPackageName android.content.Intent  
dataString android.content.Intent  equals android.content.Intent  flags android.content.Intent  	getACTION android.content.Intent  getAPPLY android.content.Intent  	getAction android.content.Intent  getApply android.content.Intent  getBLOCKEDPackageName android.content.Intent  getBlockedPackageName android.content.Intent  
getDATAString android.content.Intent  
getDataString android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  getLongExtra android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  	setAction android.content.Intent  
setDataString android.content.Intent  setFlags android.content.Intent  Editor !android.content.SharedPreferences  all !android.content.SharedPreferences  edit !android.content.SharedPreferences  getALL !android.content.SharedPreferences  getAll !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  setAll !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putFloat (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  ApplicationInfo android.content.pm  PackageManager android.content.pm  FLAG_SYSTEM "android.content.pm.ApplicationInfo  flags "android.content.pm.ApplicationInfo  packageName "android.content.pm.ApplicationInfo  
GET_META_DATA !android.content.pm.PackageManager  NameNotFoundException !android.content.pm.PackageManager  PERMISSION_GRANTED !android.content.pm.PackageManager  getApplicationIcon !android.content.pm.PackageManager  getApplicationInfo !android.content.pm.PackageManager  getApplicationLabel !android.content.pm.PackageManager  getInstalledApplications !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  Color android.graphics  PixelFormat android.graphics  BLACK android.graphics.Color  GRAY android.graphics.Color  GREEN android.graphics.Color  RED android.graphics.Color  WHITE android.graphics.Color  
parseColor android.graphics.Color  TRANSLUCENT android.graphics.PixelFormat  Drawable android.graphics.drawable  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  Process 
android.os  
UserHandle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  post android.os.Handler  
getMainLooper android.os.Looper  myUid android.os.Process  Settings android.provider  ACTION_ACCESSIBILITY_SETTINGS android.provider.Settings   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  ACTION_USAGE_ACCESS_SETTINGS android.provider.Settings  Secure android.provider.Settings  SettingNotFoundException android.provider.Settings  canDrawOverlays android.provider.Settings  ACCESSIBILITY_ENABLED  android.provider.Settings.Secure  ENABLED_ACCESSIBILITY_SERVICES  android.provider.Settings.Secure  getInt  android.provider.Settings.Secure  	getString  android.provider.Settings.Secure  Editable android.text  	TextUtils android.text  TextWatcher android.text  getTOString android.text.Editable  getToString android.text.Editable  length android.text.Editable  toString android.text.Editable  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  Gravity android.view  LayoutInflater android.view  Menu android.view  MenuInflater android.view  MenuItem android.view  MotionEvent android.view  View android.view  	ViewGroup android.view  Window android.view  
WindowManager android.view  Activity  android.view.ContextThemeWrapper  ActivityAppSelectionBinding  android.view.ContextThemeWrapper  ActivityBlockOverlayBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityPinAuthBinding  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  AppInfo  android.view.ContextThemeWrapper  AppListAdapter  android.view.ContextThemeWrapper  AppSelectionActivity  android.view.ContextThemeWrapper  ApplicationInfo  android.view.ContextThemeWrapper  BaseAdapter  android.view.ContextThemeWrapper  BlockedAppsManager  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  CharSequence  android.view.ContextThemeWrapper  CoreFunctionalityTest  android.view.ContextThemeWrapper  CoroutineScope  android.view.ContextThemeWrapper  Dispatchers  android.view.ContextThemeWrapper  Editable  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  	ImageView  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  LinearLayoutManager  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  ListView  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MAX_ATTEMPTS  android.view.ContextThemeWrapper  Menu  android.view.ContextThemeWrapper  MenuItem  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  PermissionUtils  android.view.ContextThemeWrapper  PinAuthActivity  android.view.ContextThemeWrapper  ProgressBar  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  
SearchView  android.view.ContextThemeWrapper  SettingsManager  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  
SupervisorJob  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  TextWatcher  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Unit  android.view.ContextThemeWrapper  UsageStatsMonitorService  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  allApps  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  appListAdapter  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  binding  android.view.ContextThemeWrapper  blockedPackageName  android.view.ContextThemeWrapper  cancel  android.view.ContextThemeWrapper  checkFirstTimeSetup  android.view.ContextThemeWrapper  checkPermissions  android.view.ContextThemeWrapper  com  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  count  android.view.ContextThemeWrapper  createLayout  android.view.ContextThemeWrapper  createUI  android.view.ContextThemeWrapper  deselectAllVisibleApps  android.view.ContextThemeWrapper  dropLast  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  
filterApps  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  forEachIndexed  android.view.ContextThemeWrapper  getTimeUntilBedtime  android.view.ContextThemeWrapper  goToHome  android.view.ContextThemeWrapper  handleAppSelectionChanged  android.view.ContextThemeWrapper  handleFailedAuth  android.view.ContextThemeWrapper  handleSuccessfulAuth  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  isBlank  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  isSystemApp  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  launchPinAuthentication  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  listView  android.view.ContextThemeWrapper  loadApps  android.view.ContextThemeWrapper  loadInstalledApps  android.view.ContextThemeWrapper  	lowercase  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  
onBackPressed  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  	onDestroy  android.view.ContextThemeWrapper  onNewIntent  android.view.ContextThemeWrapper  onOptionsItemSelected  android.view.ContextThemeWrapper  onPause  android.view.ContextThemeWrapper  onResume  android.view.ContextThemeWrapper  openAppSelection  android.view.ContextThemeWrapper  progressBar  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  requestDeviceAdmin  android.view.ContextThemeWrapper  requestMissingPermissions  android.view.ContextThemeWrapper  requestMoreTime  android.view.ContextThemeWrapper  returnResult  android.view.ContextThemeWrapper  runCoreTests  android.view.ContextThemeWrapper  selectAllVisibleApps  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  	setIntent  android.view.ContextThemeWrapper  	setResult  android.view.ContextThemeWrapper  setSupportActionBar  android.view.ContextThemeWrapper  
setupListView  android.view.ContextThemeWrapper  setupNumberPad  android.view.ContextThemeWrapper  setupUI  android.view.ContextThemeWrapper  showAboutDialog  android.view.ContextThemeWrapper  showBlockedAppsCount  android.view.ContextThemeWrapper  showFirstTimeSetupDialog  android.view.ContextThemeWrapper  showPermissionsDialog  android.view.ContextThemeWrapper  showPinSetupDialog  android.view.ContextThemeWrapper  showPinVerificationDialog  android.view.ContextThemeWrapper  showSystemAppWarning  android.view.ContextThemeWrapper  showTemporaryAllowDialog  android.view.ContextThemeWrapper  showTimeRequestDialog  android.view.ContextThemeWrapper  sortedBy  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startForegroundService  android.view.ContextThemeWrapper  startMonitoringIfReady  android.view.ContextThemeWrapper  startMonitoringServices  android.view.ContextThemeWrapper  startService  android.view.ContextThemeWrapper  stopMonitoringServices  android.view.ContextThemeWrapper  stopService  android.view.ContextThemeWrapper  toSet  android.view.ContextThemeWrapper  toString  android.view.ContextThemeWrapper  toggleAppBlocking  android.view.ContextThemeWrapper  updateAppInList  android.view.ContextThemeWrapper  updateBlockedAppInfo  android.view.ContextThemeWrapper  updateStatus  android.view.ContextThemeWrapper  updateUI  android.view.ContextThemeWrapper  	verifyPin  android.view.ContextThemeWrapper  withContext  android.view.ContextThemeWrapper  CENTER android.view.Gravity  START android.view.Gravity  TOP android.view.Gravity  from android.view.LayoutInflater  inflate android.view.LayoutInflater  findItem android.view.Menu  inflate android.view.MenuInflater  
actionView android.view.MenuItem  
getACTIONView android.view.MenuItem  
getActionView android.view.MenuItem  	getITEMId android.view.MenuItem  	getItemId android.view.MenuItem  itemId android.view.MenuItem  
setActionView android.view.MenuItem  	setItemId android.view.MenuItem  GONE android.view.View  VISIBLE android.view.View  addTextChangedListener android.view.View  addView android.view.View  apply android.view.View  equals android.view.View  findViewById android.view.View  getLET android.view.View  getLet android.view.View  let android.view.View  requestFocus android.view.View  setBackgroundColor android.view.View  setImageDrawable android.view.View  setOnCheckedChangeListener android.view.View  setOnClickListener android.view.View  setOnItemClickListener android.view.View  setOnQueryTextListener android.view.View  setOnTouchListener android.view.View  
setPadding android.view.View  setSelection android.view.View  setText android.view.View  setTextColor android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  LayoutParams android.view.ViewGroup  addView android.view.ViewGroup  apply android.view.ViewGroup  context android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  requestFocus android.view.ViewGroup  setBackgroundColor android.view.ViewGroup  
setContext android.view.ViewGroup  setOnClickListener android.view.ViewGroup  setOnItemClickListener android.view.ViewGroup  setOnQueryTextListener android.view.ViewGroup  
setPadding android.view.ViewGroup  apply #android.view.ViewGroup.LayoutParams  
setMargins #android.view.ViewGroup.LayoutParams  apply )android.view.ViewGroup.MarginLayoutParams  
setMargins )android.view.ViewGroup.MarginLayoutParams  setFlags android.view.Window  LayoutParams android.view.WindowManager  addView android.view.WindowManager  
removeView android.view.WindowManager  Build 'android.view.WindowManager.LayoutParams  FLAG_FULLSCREEN 'android.view.WindowManager.LayoutParams  FLAG_LAYOUT_IN_SCREEN 'android.view.WindowManager.LayoutParams  FLAG_LAYOUT_NO_LIMITS 'android.view.WindowManager.LayoutParams  FLAG_NOT_FOCUSABLE 'android.view.WindowManager.LayoutParams  FLAG_SECURE 'android.view.WindowManager.LayoutParams  FLAG_SHOW_WHEN_LOCKED 'android.view.WindowManager.LayoutParams  Gravity 'android.view.WindowManager.LayoutParams  MATCH_PARENT 'android.view.WindowManager.LayoutParams  PixelFormat 'android.view.WindowManager.LayoutParams  TYPE_ACCESSIBILITY_OVERLAY 'android.view.WindowManager.LayoutParams  TYPE_SYSTEM_OVERLAY 'android.view.WindowManager.LayoutParams  
WindowManager 'android.view.WindowManager.LayoutParams  apply 'android.view.WindowManager.LayoutParams  flags 'android.view.WindowManager.LayoutParams  format 'android.view.WindowManager.LayoutParams  getAPPLY 'android.view.WindowManager.LayoutParams  getApply 'android.view.WindowManager.LayoutParams  gravity 'android.view.WindowManager.LayoutParams  height 'android.view.WindowManager.LayoutParams  type 'android.view.WindowManager.LayoutParams  width 'android.view.WindowManager.LayoutParams  AccessibilityEvent android.view.accessibility  AccessibilityNodeInfo android.view.accessibility  TYPE_WINDOW_CONTENT_CHANGED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_STATE_CHANGED -android.view.accessibility.AccessibilityEvent  	eventType -android.view.accessibility.AccessibilityEvent  getEVENTType -android.view.accessibility.AccessibilityEvent  getEventType -android.view.accessibility.AccessibilityEvent  getLET -android.view.accessibility.AccessibilityEvent  getLet -android.view.accessibility.AccessibilityEvent  getPACKAGEName -android.view.accessibility.AccessibilityEvent  getPackageName -android.view.accessibility.AccessibilityEvent  	getSOURCE -android.view.accessibility.AccessibilityEvent  	getSource -android.view.accessibility.AccessibilityEvent  let -android.view.accessibility.AccessibilityEvent  packageName -android.view.accessibility.AccessibilityEvent  setEventType -android.view.accessibility.AccessibilityEvent  setPackageName -android.view.accessibility.AccessibilityEvent  	setSource -android.view.accessibility.AccessibilityEvent  source -android.view.accessibility.AccessibilityEvent  getLET 0android.view.accessibility.AccessibilityNodeInfo  getLet 0android.view.accessibility.AccessibilityNodeInfo  getTEXT 0android.view.accessibility.AccessibilityNodeInfo  getText 0android.view.accessibility.AccessibilityNodeInfo  let 0android.view.accessibility.AccessibilityNodeInfo  setText 0android.view.accessibility.AccessibilityNodeInfo  text 0android.view.accessibility.AccessibilityNodeInfo  let .android.view.accessibility.AccessibilityRecord  AdapterView android.widget  AppInfo android.widget  BaseAdapter android.widget  BlockedAppsManager android.widget  Button android.widget  CheckBox android.widget  CompoundButton android.widget  CoreFunctionalityTest android.widget  CoroutineScope android.widget  Dispatchers android.widget  	Exception android.widget  FrameLayout android.widget  	ImageView android.widget  Intent android.widget  LinearLayout android.widget  ListAdapter android.widget  ListView android.widget  Log android.widget  PackageManager android.widget  PermissionUtils android.widget  ProgressBar android.widget  
SupervisorJob android.widget  TAG android.widget  TextView android.widget  Toast android.widget  UsageStatsMonitorService android.widget  View android.widget  allApps android.widget  android android.widget  apply android.widget  cancel android.widget  com android.widget  filter android.widget  java android.widget  launch android.widget  listView android.widget  loadInstalledApps android.widget  	lowercase android.widget  map android.widget  
mutableListOf android.widget  openAppSelection android.widget  progressBar android.widget  
setupListView android.widget  sortedBy android.widget  toSet android.widget  withContext android.widget  apply android.widget.AbsListView  setOnItemClickListener android.widget.AbsListView  apply android.widget.AdapterView  setOnItemClickListener android.widget.AdapterView  <SAM-CONSTRUCTOR> .android.widget.AdapterView.OnItemClickListener  AppInfo android.widget.BaseAdapter  	ImageView android.widget.BaseAdapter  Int android.widget.BaseAdapter  LinearLayout android.widget.BaseAdapter  Long android.widget.BaseAdapter  TextView android.widget.BaseAdapter  View android.widget.BaseAdapter  allApps android.widget.BaseAdapter  android android.widget.BaseAdapter  apply android.widget.BaseAdapter  notifyDataSetChanged android.widget.BaseAdapter  apply android.widget.Button  getAPPLY android.widget.Button  getApply android.widget.Button  getOPENAppSelection android.widget.Button  getOpenAppSelection android.widget.Button  getTEXT android.widget.Button  getTEXTSize android.widget.Button  getText android.widget.Button  getTextSize android.widget.Button  openAppSelection android.widget.Button  setOnCheckedChangeListener android.widget.Button  setOnClickListener android.widget.Button  
setPadding android.widget.Button  setText android.widget.Button  setTextSize android.widget.Button  text android.widget.Button  textSize android.widget.Button  getISChecked android.widget.CheckBox  getIsChecked android.widget.CheckBox  	isChecked android.widget.CheckBox  
setChecked android.widget.CheckBox  setOnCheckedChangeListener android.widget.CheckBox  setOnCheckedChangeListener android.widget.CompoundButton  <SAM-CONSTRUCTOR> 5android.widget.CompoundButton.OnCheckedChangeListener  addTextChangedListener android.widget.EditText  setSelection android.widget.EditText  setText android.widget.EditText  getISFocusableInTouchMode android.widget.FrameLayout  getIsFocusableInTouchMode android.widget.FrameLayout  isFocusableInTouchMode android.widget.FrameLayout  requestFocus android.widget.FrameLayout  setFocusableInTouchMode android.widget.FrameLayout  setOnClickListener android.widget.FrameLayout  LinearLayout android.widget.ImageView  apply android.widget.ImageView  getAPPLY android.widget.ImageView  getApply android.widget.ImageView  getLAYOUTParams android.widget.ImageView  getLayoutParams android.widget.ImageView  layoutParams android.widget.ImageView  setImageDrawable android.widget.ImageView  setLayoutParams android.widget.ImageView  
HORIZONTAL android.widget.LinearLayout  LayoutParams android.widget.LinearLayout  LinearLayout android.widget.LinearLayout  VERTICAL android.widget.LinearLayout  addView android.widget.LinearLayout  alpha android.widget.LinearLayout  apply android.widget.LinearLayout  context android.widget.LinearLayout  getALPHA android.widget.LinearLayout  getAPPLY android.widget.LinearLayout  getAlpha android.widget.LinearLayout  getApply android.widget.LinearLayout  
getCONTEXT android.widget.LinearLayout  
getContext android.widget.LinearLayout  getLAYOUTParams android.widget.LinearLayout  getLayoutParams android.widget.LinearLayout  getORIENTATION android.widget.LinearLayout  getOrientation android.widget.LinearLayout  layoutParams android.widget.LinearLayout  orientation android.widget.LinearLayout  setAlpha android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  
setContext android.widget.LinearLayout  setLayoutParams android.widget.LinearLayout  setOnClickListener android.widget.LinearLayout  setOrientation android.widget.LinearLayout  
setPadding android.widget.LinearLayout  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  apply (android.widget.LinearLayout.LayoutParams  getAPPLY (android.widget.LinearLayout.LayoutParams  getApply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  View android.widget.ListView  adapter android.widget.ListView  apply android.widget.ListView  
getADAPTER android.widget.ListView  getAPPLY android.widget.ListView  
getAdapter android.widget.ListView  getApply android.widget.ListView  
getVISIBILITY android.widget.ListView  
getVisibility android.widget.ListView  
setAdapter android.widget.ListView  setOnItemClickListener android.widget.ListView  
setVisibility android.widget.ListView  
visibility android.widget.ListView  View android.widget.ProgressBar  apply android.widget.ProgressBar  getAPPLY android.widget.ProgressBar  getApply android.widget.ProgressBar  
getVISIBILITY android.widget.ProgressBar  
getVisibility android.widget.ProgressBar  
setVisibility android.widget.ProgressBar  
visibility android.widget.ProgressBar  addTextChangedListener android.widget.TextView  android android.widget.TextView  apply android.widget.TextView  
getANDROID android.widget.TextView  getAPPLY android.widget.TextView  
getAndroid android.widget.TextView  getApply android.widget.TextView  
getGRAVITY android.widget.TextView  
getGravity android.widget.TextView  getTEXT android.widget.TextView  getTEXTSize android.widget.TextView  getText android.widget.TextView  getTextSize android.widget.TextView  
getVISIBILITY android.widget.TextView  
getVisibility android.widget.TextView  gravity android.widget.TextView  
setGravity android.widget.TextView  setOnCheckedChangeListener android.widget.TextView  setOnClickListener android.widget.TextView  
setPadding android.widget.TextView  setSelection android.widget.TextView  setText android.widget.TextView  setTextColor android.widget.TextView  setTextSize android.widget.TextView  
setVisibility android.widget.TextView  text android.widget.TextView  textSize android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  Activity #androidx.activity.ComponentActivity  ActivityAppSelectionBinding #androidx.activity.ComponentActivity  ActivityBlockOverlayBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityPinAuthBinding #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  AlertDialog #androidx.activity.ComponentActivity  AppInfo #androidx.activity.ComponentActivity  AppListAdapter #androidx.activity.ComponentActivity  AppSelectionActivity #androidx.activity.ComponentActivity  ApplicationInfo #androidx.activity.ComponentActivity  BlockedAppsManager #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  CharSequence #androidx.activity.ComponentActivity  CoroutineScope #androidx.activity.ComponentActivity  Dispatchers #androidx.activity.ComponentActivity  Editable #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LinearLayoutManager #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  MAX_ATTEMPTS #androidx.activity.ComponentActivity  Menu #androidx.activity.ComponentActivity  MenuItem #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  PermissionUtils #androidx.activity.ComponentActivity  PinAuthActivity #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  
SearchView #androidx.activity.ComponentActivity  SettingsManager #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  
SupervisorJob #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  TextWatcher #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  UsageStatsMonitorService #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  allApps #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  appListAdapter #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  arrayOf #androidx.activity.ComponentActivity  binding #androidx.activity.ComponentActivity  blockedPackageName #androidx.activity.ComponentActivity  cancel #androidx.activity.ComponentActivity  checkFirstTimeSetup #androidx.activity.ComponentActivity  com #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity  count #androidx.activity.ComponentActivity  deselectAllVisibleApps #androidx.activity.ComponentActivity  dropLast #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  
filterApps #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  forEachIndexed #androidx.activity.ComponentActivity  getTimeUntilBedtime #androidx.activity.ComponentActivity  goToHome #androidx.activity.ComponentActivity  handleAppSelectionChanged #androidx.activity.ComponentActivity  handleFailedAuth #androidx.activity.ComponentActivity  handleSuccessfulAuth #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  isBlank #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  isSystemApp #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  launchPinAuthentication #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  loadApps #androidx.activity.ComponentActivity  loadInstalledApps #androidx.activity.ComponentActivity  	lowercase #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  onNewIntent #androidx.activity.ComponentActivity  onOptionsItemSelected #androidx.activity.ComponentActivity  onPause #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  requestDeviceAdmin #androidx.activity.ComponentActivity  requestMissingPermissions #androidx.activity.ComponentActivity  requestMoreTime #androidx.activity.ComponentActivity  returnResult #androidx.activity.ComponentActivity  selectAllVisibleApps #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  	setIntent #androidx.activity.ComponentActivity  	setResult #androidx.activity.ComponentActivity  setSupportActionBar #androidx.activity.ComponentActivity  setupNumberPad #androidx.activity.ComponentActivity  setupUI #androidx.activity.ComponentActivity  showAboutDialog #androidx.activity.ComponentActivity  showBlockedAppsCount #androidx.activity.ComponentActivity  showFirstTimeSetupDialog #androidx.activity.ComponentActivity  showPermissionsDialog #androidx.activity.ComponentActivity  showPinSetupDialog #androidx.activity.ComponentActivity  showPinVerificationDialog #androidx.activity.ComponentActivity  showSystemAppWarning #androidx.activity.ComponentActivity  showTemporaryAllowDialog #androidx.activity.ComponentActivity  showTimeRequestDialog #androidx.activity.ComponentActivity  sortedBy #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startForegroundService #androidx.activity.ComponentActivity  startMonitoringServices #androidx.activity.ComponentActivity  startService #androidx.activity.ComponentActivity  stopMonitoringServices #androidx.activity.ComponentActivity  stopService #androidx.activity.ComponentActivity  toSet #androidx.activity.ComponentActivity  toString #androidx.activity.ComponentActivity  toggleAppBlocking #androidx.activity.ComponentActivity  updateAppInList #androidx.activity.ComponentActivity  updateBlockedAppInfo #androidx.activity.ComponentActivity  updateUI #androidx.activity.ComponentActivity  	verifyPin #androidx.activity.ComponentActivity  withContext #androidx.activity.ComponentActivity  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  getDATA 'androidx.activity.result.ActivityResult  getData 'androidx.activity.result.ActivityResult  
getRESULTCode 'androidx.activity.result.ActivityResult  
getResultCode 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  setData 'androidx.activity.result.ActivityResult  
setResultCode 'androidx.activity.result.ActivityResult  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  invoke Zandroidx.activity.result.contract.ActivityResultContracts.StartActivityForResult.Companion  	ActionBar androidx.appcompat.app  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  getTITLE  androidx.appcompat.app.ActionBar  getTitle  androidx.appcompat.app.ActionBar  setDisplayHomeAsUpEnabled  androidx.appcompat.app.ActionBar  setTitle  androidx.appcompat.app.ActionBar  title  androidx.appcompat.app.ActionBar  Builder "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  setItems *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  setView *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  Activity (androidx.appcompat.app.AppCompatActivity  ActivityAppSelectionBinding (androidx.appcompat.app.AppCompatActivity  ActivityBlockOverlayBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityPinAuthBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  AlertDialog (androidx.appcompat.app.AppCompatActivity  AppInfo (androidx.appcompat.app.AppCompatActivity  AppListAdapter (androidx.appcompat.app.AppCompatActivity  AppSelectionActivity (androidx.appcompat.app.AppCompatActivity  ApplicationInfo (androidx.appcompat.app.AppCompatActivity  BlockedAppsManager (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  CharSequence (androidx.appcompat.app.AppCompatActivity  CoroutineScope (androidx.appcompat.app.AppCompatActivity  Dispatchers (androidx.appcompat.app.AppCompatActivity  Editable (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LinearLayoutManager (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  MAX_ATTEMPTS (androidx.appcompat.app.AppCompatActivity  Menu (androidx.appcompat.app.AppCompatActivity  MenuItem (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  PermissionUtils (androidx.appcompat.app.AppCompatActivity  PinAuthActivity (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  
SearchView (androidx.appcompat.app.AppCompatActivity  SettingsManager (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  
SupervisorJob (androidx.appcompat.app.AppCompatActivity  System (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  TextWatcher (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  Unit (androidx.appcompat.app.AppCompatActivity  UsageStatsMonitorService (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
WindowManager (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  allApps (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  appListAdapter (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  arrayOf (androidx.appcompat.app.AppCompatActivity  binding (androidx.appcompat.app.AppCompatActivity  blockedPackageName (androidx.appcompat.app.AppCompatActivity  cancel (androidx.appcompat.app.AppCompatActivity  checkFirstTimeSetup (androidx.appcompat.app.AppCompatActivity  com (androidx.appcompat.app.AppCompatActivity  contains (androidx.appcompat.app.AppCompatActivity  count (androidx.appcompat.app.AppCompatActivity  deselectAllVisibleApps (androidx.appcompat.app.AppCompatActivity  dropLast (androidx.appcompat.app.AppCompatActivity  filter (androidx.appcompat.app.AppCompatActivity  
filterApps (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  forEachIndexed (androidx.appcompat.app.AppCompatActivity  getTimeUntilBedtime (androidx.appcompat.app.AppCompatActivity  goToHome (androidx.appcompat.app.AppCompatActivity  handleAppSelectionChanged (androidx.appcompat.app.AppCompatActivity  handleFailedAuth (androidx.appcompat.app.AppCompatActivity  handleSuccessfulAuth (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  isBlank (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  isSystemApp (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  launchPinAuthentication (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  loadApps (androidx.appcompat.app.AppCompatActivity  loadInstalledApps (androidx.appcompat.app.AppCompatActivity  	lowercase (androidx.appcompat.app.AppCompatActivity  map (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onNewIntent (androidx.appcompat.app.AppCompatActivity  onOptionsItemSelected (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  requestDeviceAdmin (androidx.appcompat.app.AppCompatActivity  requestMissingPermissions (androidx.appcompat.app.AppCompatActivity  requestMoreTime (androidx.appcompat.app.AppCompatActivity  returnResult (androidx.appcompat.app.AppCompatActivity  selectAllVisibleApps (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  	setIntent (androidx.appcompat.app.AppCompatActivity  	setResult (androidx.appcompat.app.AppCompatActivity  setSupportActionBar (androidx.appcompat.app.AppCompatActivity  setupNumberPad (androidx.appcompat.app.AppCompatActivity  setupUI (androidx.appcompat.app.AppCompatActivity  showAboutDialog (androidx.appcompat.app.AppCompatActivity  showBlockedAppsCount (androidx.appcompat.app.AppCompatActivity  showFirstTimeSetupDialog (androidx.appcompat.app.AppCompatActivity  showPermissionsDialog (androidx.appcompat.app.AppCompatActivity  showPinSetupDialog (androidx.appcompat.app.AppCompatActivity  showPinVerificationDialog (androidx.appcompat.app.AppCompatActivity  showSystemAppWarning (androidx.appcompat.app.AppCompatActivity  showTemporaryAllowDialog (androidx.appcompat.app.AppCompatActivity  showTimeRequestDialog (androidx.appcompat.app.AppCompatActivity  sortedBy (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startForegroundService (androidx.appcompat.app.AppCompatActivity  startMonitoringServices (androidx.appcompat.app.AppCompatActivity  startService (androidx.appcompat.app.AppCompatActivity  stopMonitoringServices (androidx.appcompat.app.AppCompatActivity  stopService (androidx.appcompat.app.AppCompatActivity  toSet (androidx.appcompat.app.AppCompatActivity  toString (androidx.appcompat.app.AppCompatActivity  toggleAppBlocking (androidx.appcompat.app.AppCompatActivity  updateAppInList (androidx.appcompat.app.AppCompatActivity  updateBlockedAppInfo (androidx.appcompat.app.AppCompatActivity  updateUI (androidx.appcompat.app.AppCompatActivity  	verifyPin (androidx.appcompat.app.AppCompatActivity  withContext (androidx.appcompat.app.AppCompatActivity  
SearchView androidx.appcompat.widget  Toolbar androidx.appcompat.widget  setOnClickListener )androidx.appcompat.widget.AppCompatButton  addTextChangedListener +androidx.appcompat.widget.AppCompatEditText  setSelection +androidx.appcompat.widget.AppCompatEditText  setText +androidx.appcompat.widget.AppCompatEditText  setOnQueryTextListener ,androidx.appcompat.widget.LinearLayoutCompat  OnQueryTextListener $androidx.appcompat.widget.SearchView  setOnQueryTextListener $androidx.appcompat.widget.SearchView  setOnCheckedChangeListener &androidx.appcompat.widget.SwitchCompat  setOnClickListener !androidx.cardview.widget.CardView  CoordinatorLayout !androidx.coordinatorlayout.widget  NotificationCompat androidx.core.app  Activity #androidx.core.app.ComponentActivity  ActivityAppSelectionBinding #androidx.core.app.ComponentActivity  ActivityBlockOverlayBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityPinAuthBinding #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  AlertDialog #androidx.core.app.ComponentActivity  AppInfo #androidx.core.app.ComponentActivity  AppListAdapter #androidx.core.app.ComponentActivity  AppSelectionActivity #androidx.core.app.ComponentActivity  ApplicationInfo #androidx.core.app.ComponentActivity  BlockedAppsManager #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CharSequence #androidx.core.app.ComponentActivity  CoroutineScope #androidx.core.app.ComponentActivity  Dispatchers #androidx.core.app.ComponentActivity  Editable #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LinearLayoutManager #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  MAX_ATTEMPTS #androidx.core.app.ComponentActivity  Menu #androidx.core.app.ComponentActivity  MenuItem #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  PermissionUtils #androidx.core.app.ComponentActivity  PinAuthActivity #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  
SearchView #androidx.core.app.ComponentActivity  SettingsManager #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
SupervisorJob #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  TextWatcher #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  UsageStatsMonitorService #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  allApps #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  appListAdapter #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  arrayOf #androidx.core.app.ComponentActivity  binding #androidx.core.app.ComponentActivity  blockedPackageName #androidx.core.app.ComponentActivity  cancel #androidx.core.app.ComponentActivity  checkFirstTimeSetup #androidx.core.app.ComponentActivity  com #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity  count #androidx.core.app.ComponentActivity  deselectAllVisibleApps #androidx.core.app.ComponentActivity  dropLast #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  
filterApps #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  forEachIndexed #androidx.core.app.ComponentActivity  getTimeUntilBedtime #androidx.core.app.ComponentActivity  goToHome #androidx.core.app.ComponentActivity  handleAppSelectionChanged #androidx.core.app.ComponentActivity  handleFailedAuth #androidx.core.app.ComponentActivity  handleSuccessfulAuth #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  isBlank #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  isSystemApp #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  launchPinAuthentication #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  loadApps #androidx.core.app.ComponentActivity  loadInstalledApps #androidx.core.app.ComponentActivity  	lowercase #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  
onBackPressed #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  	onDestroy #androidx.core.app.ComponentActivity  onNewIntent #androidx.core.app.ComponentActivity  onOptionsItemSelected #androidx.core.app.ComponentActivity  onPause #androidx.core.app.ComponentActivity  onResume #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  requestDeviceAdmin #androidx.core.app.ComponentActivity  requestMissingPermissions #androidx.core.app.ComponentActivity  requestMoreTime #androidx.core.app.ComponentActivity  returnResult #androidx.core.app.ComponentActivity  selectAllVisibleApps #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  	setIntent #androidx.core.app.ComponentActivity  	setResult #androidx.core.app.ComponentActivity  setSupportActionBar #androidx.core.app.ComponentActivity  setupNumberPad #androidx.core.app.ComponentActivity  setupUI #androidx.core.app.ComponentActivity  showAboutDialog #androidx.core.app.ComponentActivity  showBlockedAppsCount #androidx.core.app.ComponentActivity  showFirstTimeSetupDialog #androidx.core.app.ComponentActivity  showPermissionsDialog #androidx.core.app.ComponentActivity  showPinSetupDialog #androidx.core.app.ComponentActivity  showPinVerificationDialog #androidx.core.app.ComponentActivity  showSystemAppWarning #androidx.core.app.ComponentActivity  showTemporaryAllowDialog #androidx.core.app.ComponentActivity  showTimeRequestDialog #androidx.core.app.ComponentActivity  sortedBy #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startForegroundService #androidx.core.app.ComponentActivity  startMonitoringServices #androidx.core.app.ComponentActivity  startService #androidx.core.app.ComponentActivity  stopMonitoringServices #androidx.core.app.ComponentActivity  stopService #androidx.core.app.ComponentActivity  toSet #androidx.core.app.ComponentActivity  toString #androidx.core.app.ComponentActivity  toggleAppBlocking #androidx.core.app.ComponentActivity  updateAppInList #androidx.core.app.ComponentActivity  updateBlockedAppInfo #androidx.core.app.ComponentActivity  updateUI #androidx.core.app.ComponentActivity  	verifyPin #androidx.core.app.ComponentActivity  withContext #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getColor #androidx.core.content.ContextCompat  Activity &androidx.fragment.app.FragmentActivity  ActivityAppSelectionBinding &androidx.fragment.app.FragmentActivity  ActivityBlockOverlayBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityPinAuthBinding &androidx.fragment.app.FragmentActivity  ActivityResultContracts &androidx.fragment.app.FragmentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  AppInfo &androidx.fragment.app.FragmentActivity  AppListAdapter &androidx.fragment.app.FragmentActivity  AppSelectionActivity &androidx.fragment.app.FragmentActivity  ApplicationInfo &androidx.fragment.app.FragmentActivity  BlockedAppsManager &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  CharSequence &androidx.fragment.app.FragmentActivity  CoroutineScope &androidx.fragment.app.FragmentActivity  Dispatchers &androidx.fragment.app.FragmentActivity  Editable &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LinearLayoutManager &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  MAX_ATTEMPTS &androidx.fragment.app.FragmentActivity  Menu &androidx.fragment.app.FragmentActivity  MenuItem &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  PermissionUtils &androidx.fragment.app.FragmentActivity  PinAuthActivity &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  
SearchView &androidx.fragment.app.FragmentActivity  SettingsManager &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  
SupervisorJob &androidx.fragment.app.FragmentActivity  System &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  TextWatcher &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  Unit &androidx.fragment.app.FragmentActivity  UsageStatsMonitorService &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
WindowManager &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  allApps &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  appListAdapter &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  arrayOf &androidx.fragment.app.FragmentActivity  binding &androidx.fragment.app.FragmentActivity  blockedPackageName &androidx.fragment.app.FragmentActivity  cancel &androidx.fragment.app.FragmentActivity  checkFirstTimeSetup &androidx.fragment.app.FragmentActivity  com &androidx.fragment.app.FragmentActivity  contains &androidx.fragment.app.FragmentActivity  count &androidx.fragment.app.FragmentActivity  deselectAllVisibleApps &androidx.fragment.app.FragmentActivity  dropLast &androidx.fragment.app.FragmentActivity  filter &androidx.fragment.app.FragmentActivity  
filterApps &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  forEachIndexed &androidx.fragment.app.FragmentActivity  getTimeUntilBedtime &androidx.fragment.app.FragmentActivity  goToHome &androidx.fragment.app.FragmentActivity  handleAppSelectionChanged &androidx.fragment.app.FragmentActivity  handleFailedAuth &androidx.fragment.app.FragmentActivity  handleSuccessfulAuth &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  isBlank &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  isSystemApp &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  launchPinAuthentication &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  loadApps &androidx.fragment.app.FragmentActivity  loadInstalledApps &androidx.fragment.app.FragmentActivity  	lowercase &androidx.fragment.app.FragmentActivity  map &androidx.fragment.app.FragmentActivity  
onBackPressed &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  	onDestroy &androidx.fragment.app.FragmentActivity  onNewIntent &androidx.fragment.app.FragmentActivity  onOptionsItemSelected &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  requestDeviceAdmin &androidx.fragment.app.FragmentActivity  requestMissingPermissions &androidx.fragment.app.FragmentActivity  requestMoreTime &androidx.fragment.app.FragmentActivity  returnResult &androidx.fragment.app.FragmentActivity  selectAllVisibleApps &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  	setIntent &androidx.fragment.app.FragmentActivity  	setResult &androidx.fragment.app.FragmentActivity  setSupportActionBar &androidx.fragment.app.FragmentActivity  setupNumberPad &androidx.fragment.app.FragmentActivity  setupUI &androidx.fragment.app.FragmentActivity  showAboutDialog &androidx.fragment.app.FragmentActivity  showBlockedAppsCount &androidx.fragment.app.FragmentActivity  showFirstTimeSetupDialog &androidx.fragment.app.FragmentActivity  showPermissionsDialog &androidx.fragment.app.FragmentActivity  showPinSetupDialog &androidx.fragment.app.FragmentActivity  showPinVerificationDialog &androidx.fragment.app.FragmentActivity  showSystemAppWarning &androidx.fragment.app.FragmentActivity  showTemporaryAllowDialog &androidx.fragment.app.FragmentActivity  showTimeRequestDialog &androidx.fragment.app.FragmentActivity  sortedBy &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startForegroundService &androidx.fragment.app.FragmentActivity  startMonitoringServices &androidx.fragment.app.FragmentActivity  startService &androidx.fragment.app.FragmentActivity  stopMonitoringServices &androidx.fragment.app.FragmentActivity  stopService &androidx.fragment.app.FragmentActivity  toSet &androidx.fragment.app.FragmentActivity  toString &androidx.fragment.app.FragmentActivity  toggleAppBlocking &androidx.fragment.app.FragmentActivity  updateAppInList &androidx.fragment.app.FragmentActivity  updateBlockedAppInfo &androidx.fragment.app.FragmentActivity  updateUI &androidx.fragment.app.FragmentActivity  	verifyPin &androidx.fragment.app.FragmentActivity  withContext &androidx.fragment.app.FragmentActivity  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  AppSelectionActivity 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  AppSelectionActivity (androidx.recyclerview.widget.ListAdapter  Boolean (androidx.recyclerview.widget.ListAdapter  DiffUtil (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  ItemAppBinding (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  android (androidx.recyclerview.widget.ListAdapter  androidx (androidx.recyclerview.widget.ListAdapter  apply (androidx.recyclerview.widget.ListAdapter  com (androidx.recyclerview.widget.ListAdapter  getItem (androidx.recyclerview.widget.ListAdapter  invoke (androidx.recyclerview.widget.ListAdapter  notifyDataSetChanged (androidx.recyclerview.widget.ListAdapter  notifyItemChanged (androidx.recyclerview.widget.ListAdapter  onAppSelectionChanged (androidx.recyclerview.widget.ListAdapter  
submitList (androidx.recyclerview.widget.ListAdapter  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  appListAdapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  getAPPLY )androidx.recyclerview.widget.RecyclerView  getAPPListAdapter )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getAppListAdapter )androidx.recyclerview.widget.RecyclerView  getApply )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  
getVISIBILITY )androidx.recyclerview.widget.RecyclerView  
getVisibility )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  
setVisibility )androidx.recyclerview.widget.RecyclerView  
visibility )androidx.recyclerview.widget.RecyclerView  AppSelectionActivity 1androidx.recyclerview.widget.RecyclerView.Adapter  Boolean 1androidx.recyclerview.widget.RecyclerView.Adapter  DiffUtil 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemAppBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  android 1androidx.recyclerview.widget.RecyclerView.Adapter  androidx 1androidx.recyclerview.widget.RecyclerView.Adapter  apply 1androidx.recyclerview.widget.RecyclerView.Adapter  com 1androidx.recyclerview.widget.RecyclerView.Adapter  getItem 1androidx.recyclerview.widget.RecyclerView.Adapter  invoke 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyDataSetChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyItemChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  onAppSelectionChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  
submitList 1androidx.recyclerview.widget.RecyclerView.Adapter  AppSelectionActivity 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemAppBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  android 4androidx.recyclerview.widget.RecyclerView.ViewHolder  androidx 4androidx.recyclerview.widget.RecyclerView.ViewHolder  apply 4androidx.recyclerview.widget.RecyclerView.ViewHolder  bind 4androidx.recyclerview.widget.RecyclerView.ViewHolder  com 4androidx.recyclerview.widget.RecyclerView.ViewHolder  invoke 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onAppSelectionChanged 4androidx.recyclerview.widget.RecyclerView.ViewHolder  BlockedAppsManager com.appblock  Button com.appblock  CoreFunctionalityTest com.appblock  	Exception com.appblock  Intent com.appblock  LinearLayout com.appblock  Log com.appblock  MainActivity com.appblock  PermissionUtils com.appblock  R com.appblock  TAG com.appblock  TextView com.appblock  Toast com.appblock  UsageStatsMonitorService com.appblock  android com.appblock  apply com.appblock  com com.appblock  java com.appblock  openAppSelection com.appblock  BlockedAppsManager com.appblock.MainActivity  Bundle com.appblock.MainActivity  Button com.appblock.MainActivity  CoreFunctionalityTest com.appblock.MainActivity  	Exception com.appblock.MainActivity  Intent com.appblock.MainActivity  LinearLayout com.appblock.MainActivity  Log com.appblock.MainActivity  PermissionUtils com.appblock.MainActivity  TAG com.appblock.MainActivity  TextView com.appblock.MainActivity  Toast com.appblock.MainActivity  UsageStatsMonitorService com.appblock.MainActivity  android com.appblock.MainActivity  apply com.appblock.MainActivity  checkPermissions com.appblock.MainActivity  com com.appblock.MainActivity  createUI com.appblock.MainActivity  
getANDROID com.appblock.MainActivity  getAPPLY com.appblock.MainActivity  
getAndroid com.appblock.MainActivity  getApply com.appblock.MainActivity  getCOM com.appblock.MainActivity  getCom com.appblock.MainActivity  invoke com.appblock.MainActivity  java com.appblock.MainActivity  openAppSelection com.appblock.MainActivity  runCoreTests com.appblock.MainActivity  setContentView com.appblock.MainActivity  
startActivity com.appblock.MainActivity  startForegroundService com.appblock.MainActivity  startMonitoringIfReady com.appblock.MainActivity  startService com.appblock.MainActivity  updateStatus com.appblock.MainActivity  BlockedAppsManager #com.appblock.MainActivity.Companion  Bundle #com.appblock.MainActivity.Companion  Button #com.appblock.MainActivity.Companion  CoreFunctionalityTest #com.appblock.MainActivity.Companion  	Exception #com.appblock.MainActivity.Companion  Intent #com.appblock.MainActivity.Companion  LinearLayout #com.appblock.MainActivity.Companion  Log #com.appblock.MainActivity.Companion  PermissionUtils #com.appblock.MainActivity.Companion  TAG #com.appblock.MainActivity.Companion  TextView #com.appblock.MainActivity.Companion  Toast #com.appblock.MainActivity.Companion  UsageStatsMonitorService #com.appblock.MainActivity.Companion  android #com.appblock.MainActivity.Companion  apply #com.appblock.MainActivity.Companion  com #com.appblock.MainActivity.Companion  
getANDROID #com.appblock.MainActivity.Companion  getAPPLY #com.appblock.MainActivity.Companion  
getAndroid #com.appblock.MainActivity.Companion  getApply #com.appblock.MainActivity.Companion  getCOM #com.appblock.MainActivity.Companion  getCom #com.appblock.MainActivity.Companion  invoke #com.appblock.MainActivity.Companion  java #com.appblock.MainActivity.Companion  openAppSelection #com.appblock.MainActivity.Companion  color com.appblock.R  id com.appblock.R  layout com.appblock.R  menu com.appblock.R  blocked_app_background com.appblock.R.color  action_about com.appblock.R.id  action_blocked_count com.appblock.R.id  action_deselect_all com.appblock.R.id  action_logs com.appblock.R.id  
action_search com.appblock.R.id  action_select_all com.appblock.R.id  et_confirm_pin com.appblock.R.id  et_pin com.appblock.R.id  dialog_pin_setup com.appblock.R.layout  dialog_pin_verify com.appblock.R.layout  app_selection_menu com.appblock.R.menu  	main_menu com.appblock.R.menu  Any com.appblock.data  BlockedAppsManager com.appblock.data  Boolean com.appblock.data  ConcurrentHashMap com.appblock.data  Context com.appblock.data  	Exception com.appblock.data  Float com.appblock.data  Gson com.appblock.data  Int com.appblock.data  KEY_APP_BLOCKING_ENABLED com.appblock.data  KEY_BLOCKED_APPS com.appblock.data  KEY_BOOT_EVENTS com.appblock.data  KEY_DEVICE_ADMIN_ENABLED com.appblock.data  KEY_FIRST_SETUP_COMPLETE com.appblock.data  KEY_HIDE_APP_ICON com.appblock.data  KEY_LAST_ACTIVITY com.appblock.data  KEY_LOCK_ON_TAMPERING com.appblock.data  KEY_PARENT_PIN com.appblock.data  KEY_SETTINGS_BLOCKED com.appblock.data  KEY_TAMPERING_ATTEMPTS com.appblock.data  KEY_TEMPORARY_ALLOWS com.appblock.data  KEY_TIME_LIMITS com.appblock.data  List com.appblock.data  Log com.appblock.data  Long com.appblock.data  Map com.appblock.data  
PREFS_NAME com.appblock.data  SettingsManager com.appblock.data  String com.appblock.data  System com.appblock.data  TAG com.appblock.data  
TimeLimitInfo com.appblock.data  
component1 com.appblock.data  
component2 com.appblock.data  	emptyList com.appblock.data  emptyMap com.appblock.data  filter com.appblock.data  filterValues com.appblock.data  forEach com.appblock.data  java com.appblock.data  maxOf com.appblock.data  set com.appblock.data  toList com.appblock.data  toMap com.appblock.data  
toMutableList com.appblock.data  Boolean $com.appblock.data.BlockedAppsManager  ConcurrentHashMap $com.appblock.data.BlockedAppsManager  Context $com.appblock.data.BlockedAppsManager  	Exception $com.appblock.data.BlockedAppsManager  Gson $com.appblock.data.BlockedAppsManager  KEY_BLOCKED_APPS $com.appblock.data.BlockedAppsManager  KEY_SETTINGS_BLOCKED $com.appblock.data.BlockedAppsManager  KEY_TEMPORARY_ALLOWS $com.appblock.data.BlockedAppsManager  KEY_TIME_LIMITS $com.appblock.data.BlockedAppsManager  List $com.appblock.data.BlockedAppsManager  Log $com.appblock.data.BlockedAppsManager  Long $com.appblock.data.BlockedAppsManager  Map $com.appblock.data.BlockedAppsManager  
PREFS_NAME $com.appblock.data.BlockedAppsManager  SharedPreferences $com.appblock.data.BlockedAppsManager  String $com.appblock.data.BlockedAppsManager  System $com.appblock.data.BlockedAppsManager  TAG $com.appblock.data.BlockedAppsManager  
TimeLimitInfo $com.appblock.data.BlockedAppsManager  	TypeToken $com.appblock.data.BlockedAppsManager  
addBlockedApp $com.appblock.data.BlockedAppsManager  blockedAppsCache $com.appblock.data.BlockedAppsManager  context $com.appblock.data.BlockedAppsManager  	emptyList $com.appblock.data.BlockedAppsManager  emptyMap $com.appblock.data.BlockedAppsManager  filterValues $com.appblock.data.BlockedAppsManager  getBlockedApps $com.appblock.data.BlockedAppsManager  getCurrentDate $com.appblock.data.BlockedAppsManager  getCurrentDateStatic $com.appblock.data.BlockedAppsManager  getEMPTYList $com.appblock.data.BlockedAppsManager  getEMPTYMap $com.appblock.data.BlockedAppsManager  getEmptyList $com.appblock.data.BlockedAppsManager  getEmptyMap $com.appblock.data.BlockedAppsManager  getFILTERValues $com.appblock.data.BlockedAppsManager  getFilterValues $com.appblock.data.BlockedAppsManager  getJAVA $com.appblock.data.BlockedAppsManager  getJava $com.appblock.data.BlockedAppsManager  getMAXOf $com.appblock.data.BlockedAppsManager  getMaxOf $com.appblock.data.BlockedAppsManager  getRemainingTime $com.appblock.data.BlockedAppsManager  getSET $com.appblock.data.BlockedAppsManager  getSet $com.appblock.data.BlockedAppsManager  	getTOList $com.appblock.data.BlockedAppsManager  getTOMap $com.appblock.data.BlockedAppsManager  	getToList $com.appblock.data.BlockedAppsManager  getToMap $com.appblock.data.BlockedAppsManager  gson $com.appblock.data.BlockedAppsManager  isAppBlocked $com.appblock.data.BlockedAppsManager  isAppOverTimeLimit $com.appblock.data.BlockedAppsManager  isSettingsBlocked $com.appblock.data.BlockedAppsManager  java $com.appblock.data.BlockedAppsManager  
loadCaches $com.appblock.data.BlockedAppsManager  maxOf $com.appblock.data.BlockedAppsManager  prefs $com.appblock.data.BlockedAppsManager  removeBlockedApp $com.appblock.data.BlockedAppsManager  saveBlockedApps $com.appblock.data.BlockedAppsManager  saveTemporaryAllows $com.appblock.data.BlockedAppsManager  saveTimeLimits $com.appblock.data.BlockedAppsManager  set $com.appblock.data.BlockedAppsManager  setTimeLimit $com.appblock.data.BlockedAppsManager  temporarilyAllowApp $com.appblock.data.BlockedAppsManager  temporaryAllowsCache $com.appblock.data.BlockedAppsManager  timeLimitsCache $com.appblock.data.BlockedAppsManager  toList $com.appblock.data.BlockedAppsManager  toMap $com.appblock.data.BlockedAppsManager  Boolean .com.appblock.data.BlockedAppsManager.Companion  ConcurrentHashMap .com.appblock.data.BlockedAppsManager.Companion  Context .com.appblock.data.BlockedAppsManager.Companion  	Exception .com.appblock.data.BlockedAppsManager.Companion  Gson .com.appblock.data.BlockedAppsManager.Companion  KEY_BLOCKED_APPS .com.appblock.data.BlockedAppsManager.Companion  KEY_SETTINGS_BLOCKED .com.appblock.data.BlockedAppsManager.Companion  KEY_TEMPORARY_ALLOWS .com.appblock.data.BlockedAppsManager.Companion  KEY_TIME_LIMITS .com.appblock.data.BlockedAppsManager.Companion  List .com.appblock.data.BlockedAppsManager.Companion  Log .com.appblock.data.BlockedAppsManager.Companion  Long .com.appblock.data.BlockedAppsManager.Companion  Map .com.appblock.data.BlockedAppsManager.Companion  
PREFS_NAME .com.appblock.data.BlockedAppsManager.Companion  SharedPreferences .com.appblock.data.BlockedAppsManager.Companion  String .com.appblock.data.BlockedAppsManager.Companion  System .com.appblock.data.BlockedAppsManager.Companion  TAG .com.appblock.data.BlockedAppsManager.Companion  
TimeLimitInfo .com.appblock.data.BlockedAppsManager.Companion  	TypeToken .com.appblock.data.BlockedAppsManager.Companion  	emptyList .com.appblock.data.BlockedAppsManager.Companion  emptyMap .com.appblock.data.BlockedAppsManager.Companion  filterValues .com.appblock.data.BlockedAppsManager.Companion  getCurrentDateStatic .com.appblock.data.BlockedAppsManager.Companion  getEMPTYList .com.appblock.data.BlockedAppsManager.Companion  getEMPTYMap .com.appblock.data.BlockedAppsManager.Companion  getEmptyList .com.appblock.data.BlockedAppsManager.Companion  getEmptyMap .com.appblock.data.BlockedAppsManager.Companion  getFILTERValues .com.appblock.data.BlockedAppsManager.Companion  getFilterValues .com.appblock.data.BlockedAppsManager.Companion  getJAVA .com.appblock.data.BlockedAppsManager.Companion  getJava .com.appblock.data.BlockedAppsManager.Companion  getMAXOf .com.appblock.data.BlockedAppsManager.Companion  getMaxOf .com.appblock.data.BlockedAppsManager.Companion  getSET .com.appblock.data.BlockedAppsManager.Companion  getSet .com.appblock.data.BlockedAppsManager.Companion  	getTOList .com.appblock.data.BlockedAppsManager.Companion  getTOMap .com.appblock.data.BlockedAppsManager.Companion  	getToList .com.appblock.data.BlockedAppsManager.Companion  getToMap .com.appblock.data.BlockedAppsManager.Companion  invoke .com.appblock.data.BlockedAppsManager.Companion  java .com.appblock.data.BlockedAppsManager.Companion  maxOf .com.appblock.data.BlockedAppsManager.Companion  set .com.appblock.data.BlockedAppsManager.Companion  toList .com.appblock.data.BlockedAppsManager.Companion  toMap .com.appblock.data.BlockedAppsManager.Companion  Long 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  String 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  copy 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  dailyLimitMs 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  getCurrentDateStatic 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  
lastResetDate 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  usedTodayMs 2com.appblock.data.BlockedAppsManager.TimeLimitInfo  getTYPE Bcom.appblock.data.BlockedAppsManager.loadCaches.<no name provided>  getType Bcom.appblock.data.BlockedAppsManager.loadCaches.<no name provided>  setType Bcom.appblock.data.BlockedAppsManager.loadCaches.<no name provided>  Any !com.appblock.data.SettingsManager  Boolean !com.appblock.data.SettingsManager  Context !com.appblock.data.SettingsManager  	Exception !com.appblock.data.SettingsManager  Float !com.appblock.data.SettingsManager  Gson !com.appblock.data.SettingsManager  Int !com.appblock.data.SettingsManager  KEY_APP_BLOCKING_ENABLED !com.appblock.data.SettingsManager  KEY_BOOT_EVENTS !com.appblock.data.SettingsManager  KEY_DEVICE_ADMIN_ENABLED !com.appblock.data.SettingsManager  KEY_FIRST_SETUP_COMPLETE !com.appblock.data.SettingsManager  KEY_HIDE_APP_ICON !com.appblock.data.SettingsManager  KEY_LAST_ACTIVITY !com.appblock.data.SettingsManager  KEY_LOCK_ON_TAMPERING !com.appblock.data.SettingsManager  KEY_PARENT_PIN !com.appblock.data.SettingsManager  KEY_TAMPERING_ATTEMPTS !com.appblock.data.SettingsManager  List !com.appblock.data.SettingsManager  Log !com.appblock.data.SettingsManager  Long !com.appblock.data.SettingsManager  Map !com.appblock.data.SettingsManager  
PREFS_NAME !com.appblock.data.SettingsManager  SharedPreferences !com.appblock.data.SettingsManager  String !com.appblock.data.SettingsManager  System !com.appblock.data.SettingsManager  TAG !com.appblock.data.SettingsManager  	TypeToken !com.appblock.data.SettingsManager  
component1 !com.appblock.data.SettingsManager  
component2 !com.appblock.data.SettingsManager  context !com.appblock.data.SettingsManager  	emptyList !com.appblock.data.SettingsManager  filter !com.appblock.data.SettingsManager  
getBootEvents !com.appblock.data.SettingsManager  
getComponent1 !com.appblock.data.SettingsManager  
getComponent2 !com.appblock.data.SettingsManager  getEMPTYList !com.appblock.data.SettingsManager  getEmptyList !com.appblock.data.SettingsManager  	getFILTER !com.appblock.data.SettingsManager  	getFilter !com.appblock.data.SettingsManager  getTOMutableList !com.appblock.data.SettingsManager  getTamperingAttempts !com.appblock.data.SettingsManager  getTamperingAttemptsCount !com.appblock.data.SettingsManager  getToMutableList !com.appblock.data.SettingsManager  gson !com.appblock.data.SettingsManager  hasParentPin !com.appblock.data.SettingsManager  hashPin !com.appblock.data.SettingsManager  isAppBlockingEnabled !com.appblock.data.SettingsManager  isFirstSetupComplete !com.appblock.data.SettingsManager  prefs !com.appblock.data.SettingsManager  recordBootEvent !com.appblock.data.SettingsManager  recordTamperingAttempt !com.appblock.data.SettingsManager  setAppBlockingEnabled !com.appblock.data.SettingsManager  setDeviceAdminEnabled !com.appblock.data.SettingsManager  setParentPin !com.appblock.data.SettingsManager  shouldLockOnTampering !com.appblock.data.SettingsManager  
toMutableList !com.appblock.data.SettingsManager  updateLastActivity !com.appblock.data.SettingsManager  verifyParentPin !com.appblock.data.SettingsManager  Any +com.appblock.data.SettingsManager.Companion  Boolean +com.appblock.data.SettingsManager.Companion  Context +com.appblock.data.SettingsManager.Companion  	Exception +com.appblock.data.SettingsManager.Companion  Float +com.appblock.data.SettingsManager.Companion  Gson +com.appblock.data.SettingsManager.Companion  Int +com.appblock.data.SettingsManager.Companion  KEY_APP_BLOCKING_ENABLED +com.appblock.data.SettingsManager.Companion  KEY_BOOT_EVENTS +com.appblock.data.SettingsManager.Companion  KEY_DEVICE_ADMIN_ENABLED +com.appblock.data.SettingsManager.Companion  KEY_FIRST_SETUP_COMPLETE +com.appblock.data.SettingsManager.Companion  KEY_HIDE_APP_ICON +com.appblock.data.SettingsManager.Companion  KEY_LAST_ACTIVITY +com.appblock.data.SettingsManager.Companion  KEY_LOCK_ON_TAMPERING +com.appblock.data.SettingsManager.Companion  KEY_PARENT_PIN +com.appblock.data.SettingsManager.Companion  KEY_TAMPERING_ATTEMPTS +com.appblock.data.SettingsManager.Companion  List +com.appblock.data.SettingsManager.Companion  Log +com.appblock.data.SettingsManager.Companion  Long +com.appblock.data.SettingsManager.Companion  Map +com.appblock.data.SettingsManager.Companion  
PREFS_NAME +com.appblock.data.SettingsManager.Companion  SharedPreferences +com.appblock.data.SettingsManager.Companion  String +com.appblock.data.SettingsManager.Companion  System +com.appblock.data.SettingsManager.Companion  TAG +com.appblock.data.SettingsManager.Companion  	TypeToken +com.appblock.data.SettingsManager.Companion  
component1 +com.appblock.data.SettingsManager.Companion  
component2 +com.appblock.data.SettingsManager.Companion  	emptyList +com.appblock.data.SettingsManager.Companion  filter +com.appblock.data.SettingsManager.Companion  
getComponent1 +com.appblock.data.SettingsManager.Companion  
getComponent2 +com.appblock.data.SettingsManager.Companion  getEMPTYList +com.appblock.data.SettingsManager.Companion  getEmptyList +com.appblock.data.SettingsManager.Companion  	getFILTER +com.appblock.data.SettingsManager.Companion  	getFilter +com.appblock.data.SettingsManager.Companion  getTOMutableList +com.appblock.data.SettingsManager.Companion  getToMutableList +com.appblock.data.SettingsManager.Companion  invoke +com.appblock.data.SettingsManager.Companion  
toMutableList +com.appblock.data.SettingsManager.Companion  getTYPE Bcom.appblock.data.SettingsManager.getBootEvents.<no name provided>  getType Bcom.appblock.data.SettingsManager.getBootEvents.<no name provided>  setType Bcom.appblock.data.SettingsManager.getBootEvents.<no name provided>  getTYPE Icom.appblock.data.SettingsManager.getTamperingAttempts.<no name provided>  getType Icom.appblock.data.SettingsManager.getTamperingAttempts.<no name provided>  setType Icom.appblock.data.SettingsManager.getTamperingAttempts.<no name provided>  getTYPE Ccom.appblock.data.SettingsManager.importSettings.<no name provided>  getType Ccom.appblock.data.SettingsManager.importSettings.<no name provided>  setType Ccom.appblock.data.SettingsManager.importSettings.<no name provided>  ActivityAppSelectionBinding com.appblock.databinding  ActivityBlockOverlayBinding com.appblock.databinding  ActivityMainBinding com.appblock.databinding  ActivityPinAuthBinding com.appblock.databinding  ItemAppBinding com.appblock.databinding  
btnShowAll 4com.appblock.databinding.ActivityAppSelectionBinding  btnShowSystemApps 4com.appblock.databinding.ActivityAppSelectionBinding  btnShowUserApps 4com.appblock.databinding.ActivityAppSelectionBinding  getROOT 4com.appblock.databinding.ActivityAppSelectionBinding  getRoot 4com.appblock.databinding.ActivityAppSelectionBinding  inflate 4com.appblock.databinding.ActivityAppSelectionBinding  progressBar 4com.appblock.databinding.ActivityAppSelectionBinding  recyclerView 4com.appblock.databinding.ActivityAppSelectionBinding  root 4com.appblock.databinding.ActivityAppSelectionBinding  setRoot 4com.appblock.databinding.ActivityAppSelectionBinding  toolbar 4com.appblock.databinding.ActivityAppSelectionBinding  
tvAppCount 4com.appblock.databinding.ActivityAppSelectionBinding  btnEnterPin 4com.appblock.databinding.ActivityBlockOverlayBinding  	btnGoHome 4com.appblock.databinding.ActivityBlockOverlayBinding  btnRequestTime 4com.appblock.databinding.ActivityBlockOverlayBinding  getROOT 4com.appblock.databinding.ActivityBlockOverlayBinding  getRoot 4com.appblock.databinding.ActivityBlockOverlayBinding  inflate 4com.appblock.databinding.ActivityBlockOverlayBinding  	ivAppIcon 4com.appblock.databinding.ActivityBlockOverlayBinding  root 4com.appblock.databinding.ActivityBlockOverlayBinding  setRoot 4com.appblock.databinding.ActivityBlockOverlayBinding  	tvAppName 4com.appblock.databinding.ActivityBlockOverlayBinding  	tvMessage 4com.appblock.databinding.ActivityBlockOverlayBinding  tvRemainingTime 4com.appblock.databinding.ActivityBlockOverlayBinding  tvSource 4com.appblock.databinding.ActivityBlockOverlayBinding  btnDeviceAdmin ,com.appblock.databinding.ActivityMainBinding  btnSetupPin ,com.appblock.databinding.ActivityMainBinding  cardBlockedApps ,com.appblock.databinding.ActivityMainBinding  cardPermissions ,com.appblock.databinding.ActivityMainBinding  cardSettings ,com.appblock.databinding.ActivityMainBinding  getROOT ,com.appblock.databinding.ActivityMainBinding  getRoot ,com.appblock.databinding.ActivityMainBinding  inflate ,com.appblock.databinding.ActivityMainBinding  root ,com.appblock.databinding.ActivityMainBinding  setRoot ,com.appblock.databinding.ActivityMainBinding  switchAppBlocking ,com.appblock.databinding.ActivityMainBinding  toolbar ,com.appblock.databinding.ActivityMainBinding  tvBlockedAppsCount ,com.appblock.databinding.ActivityMainBinding  tvPermissionStatus ,com.appblock.databinding.ActivityMainBinding  btn0 /com.appblock.databinding.ActivityPinAuthBinding  btn1 /com.appblock.databinding.ActivityPinAuthBinding  btn2 /com.appblock.databinding.ActivityPinAuthBinding  btn3 /com.appblock.databinding.ActivityPinAuthBinding  btn4 /com.appblock.databinding.ActivityPinAuthBinding  btn5 /com.appblock.databinding.ActivityPinAuthBinding  btn6 /com.appblock.databinding.ActivityPinAuthBinding  btn7 /com.appblock.databinding.ActivityPinAuthBinding  btn8 /com.appblock.databinding.ActivityPinAuthBinding  btn9 /com.appblock.databinding.ActivityPinAuthBinding  btnBackspace /com.appblock.databinding.ActivityPinAuthBinding  	btnCancel /com.appblock.databinding.ActivityPinAuthBinding  btnClear /com.appblock.databinding.ActivityPinAuthBinding  	btnVerify /com.appblock.databinding.ActivityPinAuthBinding  etPin /com.appblock.databinding.ActivityPinAuthBinding  getROOT /com.appblock.databinding.ActivityPinAuthBinding  getRoot /com.appblock.databinding.ActivityPinAuthBinding  inflate /com.appblock.databinding.ActivityPinAuthBinding  root /com.appblock.databinding.ActivityPinAuthBinding  setRoot /com.appblock.databinding.ActivityPinAuthBinding  	tvAppName /com.appblock.databinding.ActivityPinAuthBinding  	tvMessage /com.appblock.databinding.ActivityPinAuthBinding  tvTitle /com.appblock.databinding.ActivityPinAuthBinding  android 'com.appblock.databinding.ItemAppBinding  androidx 'com.appblock.databinding.ItemAppBinding  apply 'com.appblock.databinding.ItemAppBinding  checkboxBlocked 'com.appblock.databinding.ItemAppBinding  com 'com.appblock.databinding.ItemAppBinding  
getANDROID 'com.appblock.databinding.ItemAppBinding  getANDROIDX 'com.appblock.databinding.ItemAppBinding  getAPPLY 'com.appblock.databinding.ItemAppBinding  
getAndroid 'com.appblock.databinding.ItemAppBinding  getAndroidx 'com.appblock.databinding.ItemAppBinding  getApply 'com.appblock.databinding.ItemAppBinding  getCOM 'com.appblock.databinding.ItemAppBinding  getCom 'com.appblock.databinding.ItemAppBinding  getONAppSelectionChanged 'com.appblock.databinding.ItemAppBinding  getOnAppSelectionChanged 'com.appblock.databinding.ItemAppBinding  getROOT 'com.appblock.databinding.ItemAppBinding  getRoot 'com.appblock.databinding.ItemAppBinding  inflate 'com.appblock.databinding.ItemAppBinding  invoke 'com.appblock.databinding.ItemAppBinding  	ivAppIcon 'com.appblock.databinding.ItemAppBinding  onAppSelectionChanged 'com.appblock.databinding.ItemAppBinding  root 'com.appblock.databinding.ItemAppBinding  setRoot 'com.appblock.databinding.ItemAppBinding  	tvAppName 'com.appblock.databinding.ItemAppBinding  
tvPackageName 'com.appblock.databinding.ItemAppBinding  tvSystemApp 'com.appblock.databinding.ItemAppBinding  ACTION_CHECK_PERMISSIONS com.appblock.receiver  AppBlockDeviceAdminReceiver com.appblock.receiver  Boolean com.appblock.receiver  BootReceiver com.appblock.receiver  Build com.appblock.receiver  CharSequence com.appblock.receiver  
ComponentName com.appblock.receiver  Context com.appblock.receiver  DevicePolicyManager com.appblock.receiver  	Exception com.appblock.receiver  Intent com.appblock.receiver  InterruptedException com.appblock.receiver  Log com.appblock.receiver  PermissionMonitorReceiver com.appblock.receiver  PermissionUtils com.appblock.receiver  SettingsManager com.appblock.receiver  System com.appblock.receiver  TAG com.appblock.receiver  Thread com.appblock.receiver  Toast com.appblock.receiver  UsageStatsMonitorService com.appblock.receiver  android com.appblock.receiver  com com.appblock.receiver  contains com.appblock.receiver  getComponentName com.appblock.receiver  java com.appblock.receiver  removePrefix com.appblock.receiver  AppBlockDeviceAdminReceiver 1com.appblock.receiver.AppBlockDeviceAdminReceiver  Boolean 1com.appblock.receiver.AppBlockDeviceAdminReceiver  CharSequence 1com.appblock.receiver.AppBlockDeviceAdminReceiver  	Companion 1com.appblock.receiver.AppBlockDeviceAdminReceiver  
ComponentName 1com.appblock.receiver.AppBlockDeviceAdminReceiver  Context 1com.appblock.receiver.AppBlockDeviceAdminReceiver  DevicePolicyManager 1com.appblock.receiver.AppBlockDeviceAdminReceiver  	Exception 1com.appblock.receiver.AppBlockDeviceAdminReceiver  Intent 1com.appblock.receiver.AppBlockDeviceAdminReceiver  Log 1com.appblock.receiver.AppBlockDeviceAdminReceiver  SettingsManager 1com.appblock.receiver.AppBlockDeviceAdminReceiver  System 1com.appblock.receiver.AppBlockDeviceAdminReceiver  TAG 1com.appblock.receiver.AppBlockDeviceAdminReceiver  Toast 1com.appblock.receiver.AppBlockDeviceAdminReceiver  android 1com.appblock.receiver.AppBlockDeviceAdminReceiver  com 1com.appblock.receiver.AppBlockDeviceAdminReceiver  
getANDROID 1com.appblock.receiver.AppBlockDeviceAdminReceiver  
getAndroid 1com.appblock.receiver.AppBlockDeviceAdminReceiver  getCOM 1com.appblock.receiver.AppBlockDeviceAdminReceiver  getCom 1com.appblock.receiver.AppBlockDeviceAdminReceiver  getComponentName 1com.appblock.receiver.AppBlockDeviceAdminReceiver  getGETComponentName 1com.appblock.receiver.AppBlockDeviceAdminReceiver  getGetComponentName 1com.appblock.receiver.AppBlockDeviceAdminReceiver  handleTamperingAttempt 1com.appblock.receiver.AppBlockDeviceAdminReceiver  invoke 1com.appblock.receiver.AppBlockDeviceAdminReceiver  isDeviceAdminActive 1com.appblock.receiver.AppBlockDeviceAdminReceiver  java 1com.appblock.receiver.AppBlockDeviceAdminReceiver  
lockDevice 1com.appblock.receiver.AppBlockDeviceAdminReceiver  requestDeviceAdminActivation 1com.appblock.receiver.AppBlockDeviceAdminReceiver  restartProtectionServices 1com.appblock.receiver.AppBlockDeviceAdminReceiver  sendTamperingNotification 1com.appblock.receiver.AppBlockDeviceAdminReceiver  AppBlockDeviceAdminReceiver ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  Boolean ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  CharSequence ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  
ComponentName ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  Context ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  DevicePolicyManager ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  	Exception ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  Intent ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  Log ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  SettingsManager ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  System ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  TAG ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  Toast ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  android ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  com ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  
getANDROID ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  
getAndroid ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  getCOM ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  getCom ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  getComponentName ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  invoke ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  isDeviceAdminActive ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  java ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  requestDeviceAdminActivation ;com.appblock.receiver.AppBlockDeviceAdminReceiver.Companion  AppBlockDeviceAdminReceiver "com.appblock.receiver.BootReceiver  Build "com.appblock.receiver.BootReceiver  Context "com.appblock.receiver.BootReceiver  	Exception "com.appblock.receiver.BootReceiver  Intent "com.appblock.receiver.BootReceiver  InterruptedException "com.appblock.receiver.BootReceiver  Log "com.appblock.receiver.BootReceiver  PermissionUtils "com.appblock.receiver.BootReceiver  SettingsManager "com.appblock.receiver.BootReceiver  System "com.appblock.receiver.BootReceiver  TAG "com.appblock.receiver.BootReceiver  Thread "com.appblock.receiver.BootReceiver  UsageStatsMonitorService "com.appblock.receiver.BootReceiver  contains "com.appblock.receiver.BootReceiver  getCONTAINS "com.appblock.receiver.BootReceiver  getContains "com.appblock.receiver.BootReceiver  handleAppUpdated "com.appblock.receiver.BootReceiver  handleBootCompleted "com.appblock.receiver.BootReceiver  invoke "com.appblock.receiver.BootReceiver  java "com.appblock.receiver.BootReceiver  startProtectionServices "com.appblock.receiver.BootReceiver  startProtectionServicesDelayed "com.appblock.receiver.BootReceiver  AppBlockDeviceAdminReceiver ,com.appblock.receiver.BootReceiver.Companion  Build ,com.appblock.receiver.BootReceiver.Companion  Context ,com.appblock.receiver.BootReceiver.Companion  	Exception ,com.appblock.receiver.BootReceiver.Companion  Intent ,com.appblock.receiver.BootReceiver.Companion  InterruptedException ,com.appblock.receiver.BootReceiver.Companion  Log ,com.appblock.receiver.BootReceiver.Companion  PermissionUtils ,com.appblock.receiver.BootReceiver.Companion  SettingsManager ,com.appblock.receiver.BootReceiver.Companion  System ,com.appblock.receiver.BootReceiver.Companion  TAG ,com.appblock.receiver.BootReceiver.Companion  Thread ,com.appblock.receiver.BootReceiver.Companion  UsageStatsMonitorService ,com.appblock.receiver.BootReceiver.Companion  contains ,com.appblock.receiver.BootReceiver.Companion  getCONTAINS ,com.appblock.receiver.BootReceiver.Companion  getContains ,com.appblock.receiver.BootReceiver.Companion  invoke ,com.appblock.receiver.BootReceiver.Companion  java ,com.appblock.receiver.BootReceiver.Companion  ACTION_CHECK_PERMISSIONS /com.appblock.receiver.PermissionMonitorReceiver  Context /com.appblock.receiver.PermissionMonitorReceiver  Intent /com.appblock.receiver.PermissionMonitorReceiver  Log /com.appblock.receiver.PermissionMonitorReceiver  PermissionUtils /com.appblock.receiver.PermissionMonitorReceiver  SettingsManager /com.appblock.receiver.PermissionMonitorReceiver  TAG /com.appblock.receiver.PermissionMonitorReceiver  checkPermissions /com.appblock.receiver.PermissionMonitorReceiver  getREMOVEPrefix /com.appblock.receiver.PermissionMonitorReceiver  getRemovePrefix /com.appblock.receiver.PermissionMonitorReceiver  handlePackageChange /com.appblock.receiver.PermissionMonitorReceiver  invoke /com.appblock.receiver.PermissionMonitorReceiver  removePrefix /com.appblock.receiver.PermissionMonitorReceiver  ACTION_CHECK_PERMISSIONS 9com.appblock.receiver.PermissionMonitorReceiver.Companion  Context 9com.appblock.receiver.PermissionMonitorReceiver.Companion  Intent 9com.appblock.receiver.PermissionMonitorReceiver.Companion  Log 9com.appblock.receiver.PermissionMonitorReceiver.Companion  PermissionUtils 9com.appblock.receiver.PermissionMonitorReceiver.Companion  SettingsManager 9com.appblock.receiver.PermissionMonitorReceiver.Companion  TAG 9com.appblock.receiver.PermissionMonitorReceiver.Companion  getREMOVEPrefix 9com.appblock.receiver.PermissionMonitorReceiver.Companion  getRemovePrefix 9com.appblock.receiver.PermissionMonitorReceiver.Companion  invoke 9com.appblock.receiver.PermissionMonitorReceiver.Companion  removePrefix 9com.appblock.receiver.PermissionMonitorReceiver.Companion  AccessibilityEvent com.appblock.service  AccessibilityServiceInfo com.appblock.service  AppBlockAccessibilityService com.appblock.service  BlockedAppsManager com.appblock.service  Boolean com.appblock.service  Build com.appblock.service  
CHANNEL_ID com.appblock.service  Context com.appblock.service  	Exception com.appblock.service  	Executors com.appblock.service  FrameLayout com.appblock.service  Gravity com.appblock.service  Handler com.appblock.service  Int com.appblock.service  Intent com.appblock.service  InterruptedException com.appblock.service  Log com.appblock.service  Long com.appblock.service  Looper com.appblock.service  NOTIFICATION_ID com.appblock.service  NotificationChannel com.appblock.service  NotificationCompat com.appblock.service  NotificationManager com.appblock.service  POLLING_INTERVAL_SECONDS com.appblock.service  PermissionUtils com.appblock.service  PixelFormat com.appblock.service  START_STICKY com.appblock.service  String com.appblock.service  Suppress com.appblock.service  System com.appblock.service  TAG com.appblock.service  TimeUnit com.appblock.service  UsageEvents com.appblock.service  UsageStatsManager com.appblock.service  UsageStatsMonitorService com.appblock.service  Volatile com.appblock.service  
WindowManager com.appblock.service  android com.appblock.service  apply com.appblock.service  contains com.appblock.service  find com.appblock.service  instance com.appblock.service  
isNullOrEmpty com.appblock.service  java com.appblock.service  let com.appblock.service  	lowercase com.appblock.service  AccessibilityEvent 1com.appblock.service.AppBlockAccessibilityService  AccessibilityServiceInfo 1com.appblock.service.AppBlockAccessibilityService  AppBlockAccessibilityService 1com.appblock.service.AppBlockAccessibilityService  BlockedAppsManager 1com.appblock.service.AppBlockAccessibilityService  Boolean 1com.appblock.service.AppBlockAccessibilityService  Build 1com.appblock.service.AppBlockAccessibilityService  
CHANNEL_ID 1com.appblock.service.AppBlockAccessibilityService  	Companion 1com.appblock.service.AppBlockAccessibilityService  Context 1com.appblock.service.AppBlockAccessibilityService  	Exception 1com.appblock.service.AppBlockAccessibilityService  FrameLayout 1com.appblock.service.AppBlockAccessibilityService  Gravity 1com.appblock.service.AppBlockAccessibilityService  Intent 1com.appblock.service.AppBlockAccessibilityService  Log 1com.appblock.service.AppBlockAccessibilityService  Long 1com.appblock.service.AppBlockAccessibilityService  NOTIFICATION_ID 1com.appblock.service.AppBlockAccessibilityService  NotificationChannel 1com.appblock.service.AppBlockAccessibilityService  NotificationCompat 1com.appblock.service.AppBlockAccessibilityService  NotificationManager 1com.appblock.service.AppBlockAccessibilityService  PixelFormat 1com.appblock.service.AppBlockAccessibilityService  String 1com.appblock.service.AppBlockAccessibilityService  Suppress 1com.appblock.service.AppBlockAccessibilityService  TAG 1com.appblock.service.AppBlockAccessibilityService  View 1com.appblock.service.AppBlockAccessibilityService  Volatile 1com.appblock.service.AppBlockAccessibilityService  
WindowManager 1com.appblock.service.AppBlockAccessibilityService  android 1com.appblock.service.AppBlockAccessibilityService  apply 1com.appblock.service.AppBlockAccessibilityService  blockApp 1com.appblock.service.AppBlockAccessibilityService  blockedAppsManager 1com.appblock.service.AppBlockAccessibilityService  checkForTamperingAttempt 1com.appblock.service.AppBlockAccessibilityService  contains 1com.appblock.service.AppBlockAccessibilityService  createNotificationChannel 1com.appblock.service.AppBlockAccessibilityService  currentBlockedPackage 1com.appblock.service.AppBlockAccessibilityService  equals 1com.appblock.service.AppBlockAccessibilityService  
getANDROID 1com.appblock.service.AppBlockAccessibilityService  getAPPLY 1com.appblock.service.AppBlockAccessibilityService  
getAndroid 1com.appblock.service.AppBlockAccessibilityService  getApply 1com.appblock.service.AppBlockAccessibilityService  getCONTAINS 1com.appblock.service.AppBlockAccessibilityService  getContains 1com.appblock.service.AppBlockAccessibilityService  getINSTANCE 1com.appblock.service.AppBlockAccessibilityService  getISNullOrEmpty 1com.appblock.service.AppBlockAccessibilityService  getInstance 1com.appblock.service.AppBlockAccessibilityService  getIsNullOrEmpty 1com.appblock.service.AppBlockAccessibilityService  getLET 1com.appblock.service.AppBlockAccessibilityService  getLOWERCASE 1com.appblock.service.AppBlockAccessibilityService  getLet 1com.appblock.service.AppBlockAccessibilityService  getLowercase 1com.appblock.service.AppBlockAccessibilityService  getSERVICEInfo 1com.appblock.service.AppBlockAccessibilityService  getServiceInfo 1com.appblock.service.AppBlockAccessibilityService  getSystemService 1com.appblock.service.AppBlockAccessibilityService  goToHome 1com.appblock.service.AppBlockAccessibilityService  handleSettingsAccess 1com.appblock.service.AppBlockAccessibilityService  handleWindowContentChanged 1com.appblock.service.AppBlockAccessibilityService  handleWindowStateChanged 1com.appblock.service.AppBlockAccessibilityService  instance 1com.appblock.service.AppBlockAccessibilityService  invoke 1com.appblock.service.AppBlockAccessibilityService  isAppCurrentlyBlocked 1com.appblock.service.AppBlockAccessibilityService  
isNullOrEmpty 1com.appblock.service.AppBlockAccessibilityService  isServiceRunning 1com.appblock.service.AppBlockAccessibilityService  java 1com.appblock.service.AppBlockAccessibilityService  let 1com.appblock.service.AppBlockAccessibilityService  	lowercase 1com.appblock.service.AppBlockAccessibilityService  overlayView 1com.appblock.service.AppBlockAccessibilityService  
removeOverlay 1com.appblock.service.AppBlockAccessibilityService  serviceInfo 1com.appblock.service.AppBlockAccessibilityService  setServiceInfo 1com.appblock.service.AppBlockAccessibilityService  setupOverlayInteractions 1com.appblock.service.AppBlockAccessibilityService  showBlockOverlay 1com.appblock.service.AppBlockAccessibilityService  
startActivity 1com.appblock.service.AppBlockAccessibilityService  startForeground 1com.appblock.service.AppBlockAccessibilityService  startForegroundService 1com.appblock.service.AppBlockAccessibilityService  
windowManager 1com.appblock.service.AppBlockAccessibilityService  AccessibilityEvent ;com.appblock.service.AppBlockAccessibilityService.Companion  AccessibilityServiceInfo ;com.appblock.service.AppBlockAccessibilityService.Companion  AppBlockAccessibilityService ;com.appblock.service.AppBlockAccessibilityService.Companion  BlockedAppsManager ;com.appblock.service.AppBlockAccessibilityService.Companion  Boolean ;com.appblock.service.AppBlockAccessibilityService.Companion  Build ;com.appblock.service.AppBlockAccessibilityService.Companion  
CHANNEL_ID ;com.appblock.service.AppBlockAccessibilityService.Companion  Context ;com.appblock.service.AppBlockAccessibilityService.Companion  	Exception ;com.appblock.service.AppBlockAccessibilityService.Companion  FrameLayout ;com.appblock.service.AppBlockAccessibilityService.Companion  Gravity ;com.appblock.service.AppBlockAccessibilityService.Companion  Intent ;com.appblock.service.AppBlockAccessibilityService.Companion  Log ;com.appblock.service.AppBlockAccessibilityService.Companion  Long ;com.appblock.service.AppBlockAccessibilityService.Companion  NOTIFICATION_ID ;com.appblock.service.AppBlockAccessibilityService.Companion  NotificationChannel ;com.appblock.service.AppBlockAccessibilityService.Companion  NotificationCompat ;com.appblock.service.AppBlockAccessibilityService.Companion  NotificationManager ;com.appblock.service.AppBlockAccessibilityService.Companion  PixelFormat ;com.appblock.service.AppBlockAccessibilityService.Companion  String ;com.appblock.service.AppBlockAccessibilityService.Companion  Suppress ;com.appblock.service.AppBlockAccessibilityService.Companion  TAG ;com.appblock.service.AppBlockAccessibilityService.Companion  View ;com.appblock.service.AppBlockAccessibilityService.Companion  Volatile ;com.appblock.service.AppBlockAccessibilityService.Companion  
WindowManager ;com.appblock.service.AppBlockAccessibilityService.Companion  android ;com.appblock.service.AppBlockAccessibilityService.Companion  apply ;com.appblock.service.AppBlockAccessibilityService.Companion  contains ;com.appblock.service.AppBlockAccessibilityService.Companion  
getANDROID ;com.appblock.service.AppBlockAccessibilityService.Companion  getAPPLY ;com.appblock.service.AppBlockAccessibilityService.Companion  
getAndroid ;com.appblock.service.AppBlockAccessibilityService.Companion  getApply ;com.appblock.service.AppBlockAccessibilityService.Companion  getCONTAINS ;com.appblock.service.AppBlockAccessibilityService.Companion  getContains ;com.appblock.service.AppBlockAccessibilityService.Companion  getISNullOrEmpty ;com.appblock.service.AppBlockAccessibilityService.Companion  getInstance ;com.appblock.service.AppBlockAccessibilityService.Companion  getIsNullOrEmpty ;com.appblock.service.AppBlockAccessibilityService.Companion  getLET ;com.appblock.service.AppBlockAccessibilityService.Companion  getLOWERCASE ;com.appblock.service.AppBlockAccessibilityService.Companion  getLet ;com.appblock.service.AppBlockAccessibilityService.Companion  getLowercase ;com.appblock.service.AppBlockAccessibilityService.Companion  instance ;com.appblock.service.AppBlockAccessibilityService.Companion  invoke ;com.appblock.service.AppBlockAccessibilityService.Companion  
isNullOrEmpty ;com.appblock.service.AppBlockAccessibilityService.Companion  isServiceRunning ;com.appblock.service.AppBlockAccessibilityService.Companion  java ;com.appblock.service.AppBlockAccessibilityService.Companion  let ;com.appblock.service.AppBlockAccessibilityService.Companion  	lowercase ;com.appblock.service.AppBlockAccessibilityService.Companion  AppBlockAccessibilityService -com.appblock.service.UsageStatsMonitorService  BlockedAppsManager -com.appblock.service.UsageStatsMonitorService  Boolean -com.appblock.service.UsageStatsMonitorService  Build -com.appblock.service.UsageStatsMonitorService  
CHANNEL_ID -com.appblock.service.UsageStatsMonitorService  	Companion -com.appblock.service.UsageStatsMonitorService  Context -com.appblock.service.UsageStatsMonitorService  	Exception -com.appblock.service.UsageStatsMonitorService  	Executors -com.appblock.service.UsageStatsMonitorService  Handler -com.appblock.service.UsageStatsMonitorService  IBinder -com.appblock.service.UsageStatsMonitorService  Int -com.appblock.service.UsageStatsMonitorService  Intent -com.appblock.service.UsageStatsMonitorService  InterruptedException -com.appblock.service.UsageStatsMonitorService  Log -com.appblock.service.UsageStatsMonitorService  Long -com.appblock.service.UsageStatsMonitorService  Looper -com.appblock.service.UsageStatsMonitorService  NOTIFICATION_ID -com.appblock.service.UsageStatsMonitorService  NotificationChannel -com.appblock.service.UsageStatsMonitorService  NotificationCompat -com.appblock.service.UsageStatsMonitorService  NotificationManager -com.appblock.service.UsageStatsMonitorService  POLLING_INTERVAL_SECONDS -com.appblock.service.UsageStatsMonitorService  PermissionUtils -com.appblock.service.UsageStatsMonitorService  START_STICKY -com.appblock.service.UsageStatsMonitorService  ScheduledExecutorService -com.appblock.service.UsageStatsMonitorService  String -com.appblock.service.UsageStatsMonitorService  System -com.appblock.service.UsageStatsMonitorService  TAG -com.appblock.service.UsageStatsMonitorService  TimeUnit -com.appblock.service.UsageStatsMonitorService  UsageEvents -com.appblock.service.UsageStatsMonitorService  UsageStatsManager -com.appblock.service.UsageStatsMonitorService  UsageStatsMonitorService -com.appblock.service.UsageStatsMonitorService  Volatile -com.appblock.service.UsageStatsMonitorService  android -com.appblock.service.UsageStatsMonitorService  apply -com.appblock.service.UsageStatsMonitorService  blockAppViaActivity -com.appblock.service.UsageStatsMonitorService  blockedAppsManager -com.appblock.service.UsageStatsMonitorService  createNotificationChannel -com.appblock.service.UsageStatsMonitorService  currentForegroundApp -com.appblock.service.UsageStatsMonitorService  equals -com.appblock.service.UsageStatsMonitorService  find -com.appblock.service.UsageStatsMonitorService  
getANDROID -com.appblock.service.UsageStatsMonitorService  getAPPLY -com.appblock.service.UsageStatsMonitorService  
getAndroid -com.appblock.service.UsageStatsMonitorService  getApply -com.appblock.service.UsageStatsMonitorService  getFIND -com.appblock.service.UsageStatsMonitorService  getFind -com.appblock.service.UsageStatsMonitorService  getINSTANCE -com.appblock.service.UsageStatsMonitorService  getInstance -com.appblock.service.UsageStatsMonitorService  getLET -com.appblock.service.UsageStatsMonitorService  getLet -com.appblock.service.UsageStatsMonitorService  getSystemService -com.appblock.service.UsageStatsMonitorService  handleBlockedApp -com.appblock.service.UsageStatsMonitorService  instance -com.appblock.service.UsageStatsMonitorService  invoke -com.appblock.service.UsageStatsMonitorService  java -com.appblock.service.UsageStatsMonitorService  
lastCheckTime -com.appblock.service.UsageStatsMonitorService  let -com.appblock.service.UsageStatsMonitorService  mainHandler -com.appblock.service.UsageStatsMonitorService  monitorForegroundApp -com.appblock.service.UsageStatsMonitorService  	scheduler -com.appblock.service.UsageStatsMonitorService  
startActivity -com.appblock.service.UsageStatsMonitorService  startForeground -com.appblock.service.UsageStatsMonitorService  startForegroundService -com.appblock.service.UsageStatsMonitorService  startMonitoring -com.appblock.service.UsageStatsMonitorService  usageStatsManager -com.appblock.service.UsageStatsMonitorService  AppBlockAccessibilityService 7com.appblock.service.UsageStatsMonitorService.Companion  BlockedAppsManager 7com.appblock.service.UsageStatsMonitorService.Companion  Boolean 7com.appblock.service.UsageStatsMonitorService.Companion  Build 7com.appblock.service.UsageStatsMonitorService.Companion  
CHANNEL_ID 7com.appblock.service.UsageStatsMonitorService.Companion  Context 7com.appblock.service.UsageStatsMonitorService.Companion  	Exception 7com.appblock.service.UsageStatsMonitorService.Companion  	Executors 7com.appblock.service.UsageStatsMonitorService.Companion  Handler 7com.appblock.service.UsageStatsMonitorService.Companion  IBinder 7com.appblock.service.UsageStatsMonitorService.Companion  Int 7com.appblock.service.UsageStatsMonitorService.Companion  Intent 7com.appblock.service.UsageStatsMonitorService.Companion  InterruptedException 7com.appblock.service.UsageStatsMonitorService.Companion  Log 7com.appblock.service.UsageStatsMonitorService.Companion  Long 7com.appblock.service.UsageStatsMonitorService.Companion  Looper 7com.appblock.service.UsageStatsMonitorService.Companion  NOTIFICATION_ID 7com.appblock.service.UsageStatsMonitorService.Companion  NotificationChannel 7com.appblock.service.UsageStatsMonitorService.Companion  NotificationCompat 7com.appblock.service.UsageStatsMonitorService.Companion  NotificationManager 7com.appblock.service.UsageStatsMonitorService.Companion  POLLING_INTERVAL_SECONDS 7com.appblock.service.UsageStatsMonitorService.Companion  PermissionUtils 7com.appblock.service.UsageStatsMonitorService.Companion  START_STICKY 7com.appblock.service.UsageStatsMonitorService.Companion  ScheduledExecutorService 7com.appblock.service.UsageStatsMonitorService.Companion  String 7com.appblock.service.UsageStatsMonitorService.Companion  System 7com.appblock.service.UsageStatsMonitorService.Companion  TAG 7com.appblock.service.UsageStatsMonitorService.Companion  TimeUnit 7com.appblock.service.UsageStatsMonitorService.Companion  UsageEvents 7com.appblock.service.UsageStatsMonitorService.Companion  UsageStatsManager 7com.appblock.service.UsageStatsMonitorService.Companion  UsageStatsMonitorService 7com.appblock.service.UsageStatsMonitorService.Companion  Volatile 7com.appblock.service.UsageStatsMonitorService.Companion  android 7com.appblock.service.UsageStatsMonitorService.Companion  apply 7com.appblock.service.UsageStatsMonitorService.Companion  find 7com.appblock.service.UsageStatsMonitorService.Companion  
getANDROID 7com.appblock.service.UsageStatsMonitorService.Companion  getAPPLY 7com.appblock.service.UsageStatsMonitorService.Companion  
getAndroid 7com.appblock.service.UsageStatsMonitorService.Companion  getApply 7com.appblock.service.UsageStatsMonitorService.Companion  getFIND 7com.appblock.service.UsageStatsMonitorService.Companion  getFind 7com.appblock.service.UsageStatsMonitorService.Companion  getLET 7com.appblock.service.UsageStatsMonitorService.Companion  getLet 7com.appblock.service.UsageStatsMonitorService.Companion  instance 7com.appblock.service.UsageStatsMonitorService.Companion  invoke 7com.appblock.service.UsageStatsMonitorService.Companion  java 7com.appblock.service.UsageStatsMonitorService.Companion  let 7com.appblock.service.UsageStatsMonitorService.Companion  BlockedAppsManager com.appblock.test  CoreFunctionalityTest com.appblock.test  Log com.appblock.test  SettingsManager com.appblock.test  System com.appblock.test  TAG com.appblock.test  BlockedAppsManager 'com.appblock.test.CoreFunctionalityTest  Context 'com.appblock.test.CoreFunctionalityTest  Log 'com.appblock.test.CoreFunctionalityTest  SettingsManager 'com.appblock.test.CoreFunctionalityTest  System 'com.appblock.test.CoreFunctionalityTest  TAG 'com.appblock.test.CoreFunctionalityTest  context 'com.appblock.test.CoreFunctionalityTest  invoke 'com.appblock.test.CoreFunctionalityTest  
runBasicTests 'com.appblock.test.CoreFunctionalityTest  testBlockedAppsManager 'com.appblock.test.CoreFunctionalityTest  testSettingsManager 'com.appblock.test.CoreFunctionalityTest  BlockedAppsManager 1com.appblock.test.CoreFunctionalityTest.Companion  Context 1com.appblock.test.CoreFunctionalityTest.Companion  Log 1com.appblock.test.CoreFunctionalityTest.Companion  SettingsManager 1com.appblock.test.CoreFunctionalityTest.Companion  System 1com.appblock.test.CoreFunctionalityTest.Companion  TAG 1com.appblock.test.CoreFunctionalityTest.Companion  invoke 1com.appblock.test.CoreFunctionalityTest.Companion  Activity com.appblock.ui  ActivityAppSelectionBinding com.appblock.ui  ActivityBlockOverlayBinding com.appblock.ui  ActivityMainBinding com.appblock.ui  ActivityPinAuthBinding com.appblock.ui  ActivityResultContracts com.appblock.ui  AlertDialog com.appblock.ui  AppInfo com.appblock.ui  AppListAdapter com.appblock.ui  AppSelectionActivity com.appblock.ui  ApplicationInfo com.appblock.ui  BaseAdapter com.appblock.ui  BlockOverlayActivity com.appblock.ui  BlockedAppsManager com.appblock.ui  Boolean com.appblock.ui  CharSequence com.appblock.ui  CoroutineScope com.appblock.ui  Dispatchers com.appblock.ui  	Exception com.appblock.ui  	ImageView com.appblock.ui  Int com.appblock.ui  Intent com.appblock.ui  LinearLayout com.appblock.ui  LinearLayoutManager com.appblock.ui  List com.appblock.ui  ListView com.appblock.ui  Log com.appblock.ui  Long com.appblock.ui  MAX_ATTEMPTS com.appblock.ui  MainActivity com.appblock.ui  PackageManager com.appblock.ui  PermissionUtils com.appblock.ui  PinAuthActivity com.appblock.ui  ProgressBar com.appblock.ui  R com.appblock.ui  SettingsManager com.appblock.ui  SimpleAppSelectionActivity com.appblock.ui  String com.appblock.ui  
SupervisorJob com.appblock.ui  System com.appblock.ui  TAG com.appblock.ui  TextView com.appblock.ui  Toast com.appblock.ui  Unit com.appblock.ui  UsageStatsMonitorService com.appblock.ui  View com.appblock.ui  
WindowManager com.appblock.ui  all com.appblock.ui  allApps com.appblock.ui  android com.appblock.ui  appListAdapter com.appblock.ui  apply com.appblock.ui  arrayOf com.appblock.ui  binding com.appblock.ui  blockedPackageName com.appblock.ui  cancel com.appblock.ui  com com.appblock.ui  contains com.appblock.ui  count com.appblock.ui  dropLast com.appblock.ui  filter com.appblock.ui  
filterApps com.appblock.ui  forEach com.appblock.ui  forEachIndexed com.appblock.ui  isBlank com.appblock.ui  
isNotEmpty com.appblock.ui  java com.appblock.ui  joinToString com.appblock.ui  launch com.appblock.ui  let com.appblock.ui  listOf com.appblock.ui  listView com.appblock.ui  loadInstalledApps com.appblock.ui  	lowercase com.appblock.ui  map com.appblock.ui  
mutableListOf com.appblock.ui  progressBar com.appblock.ui  
setupListView com.appblock.ui  sortedBy com.appblock.ui  toSet com.appblock.ui  toString com.appblock.ui  	verifyPin com.appblock.ui  withContext com.appblock.ui  ActivityAppSelectionBinding $com.appblock.ui.AppSelectionActivity  AlertDialog $com.appblock.ui.AppSelectionActivity  AppInfo $com.appblock.ui.AppSelectionActivity  AppListAdapter $com.appblock.ui.AppSelectionActivity  ApplicationInfo $com.appblock.ui.AppSelectionActivity  BlockedAppsManager $com.appblock.ui.AppSelectionActivity  Boolean $com.appblock.ui.AppSelectionActivity  Bundle $com.appblock.ui.AppSelectionActivity  	Companion $com.appblock.ui.AppSelectionActivity  CoroutineScope $com.appblock.ui.AppSelectionActivity  Dispatchers $com.appblock.ui.AppSelectionActivity  	Exception $com.appblock.ui.AppSelectionActivity  LinearLayoutManager $com.appblock.ui.AppSelectionActivity  List $com.appblock.ui.AppSelectionActivity  Menu $com.appblock.ui.AppSelectionActivity  MenuItem $com.appblock.ui.AppSelectionActivity  PackageManager $com.appblock.ui.AppSelectionActivity  R $com.appblock.ui.AppSelectionActivity  
SearchView $com.appblock.ui.AppSelectionActivity  String $com.appblock.ui.AppSelectionActivity  
SupervisorJob $com.appblock.ui.AppSelectionActivity  Toast $com.appblock.ui.AppSelectionActivity  Unit $com.appblock.ui.AppSelectionActivity  View $com.appblock.ui.AppSelectionActivity  allApps $com.appblock.ui.AppSelectionActivity  android $com.appblock.ui.AppSelectionActivity  appListAdapter $com.appblock.ui.AppSelectionActivity  apply $com.appblock.ui.AppSelectionActivity  binding $com.appblock.ui.AppSelectionActivity  blockedAppsManager $com.appblock.ui.AppSelectionActivity  cancel $com.appblock.ui.AppSelectionActivity  contains $com.appblock.ui.AppSelectionActivity  coroutineScope $com.appblock.ui.AppSelectionActivity  count $com.appblock.ui.AppSelectionActivity  deselectAllVisibleApps $com.appblock.ui.AppSelectionActivity  filter $com.appblock.ui.AppSelectionActivity  
filterApps $com.appblock.ui.AppSelectionActivity  filteredApps $com.appblock.ui.AppSelectionActivity  finish $com.appblock.ui.AppSelectionActivity  
getANDROID $com.appblock.ui.AppSelectionActivity  getAPPLY $com.appblock.ui.AppSelectionActivity  
getAndroid $com.appblock.ui.AppSelectionActivity  getApply $com.appblock.ui.AppSelectionActivity  	getCANCEL $com.appblock.ui.AppSelectionActivity  getCONTAINS $com.appblock.ui.AppSelectionActivity  getCOUNT $com.appblock.ui.AppSelectionActivity  	getCancel $com.appblock.ui.AppSelectionActivity  getContains $com.appblock.ui.AppSelectionActivity  getCount $com.appblock.ui.AppSelectionActivity  	getFILTER $com.appblock.ui.AppSelectionActivity  	getFilter $com.appblock.ui.AppSelectionActivity  
getISBlank $com.appblock.ui.AppSelectionActivity  
getIsBlank $com.appblock.ui.AppSelectionActivity  	getLAUNCH $com.appblock.ui.AppSelectionActivity  getLAYOUTInflater $com.appblock.ui.AppSelectionActivity  	getLISTOf $com.appblock.ui.AppSelectionActivity  getLOWERCASE $com.appblock.ui.AppSelectionActivity  	getLaunch $com.appblock.ui.AppSelectionActivity  getLayoutInflater $com.appblock.ui.AppSelectionActivity  	getListOf $com.appblock.ui.AppSelectionActivity  getLowercase $com.appblock.ui.AppSelectionActivity  getMAP $com.appblock.ui.AppSelectionActivity  getMENUInflater $com.appblock.ui.AppSelectionActivity  getMap $com.appblock.ui.AppSelectionActivity  getMenuInflater $com.appblock.ui.AppSelectionActivity  getPACKAGEManager $com.appblock.ui.AppSelectionActivity  getPACKAGEName $com.appblock.ui.AppSelectionActivity  getPackageManager $com.appblock.ui.AppSelectionActivity  getPackageName $com.appblock.ui.AppSelectionActivity  getSORTEDBy $com.appblock.ui.AppSelectionActivity  getSUPPORTActionBar $com.appblock.ui.AppSelectionActivity  getSortedBy $com.appblock.ui.AppSelectionActivity  getSupportActionBar $com.appblock.ui.AppSelectionActivity  getTOSet $com.appblock.ui.AppSelectionActivity  getToSet $com.appblock.ui.AppSelectionActivity  getWITHContext $com.appblock.ui.AppSelectionActivity  getWithContext $com.appblock.ui.AppSelectionActivity  handleAppSelectionChanged $com.appblock.ui.AppSelectionActivity  invoke $com.appblock.ui.AppSelectionActivity  isBlank $com.appblock.ui.AppSelectionActivity  isSystemApp $com.appblock.ui.AppSelectionActivity  launch $com.appblock.ui.AppSelectionActivity  layoutInflater $com.appblock.ui.AppSelectionActivity  listOf $com.appblock.ui.AppSelectionActivity  loadApps $com.appblock.ui.AppSelectionActivity  loadInstalledApps $com.appblock.ui.AppSelectionActivity  	lowercase $com.appblock.ui.AppSelectionActivity  map $com.appblock.ui.AppSelectionActivity  menuInflater $com.appblock.ui.AppSelectionActivity  packageManager $com.appblock.ui.AppSelectionActivity  packageName $com.appblock.ui.AppSelectionActivity  selectAllVisibleApps $com.appblock.ui.AppSelectionActivity  setContentView $com.appblock.ui.AppSelectionActivity  setLayoutInflater $com.appblock.ui.AppSelectionActivity  setMenuInflater $com.appblock.ui.AppSelectionActivity  setPackageManager $com.appblock.ui.AppSelectionActivity  setPackageName $com.appblock.ui.AppSelectionActivity  setSupportActionBar $com.appblock.ui.AppSelectionActivity  setupUI $com.appblock.ui.AppSelectionActivity  showBlockedAppsCount $com.appblock.ui.AppSelectionActivity  showSystemAppWarning $com.appblock.ui.AppSelectionActivity  sortedBy $com.appblock.ui.AppSelectionActivity  supportActionBar $com.appblock.ui.AppSelectionActivity  toSet $com.appblock.ui.AppSelectionActivity  updateAppInList $com.appblock.ui.AppSelectionActivity  withContext $com.appblock.ui.AppSelectionActivity  Boolean ,com.appblock.ui.AppSelectionActivity.AppInfo  String ,com.appblock.ui.AppSelectionActivity.AppInfo  android ,com.appblock.ui.AppSelectionActivity.AppInfo  appName ,com.appblock.ui.AppSelectionActivity.AppInfo  equals ,com.appblock.ui.AppSelectionActivity.AppInfo  icon ,com.appblock.ui.AppSelectionActivity.AppInfo  	isBlocked ,com.appblock.ui.AppSelectionActivity.AppInfo  isSystemApp ,com.appblock.ui.AppSelectionActivity.AppInfo  packageName ,com.appblock.ui.AppSelectionActivity.AppInfo  ActivityAppSelectionBinding .com.appblock.ui.AppSelectionActivity.Companion  AlertDialog .com.appblock.ui.AppSelectionActivity.Companion  AppInfo .com.appblock.ui.AppSelectionActivity.Companion  AppListAdapter .com.appblock.ui.AppSelectionActivity.Companion  ApplicationInfo .com.appblock.ui.AppSelectionActivity.Companion  BlockedAppsManager .com.appblock.ui.AppSelectionActivity.Companion  Boolean .com.appblock.ui.AppSelectionActivity.Companion  Bundle .com.appblock.ui.AppSelectionActivity.Companion  CoroutineScope .com.appblock.ui.AppSelectionActivity.Companion  Dispatchers .com.appblock.ui.AppSelectionActivity.Companion  	Exception .com.appblock.ui.AppSelectionActivity.Companion  LinearLayoutManager .com.appblock.ui.AppSelectionActivity.Companion  List .com.appblock.ui.AppSelectionActivity.Companion  Menu .com.appblock.ui.AppSelectionActivity.Companion  MenuItem .com.appblock.ui.AppSelectionActivity.Companion  PackageManager .com.appblock.ui.AppSelectionActivity.Companion  R .com.appblock.ui.AppSelectionActivity.Companion  
SearchView .com.appblock.ui.AppSelectionActivity.Companion  String .com.appblock.ui.AppSelectionActivity.Companion  
SupervisorJob .com.appblock.ui.AppSelectionActivity.Companion  Toast .com.appblock.ui.AppSelectionActivity.Companion  Unit .com.appblock.ui.AppSelectionActivity.Companion  View .com.appblock.ui.AppSelectionActivity.Companion  allApps .com.appblock.ui.AppSelectionActivity.Companion  android .com.appblock.ui.AppSelectionActivity.Companion  appListAdapter .com.appblock.ui.AppSelectionActivity.Companion  apply .com.appblock.ui.AppSelectionActivity.Companion  binding .com.appblock.ui.AppSelectionActivity.Companion  cancel .com.appblock.ui.AppSelectionActivity.Companion  contains .com.appblock.ui.AppSelectionActivity.Companion  count .com.appblock.ui.AppSelectionActivity.Companion  filter .com.appblock.ui.AppSelectionActivity.Companion  
filterApps .com.appblock.ui.AppSelectionActivity.Companion  
getANDROID .com.appblock.ui.AppSelectionActivity.Companion  getAPPLY .com.appblock.ui.AppSelectionActivity.Companion  
getAndroid .com.appblock.ui.AppSelectionActivity.Companion  getApply .com.appblock.ui.AppSelectionActivity.Companion  	getCANCEL .com.appblock.ui.AppSelectionActivity.Companion  getCONTAINS .com.appblock.ui.AppSelectionActivity.Companion  getCOUNT .com.appblock.ui.AppSelectionActivity.Companion  	getCancel .com.appblock.ui.AppSelectionActivity.Companion  getContains .com.appblock.ui.AppSelectionActivity.Companion  getCount .com.appblock.ui.AppSelectionActivity.Companion  	getFILTER .com.appblock.ui.AppSelectionActivity.Companion  	getFilter .com.appblock.ui.AppSelectionActivity.Companion  
getISBlank .com.appblock.ui.AppSelectionActivity.Companion  
getIsBlank .com.appblock.ui.AppSelectionActivity.Companion  	getLAUNCH .com.appblock.ui.AppSelectionActivity.Companion  	getLISTOf .com.appblock.ui.AppSelectionActivity.Companion  getLOWERCASE .com.appblock.ui.AppSelectionActivity.Companion  	getLaunch .com.appblock.ui.AppSelectionActivity.Companion  	getListOf .com.appblock.ui.AppSelectionActivity.Companion  getLowercase .com.appblock.ui.AppSelectionActivity.Companion  getMAP .com.appblock.ui.AppSelectionActivity.Companion  getMap .com.appblock.ui.AppSelectionActivity.Companion  getSORTEDBy .com.appblock.ui.AppSelectionActivity.Companion  getSortedBy .com.appblock.ui.AppSelectionActivity.Companion  getTOSet .com.appblock.ui.AppSelectionActivity.Companion  getToSet .com.appblock.ui.AppSelectionActivity.Companion  getWITHContext .com.appblock.ui.AppSelectionActivity.Companion  getWithContext .com.appblock.ui.AppSelectionActivity.Companion  invoke .com.appblock.ui.AppSelectionActivity.Companion  isBlank .com.appblock.ui.AppSelectionActivity.Companion  launch .com.appblock.ui.AppSelectionActivity.Companion  listOf .com.appblock.ui.AppSelectionActivity.Companion  loadInstalledApps .com.appblock.ui.AppSelectionActivity.Companion  	lowercase .com.appblock.ui.AppSelectionActivity.Companion  map .com.appblock.ui.AppSelectionActivity.Companion  sortedBy .com.appblock.ui.AppSelectionActivity.Companion  toSet .com.appblock.ui.AppSelectionActivity.Companion  withContext .com.appblock.ui.AppSelectionActivity.Companion  
getBINDING Kcom.appblock.ui.AppSelectionActivity.onCreateOptionsMenu.<no name provided>  
getBinding Kcom.appblock.ui.AppSelectionActivity.onCreateOptionsMenu.<no name provided>  
getFILTERApps Kcom.appblock.ui.AppSelectionActivity.onCreateOptionsMenu.<no name provided>  
getFilterApps Kcom.appblock.ui.AppSelectionActivity.onCreateOptionsMenu.<no name provided>  Activity $com.appblock.ui.BlockOverlayActivity  ActivityBlockOverlayBinding $com.appblock.ui.BlockOverlayActivity  ActivityResultContracts $com.appblock.ui.BlockOverlayActivity  ApplicationInfo $com.appblock.ui.BlockOverlayActivity  BlockedAppsManager $com.appblock.ui.BlockOverlayActivity  Bundle $com.appblock.ui.BlockOverlayActivity  Intent $com.appblock.ui.BlockOverlayActivity  Log $com.appblock.ui.BlockOverlayActivity  PackageManager $com.appblock.ui.BlockOverlayActivity  PinAuthActivity $com.appblock.ui.BlockOverlayActivity  SettingsManager $com.appblock.ui.BlockOverlayActivity  String $com.appblock.ui.BlockOverlayActivity  TAG $com.appblock.ui.BlockOverlayActivity  
WindowManager $com.appblock.ui.BlockOverlayActivity  android $com.appblock.ui.BlockOverlayActivity  apply $com.appblock.ui.BlockOverlayActivity  binding $com.appblock.ui.BlockOverlayActivity  blockedAppsManager $com.appblock.ui.BlockOverlayActivity  blockedPackageName $com.appblock.ui.BlockOverlayActivity  finish $com.appblock.ui.BlockOverlayActivity  
getANDROID $com.appblock.ui.BlockOverlayActivity  getAPPLY $com.appblock.ui.BlockOverlayActivity  
getAndroid $com.appblock.ui.BlockOverlayActivity  getApply $com.appblock.ui.BlockOverlayActivity  	getINTENT $com.appblock.ui.BlockOverlayActivity  getISFinishing $com.appblock.ui.BlockOverlayActivity  	getIntent $com.appblock.ui.BlockOverlayActivity  getIsFinishing $com.appblock.ui.BlockOverlayActivity  getLAYOUTInflater $com.appblock.ui.BlockOverlayActivity  getLET $com.appblock.ui.BlockOverlayActivity  getLayoutInflater $com.appblock.ui.BlockOverlayActivity  getLet $com.appblock.ui.BlockOverlayActivity  getPACKAGEManager $com.appblock.ui.BlockOverlayActivity  getPackageManager $com.appblock.ui.BlockOverlayActivity  	getWINDOW $com.appblock.ui.BlockOverlayActivity  	getWindow $com.appblock.ui.BlockOverlayActivity  goToHome $com.appblock.ui.BlockOverlayActivity  intent $com.appblock.ui.BlockOverlayActivity  invoke $com.appblock.ui.BlockOverlayActivity  isFinishing $com.appblock.ui.BlockOverlayActivity  java $com.appblock.ui.BlockOverlayActivity  launchPinAuthentication $com.appblock.ui.BlockOverlayActivity  layoutInflater $com.appblock.ui.BlockOverlayActivity  let $com.appblock.ui.BlockOverlayActivity  packageManager $com.appblock.ui.BlockOverlayActivity  pinAuthLauncher $com.appblock.ui.BlockOverlayActivity  registerForActivityResult $com.appblock.ui.BlockOverlayActivity  requestMoreTime $com.appblock.ui.BlockOverlayActivity  setContentView $com.appblock.ui.BlockOverlayActivity  setFinishing $com.appblock.ui.BlockOverlayActivity  	setIntent $com.appblock.ui.BlockOverlayActivity  setLayoutInflater $com.appblock.ui.BlockOverlayActivity  setPackageManager $com.appblock.ui.BlockOverlayActivity  	setWindow $com.appblock.ui.BlockOverlayActivity  settingsManager $com.appblock.ui.BlockOverlayActivity  setupUI $com.appblock.ui.BlockOverlayActivity  source $com.appblock.ui.BlockOverlayActivity  
startActivity $com.appblock.ui.BlockOverlayActivity  updateBlockedAppInfo $com.appblock.ui.BlockOverlayActivity  window $com.appblock.ui.BlockOverlayActivity  Activity .com.appblock.ui.BlockOverlayActivity.Companion  ActivityBlockOverlayBinding .com.appblock.ui.BlockOverlayActivity.Companion  ActivityResultContracts .com.appblock.ui.BlockOverlayActivity.Companion  ApplicationInfo .com.appblock.ui.BlockOverlayActivity.Companion  BlockedAppsManager .com.appblock.ui.BlockOverlayActivity.Companion  Bundle .com.appblock.ui.BlockOverlayActivity.Companion  Intent .com.appblock.ui.BlockOverlayActivity.Companion  Log .com.appblock.ui.BlockOverlayActivity.Companion  PackageManager .com.appblock.ui.BlockOverlayActivity.Companion  PinAuthActivity .com.appblock.ui.BlockOverlayActivity.Companion  SettingsManager .com.appblock.ui.BlockOverlayActivity.Companion  String .com.appblock.ui.BlockOverlayActivity.Companion  TAG .com.appblock.ui.BlockOverlayActivity.Companion  
WindowManager .com.appblock.ui.BlockOverlayActivity.Companion  android .com.appblock.ui.BlockOverlayActivity.Companion  apply .com.appblock.ui.BlockOverlayActivity.Companion  blockedPackageName .com.appblock.ui.BlockOverlayActivity.Companion  
getANDROID .com.appblock.ui.BlockOverlayActivity.Companion  getAPPLY .com.appblock.ui.BlockOverlayActivity.Companion  
getAndroid .com.appblock.ui.BlockOverlayActivity.Companion  getApply .com.appblock.ui.BlockOverlayActivity.Companion  getLET .com.appblock.ui.BlockOverlayActivity.Companion  getLet .com.appblock.ui.BlockOverlayActivity.Companion  invoke .com.appblock.ui.BlockOverlayActivity.Companion  java .com.appblock.ui.BlockOverlayActivity.Companion  let .com.appblock.ui.BlockOverlayActivity.Companion  Activity com.appblock.ui.MainActivity  ActivityMainBinding com.appblock.ui.MainActivity  ActivityResultContracts com.appblock.ui.MainActivity  AlertDialog com.appblock.ui.MainActivity  AppSelectionActivity com.appblock.ui.MainActivity  BlockedAppsManager com.appblock.ui.MainActivity  Boolean com.appblock.ui.MainActivity  Bundle com.appblock.ui.MainActivity  	Exception com.appblock.ui.MainActivity  Intent com.appblock.ui.MainActivity  Log com.appblock.ui.MainActivity  Menu com.appblock.ui.MainActivity  MenuItem com.appblock.ui.MainActivity  PermissionUtils com.appblock.ui.MainActivity  R com.appblock.ui.MainActivity  SettingsManager com.appblock.ui.MainActivity  TAG com.appblock.ui.MainActivity  Toast com.appblock.ui.MainActivity  Unit com.appblock.ui.MainActivity  UsageStatsMonitorService com.appblock.ui.MainActivity  all com.appblock.ui.MainActivity  android com.appblock.ui.MainActivity  binding com.appblock.ui.MainActivity  blockedAppsManager com.appblock.ui.MainActivity  checkFirstTimeSetup com.appblock.ui.MainActivity  com com.appblock.ui.MainActivity  count com.appblock.ui.MainActivity  deviceAdminLauncher com.appblock.ui.MainActivity  getALL com.appblock.ui.MainActivity  
getANDROID com.appblock.ui.MainActivity  getAll com.appblock.ui.MainActivity  
getAndroid com.appblock.ui.MainActivity  getCOUNT com.appblock.ui.MainActivity  getCount com.appblock.ui.MainActivity  getJOINToString com.appblock.ui.MainActivity  getJoinToString com.appblock.ui.MainActivity  getLAYOUTInflater com.appblock.ui.MainActivity  getLayoutInflater com.appblock.ui.MainActivity  getMENUInflater com.appblock.ui.MainActivity  getMenuInflater com.appblock.ui.MainActivity  getTOString com.appblock.ui.MainActivity  getToString com.appblock.ui.MainActivity  invoke com.appblock.ui.MainActivity  java com.appblock.ui.MainActivity  joinToString com.appblock.ui.MainActivity  layoutInflater com.appblock.ui.MainActivity  menuInflater com.appblock.ui.MainActivity  registerForActivityResult com.appblock.ui.MainActivity  requestDeviceAdmin com.appblock.ui.MainActivity  requestMissingPermissions com.appblock.ui.MainActivity  setContentView com.appblock.ui.MainActivity  setLayoutInflater com.appblock.ui.MainActivity  setMenuInflater com.appblock.ui.MainActivity  setSupportActionBar com.appblock.ui.MainActivity  settingsManager com.appblock.ui.MainActivity  setupUI com.appblock.ui.MainActivity  showAboutDialog com.appblock.ui.MainActivity  showFirstTimeSetupDialog com.appblock.ui.MainActivity  showPermissionsDialog com.appblock.ui.MainActivity  showPinSetupDialog com.appblock.ui.MainActivity  showPinVerificationDialog com.appblock.ui.MainActivity  
startActivity com.appblock.ui.MainActivity  startForegroundService com.appblock.ui.MainActivity  startMonitoringServices com.appblock.ui.MainActivity  startService com.appblock.ui.MainActivity  stopMonitoringServices com.appblock.ui.MainActivity  stopService com.appblock.ui.MainActivity  toString com.appblock.ui.MainActivity  toggleAppBlocking com.appblock.ui.MainActivity  updateUI com.appblock.ui.MainActivity  Activity &com.appblock.ui.MainActivity.Companion  ActivityMainBinding &com.appblock.ui.MainActivity.Companion  ActivityResultContracts &com.appblock.ui.MainActivity.Companion  AlertDialog &com.appblock.ui.MainActivity.Companion  AppSelectionActivity &com.appblock.ui.MainActivity.Companion  BlockedAppsManager &com.appblock.ui.MainActivity.Companion  Boolean &com.appblock.ui.MainActivity.Companion  Bundle &com.appblock.ui.MainActivity.Companion  	Exception &com.appblock.ui.MainActivity.Companion  Intent &com.appblock.ui.MainActivity.Companion  Log &com.appblock.ui.MainActivity.Companion  Menu &com.appblock.ui.MainActivity.Companion  MenuItem &com.appblock.ui.MainActivity.Companion  PermissionUtils &com.appblock.ui.MainActivity.Companion  R &com.appblock.ui.MainActivity.Companion  SettingsManager &com.appblock.ui.MainActivity.Companion  TAG &com.appblock.ui.MainActivity.Companion  Toast &com.appblock.ui.MainActivity.Companion  Unit &com.appblock.ui.MainActivity.Companion  UsageStatsMonitorService &com.appblock.ui.MainActivity.Companion  all &com.appblock.ui.MainActivity.Companion  android &com.appblock.ui.MainActivity.Companion  com &com.appblock.ui.MainActivity.Companion  count &com.appblock.ui.MainActivity.Companion  getALL &com.appblock.ui.MainActivity.Companion  
getANDROID &com.appblock.ui.MainActivity.Companion  getAll &com.appblock.ui.MainActivity.Companion  
getAndroid &com.appblock.ui.MainActivity.Companion  getCOUNT &com.appblock.ui.MainActivity.Companion  getCount &com.appblock.ui.MainActivity.Companion  getJOINToString &com.appblock.ui.MainActivity.Companion  getJoinToString &com.appblock.ui.MainActivity.Companion  getTOString &com.appblock.ui.MainActivity.Companion  getToString &com.appblock.ui.MainActivity.Companion  invoke &com.appblock.ui.MainActivity.Companion  java &com.appblock.ui.MainActivity.Companion  joinToString &com.appblock.ui.MainActivity.Companion  toString &com.appblock.ui.MainActivity.Companion  Activity com.appblock.ui.PinAuthActivity  ActivityPinAuthBinding com.appblock.ui.PinAuthActivity  AlertDialog com.appblock.ui.PinAuthActivity  Bundle com.appblock.ui.PinAuthActivity  CharSequence com.appblock.ui.PinAuthActivity  	Companion com.appblock.ui.PinAuthActivity  Editable com.appblock.ui.PinAuthActivity  	Exception com.appblock.ui.PinAuthActivity  Int com.appblock.ui.PinAuthActivity  Intent com.appblock.ui.PinAuthActivity  Log com.appblock.ui.PinAuthActivity  Long com.appblock.ui.PinAuthActivity  MAX_ATTEMPTS com.appblock.ui.PinAuthActivity  SettingsManager com.appblock.ui.PinAuthActivity  String com.appblock.ui.PinAuthActivity  System com.appblock.ui.PinAuthActivity  TAG com.appblock.ui.PinAuthActivity  TextWatcher com.appblock.ui.PinAuthActivity  Toast com.appblock.ui.PinAuthActivity  action com.appblock.ui.PinAuthActivity  android com.appblock.ui.PinAuthActivity  apply com.appblock.ui.PinAuthActivity  arrayOf com.appblock.ui.PinAuthActivity  attemptCount com.appblock.ui.PinAuthActivity  binding com.appblock.ui.PinAuthActivity  blockedPackageName com.appblock.ui.PinAuthActivity  dropLast com.appblock.ui.PinAuthActivity  finish com.appblock.ui.PinAuthActivity  forEachIndexed com.appblock.ui.PinAuthActivity  
getANDROID com.appblock.ui.PinAuthActivity  getAPPLY com.appblock.ui.PinAuthActivity  
getARRAYOf com.appblock.ui.PinAuthActivity  
getAndroid com.appblock.ui.PinAuthActivity  getApply com.appblock.ui.PinAuthActivity  
getArrayOf com.appblock.ui.PinAuthActivity  getDROPLast com.appblock.ui.PinAuthActivity  getDropLast com.appblock.ui.PinAuthActivity  getFOREachIndexed com.appblock.ui.PinAuthActivity  getForEachIndexed com.appblock.ui.PinAuthActivity  	getINTENT com.appblock.ui.PinAuthActivity  
getISNotEmpty com.appblock.ui.PinAuthActivity  	getIntent com.appblock.ui.PinAuthActivity  
getIsNotEmpty com.appblock.ui.PinAuthActivity  getJAVA com.appblock.ui.PinAuthActivity  getJava com.appblock.ui.PinAuthActivity  getLAYOUTInflater com.appblock.ui.PinAuthActivity  getLET com.appblock.ui.PinAuthActivity  	getLISTOf com.appblock.ui.PinAuthActivity  getLayoutInflater com.appblock.ui.PinAuthActivity  getLet com.appblock.ui.PinAuthActivity  	getListOf com.appblock.ui.PinAuthActivity  getPACKAGEManager com.appblock.ui.PinAuthActivity  getPackageManager com.appblock.ui.PinAuthActivity  getTOString com.appblock.ui.PinAuthActivity  getTimeUntilBedtime com.appblock.ui.PinAuthActivity  getToString com.appblock.ui.PinAuthActivity  handleFailedAuth com.appblock.ui.PinAuthActivity  handleSuccessfulAuth com.appblock.ui.PinAuthActivity  intent com.appblock.ui.PinAuthActivity  invoke com.appblock.ui.PinAuthActivity  
isNotEmpty com.appblock.ui.PinAuthActivity  java com.appblock.ui.PinAuthActivity  layoutInflater com.appblock.ui.PinAuthActivity  let com.appblock.ui.PinAuthActivity  listOf com.appblock.ui.PinAuthActivity  packageManager com.appblock.ui.PinAuthActivity  returnResult com.appblock.ui.PinAuthActivity  setContentView com.appblock.ui.PinAuthActivity  	setIntent com.appblock.ui.PinAuthActivity  setLayoutInflater com.appblock.ui.PinAuthActivity  setPackageManager com.appblock.ui.PinAuthActivity  	setResult com.appblock.ui.PinAuthActivity  settingsManager com.appblock.ui.PinAuthActivity  setupNumberPad com.appblock.ui.PinAuthActivity  setupUI com.appblock.ui.PinAuthActivity  showTemporaryAllowDialog com.appblock.ui.PinAuthActivity  showTimeRequestDialog com.appblock.ui.PinAuthActivity  toString com.appblock.ui.PinAuthActivity  updateUI com.appblock.ui.PinAuthActivity  	verifyPin com.appblock.ui.PinAuthActivity  Activity )com.appblock.ui.PinAuthActivity.Companion  ActivityPinAuthBinding )com.appblock.ui.PinAuthActivity.Companion  AlertDialog )com.appblock.ui.PinAuthActivity.Companion  Bundle )com.appblock.ui.PinAuthActivity.Companion  CharSequence )com.appblock.ui.PinAuthActivity.Companion  Editable )com.appblock.ui.PinAuthActivity.Companion  	Exception )com.appblock.ui.PinAuthActivity.Companion  Int )com.appblock.ui.PinAuthActivity.Companion  Intent )com.appblock.ui.PinAuthActivity.Companion  Log )com.appblock.ui.PinAuthActivity.Companion  Long )com.appblock.ui.PinAuthActivity.Companion  MAX_ATTEMPTS )com.appblock.ui.PinAuthActivity.Companion  SettingsManager )com.appblock.ui.PinAuthActivity.Companion  String )com.appblock.ui.PinAuthActivity.Companion  System )com.appblock.ui.PinAuthActivity.Companion  TAG )com.appblock.ui.PinAuthActivity.Companion  TextWatcher )com.appblock.ui.PinAuthActivity.Companion  Toast )com.appblock.ui.PinAuthActivity.Companion  android )com.appblock.ui.PinAuthActivity.Companion  apply )com.appblock.ui.PinAuthActivity.Companion  arrayOf )com.appblock.ui.PinAuthActivity.Companion  blockedPackageName )com.appblock.ui.PinAuthActivity.Companion  dropLast )com.appblock.ui.PinAuthActivity.Companion  forEachIndexed )com.appblock.ui.PinAuthActivity.Companion  
getANDROID )com.appblock.ui.PinAuthActivity.Companion  getAPPLY )com.appblock.ui.PinAuthActivity.Companion  
getARRAYOf )com.appblock.ui.PinAuthActivity.Companion  
getAndroid )com.appblock.ui.PinAuthActivity.Companion  getApply )com.appblock.ui.PinAuthActivity.Companion  
getArrayOf )com.appblock.ui.PinAuthActivity.Companion  getDROPLast )com.appblock.ui.PinAuthActivity.Companion  getDropLast )com.appblock.ui.PinAuthActivity.Companion  getFOREachIndexed )com.appblock.ui.PinAuthActivity.Companion  getForEachIndexed )com.appblock.ui.PinAuthActivity.Companion  
getISNotEmpty )com.appblock.ui.PinAuthActivity.Companion  
getIsNotEmpty )com.appblock.ui.PinAuthActivity.Companion  getJAVA )com.appblock.ui.PinAuthActivity.Companion  getJava )com.appblock.ui.PinAuthActivity.Companion  getLET )com.appblock.ui.PinAuthActivity.Companion  	getLISTOf )com.appblock.ui.PinAuthActivity.Companion  getLet )com.appblock.ui.PinAuthActivity.Companion  	getListOf )com.appblock.ui.PinAuthActivity.Companion  getTOString )com.appblock.ui.PinAuthActivity.Companion  getToString )com.appblock.ui.PinAuthActivity.Companion  invoke )com.appblock.ui.PinAuthActivity.Companion  
isNotEmpty )com.appblock.ui.PinAuthActivity.Companion  java )com.appblock.ui.PinAuthActivity.Companion  let )com.appblock.ui.PinAuthActivity.Companion  listOf )com.appblock.ui.PinAuthActivity.Companion  toString )com.appblock.ui.PinAuthActivity.Companion  	verifyPin )com.appblock.ui.PinAuthActivity.Companion  getVERIFYPin :com.appblock.ui.PinAuthActivity.setupUI.<no name provided>  getVerifyPin :com.appblock.ui.PinAuthActivity.setupUI.<no name provided>  AppInfo *com.appblock.ui.SimpleAppSelectionActivity  AppListAdapter *com.appblock.ui.SimpleAppSelectionActivity  BaseAdapter *com.appblock.ui.SimpleAppSelectionActivity  BlockedAppsManager *com.appblock.ui.SimpleAppSelectionActivity  Boolean *com.appblock.ui.SimpleAppSelectionActivity  Bundle *com.appblock.ui.SimpleAppSelectionActivity  	Companion *com.appblock.ui.SimpleAppSelectionActivity  CoroutineScope *com.appblock.ui.SimpleAppSelectionActivity  Dispatchers *com.appblock.ui.SimpleAppSelectionActivity  	Exception *com.appblock.ui.SimpleAppSelectionActivity  	ImageView *com.appblock.ui.SimpleAppSelectionActivity  Int *com.appblock.ui.SimpleAppSelectionActivity  LinearLayout *com.appblock.ui.SimpleAppSelectionActivity  List *com.appblock.ui.SimpleAppSelectionActivity  ListView *com.appblock.ui.SimpleAppSelectionActivity  Log *com.appblock.ui.SimpleAppSelectionActivity  Long *com.appblock.ui.SimpleAppSelectionActivity  PackageManager *com.appblock.ui.SimpleAppSelectionActivity  ProgressBar *com.appblock.ui.SimpleAppSelectionActivity  String *com.appblock.ui.SimpleAppSelectionActivity  
SupervisorJob *com.appblock.ui.SimpleAppSelectionActivity  TAG *com.appblock.ui.SimpleAppSelectionActivity  TextView *com.appblock.ui.SimpleAppSelectionActivity  Toast *com.appblock.ui.SimpleAppSelectionActivity  View *com.appblock.ui.SimpleAppSelectionActivity  adapter *com.appblock.ui.SimpleAppSelectionActivity  allApps *com.appblock.ui.SimpleAppSelectionActivity  android *com.appblock.ui.SimpleAppSelectionActivity  apply *com.appblock.ui.SimpleAppSelectionActivity  blockedAppsManager *com.appblock.ui.SimpleAppSelectionActivity  cancel *com.appblock.ui.SimpleAppSelectionActivity  coroutineScope *com.appblock.ui.SimpleAppSelectionActivity  createLayout *com.appblock.ui.SimpleAppSelectionActivity  filter *com.appblock.ui.SimpleAppSelectionActivity  
getANDROID *com.appblock.ui.SimpleAppSelectionActivity  getAPPLY *com.appblock.ui.SimpleAppSelectionActivity  
getAndroid *com.appblock.ui.SimpleAppSelectionActivity  getApply *com.appblock.ui.SimpleAppSelectionActivity  	getCANCEL *com.appblock.ui.SimpleAppSelectionActivity  	getCancel *com.appblock.ui.SimpleAppSelectionActivity  	getFILTER *com.appblock.ui.SimpleAppSelectionActivity  	getFilter *com.appblock.ui.SimpleAppSelectionActivity  	getLAUNCH *com.appblock.ui.SimpleAppSelectionActivity  getLOWERCASE *com.appblock.ui.SimpleAppSelectionActivity  	getLaunch *com.appblock.ui.SimpleAppSelectionActivity  getLowercase *com.appblock.ui.SimpleAppSelectionActivity  getMAP *com.appblock.ui.SimpleAppSelectionActivity  getMUTABLEListOf *com.appblock.ui.SimpleAppSelectionActivity  getMap *com.appblock.ui.SimpleAppSelectionActivity  getMutableListOf *com.appblock.ui.SimpleAppSelectionActivity  getPACKAGEManager *com.appblock.ui.SimpleAppSelectionActivity  getPACKAGEName *com.appblock.ui.SimpleAppSelectionActivity  getPackageManager *com.appblock.ui.SimpleAppSelectionActivity  getPackageName *com.appblock.ui.SimpleAppSelectionActivity  getSORTEDBy *com.appblock.ui.SimpleAppSelectionActivity  getSortedBy *com.appblock.ui.SimpleAppSelectionActivity  getTOSet *com.appblock.ui.SimpleAppSelectionActivity  getToSet *com.appblock.ui.SimpleAppSelectionActivity  getWITHContext *com.appblock.ui.SimpleAppSelectionActivity  getWithContext *com.appblock.ui.SimpleAppSelectionActivity  invoke *com.appblock.ui.SimpleAppSelectionActivity  launch *com.appblock.ui.SimpleAppSelectionActivity  listView *com.appblock.ui.SimpleAppSelectionActivity  loadApps *com.appblock.ui.SimpleAppSelectionActivity  loadInstalledApps *com.appblock.ui.SimpleAppSelectionActivity  	lowercase *com.appblock.ui.SimpleAppSelectionActivity  map *com.appblock.ui.SimpleAppSelectionActivity  
mutableListOf *com.appblock.ui.SimpleAppSelectionActivity  packageManager *com.appblock.ui.SimpleAppSelectionActivity  packageName *com.appblock.ui.SimpleAppSelectionActivity  progressBar *com.appblock.ui.SimpleAppSelectionActivity  setContentView *com.appblock.ui.SimpleAppSelectionActivity  setPackageManager *com.appblock.ui.SimpleAppSelectionActivity  setPackageName *com.appblock.ui.SimpleAppSelectionActivity  
setupListView *com.appblock.ui.SimpleAppSelectionActivity  sortedBy *com.appblock.ui.SimpleAppSelectionActivity  toSet *com.appblock.ui.SimpleAppSelectionActivity  toggleAppBlocking *com.appblock.ui.SimpleAppSelectionActivity  withContext *com.appblock.ui.SimpleAppSelectionActivity  Boolean 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  String 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  android 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  appName 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  icon 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  	isBlocked 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  packageName 2com.appblock.ui.SimpleAppSelectionActivity.AppInfo  AppInfo 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  	ImageView 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  Int 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  LinearLayout 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  Long 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  TextView 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  View 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  allApps 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  android 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  apply 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  
getALLApps 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  
getANDROID 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  getAPPLY 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  
getAllApps 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  
getAndroid 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  getApply 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  notifyDataSetChanged 9com.appblock.ui.SimpleAppSelectionActivity.AppListAdapter  AppInfo 4com.appblock.ui.SimpleAppSelectionActivity.Companion  BaseAdapter 4com.appblock.ui.SimpleAppSelectionActivity.Companion  BlockedAppsManager 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Boolean 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Bundle 4com.appblock.ui.SimpleAppSelectionActivity.Companion  CoroutineScope 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Dispatchers 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	Exception 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	ImageView 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Int 4com.appblock.ui.SimpleAppSelectionActivity.Companion  LinearLayout 4com.appblock.ui.SimpleAppSelectionActivity.Companion  List 4com.appblock.ui.SimpleAppSelectionActivity.Companion  ListView 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Log 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Long 4com.appblock.ui.SimpleAppSelectionActivity.Companion  PackageManager 4com.appblock.ui.SimpleAppSelectionActivity.Companion  ProgressBar 4com.appblock.ui.SimpleAppSelectionActivity.Companion  String 4com.appblock.ui.SimpleAppSelectionActivity.Companion  
SupervisorJob 4com.appblock.ui.SimpleAppSelectionActivity.Companion  TAG 4com.appblock.ui.SimpleAppSelectionActivity.Companion  TextView 4com.appblock.ui.SimpleAppSelectionActivity.Companion  Toast 4com.appblock.ui.SimpleAppSelectionActivity.Companion  View 4com.appblock.ui.SimpleAppSelectionActivity.Companion  allApps 4com.appblock.ui.SimpleAppSelectionActivity.Companion  android 4com.appblock.ui.SimpleAppSelectionActivity.Companion  apply 4com.appblock.ui.SimpleAppSelectionActivity.Companion  cancel 4com.appblock.ui.SimpleAppSelectionActivity.Companion  filter 4com.appblock.ui.SimpleAppSelectionActivity.Companion  
getANDROID 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getAPPLY 4com.appblock.ui.SimpleAppSelectionActivity.Companion  
getAndroid 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getApply 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	getCANCEL 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	getCancel 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	getFILTER 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	getFilter 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	getLAUNCH 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getLOWERCASE 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	getLaunch 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getLowercase 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getMAP 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getMUTABLEListOf 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getMap 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getMutableListOf 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getSORTEDBy 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getSortedBy 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getTOSet 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getToSet 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getWITHContext 4com.appblock.ui.SimpleAppSelectionActivity.Companion  getWithContext 4com.appblock.ui.SimpleAppSelectionActivity.Companion  invoke 4com.appblock.ui.SimpleAppSelectionActivity.Companion  launch 4com.appblock.ui.SimpleAppSelectionActivity.Companion  listView 4com.appblock.ui.SimpleAppSelectionActivity.Companion  loadInstalledApps 4com.appblock.ui.SimpleAppSelectionActivity.Companion  	lowercase 4com.appblock.ui.SimpleAppSelectionActivity.Companion  map 4com.appblock.ui.SimpleAppSelectionActivity.Companion  
mutableListOf 4com.appblock.ui.SimpleAppSelectionActivity.Companion  progressBar 4com.appblock.ui.SimpleAppSelectionActivity.Companion  
setupListView 4com.appblock.ui.SimpleAppSelectionActivity.Companion  sortedBy 4com.appblock.ui.SimpleAppSelectionActivity.Companion  toSet 4com.appblock.ui.SimpleAppSelectionActivity.Companion  withContext 4com.appblock.ui.SimpleAppSelectionActivity.Companion  AppListAdapter com.appblock.ui.adapter  Boolean com.appblock.ui.adapter  Int com.appblock.ui.adapter  ItemAppBinding com.appblock.ui.adapter  LayoutInflater com.appblock.ui.adapter  Unit com.appblock.ui.adapter  android com.appblock.ui.adapter  androidx com.appblock.ui.adapter  apply com.appblock.ui.adapter  com com.appblock.ui.adapter  onAppSelectionChanged com.appblock.ui.adapter  AppDiffCallback &com.appblock.ui.adapter.AppListAdapter  AppSelectionActivity &com.appblock.ui.adapter.AppListAdapter  
AppViewHolder &com.appblock.ui.adapter.AppListAdapter  Boolean &com.appblock.ui.adapter.AppListAdapter  DiffUtil &com.appblock.ui.adapter.AppListAdapter  Int &com.appblock.ui.adapter.AppListAdapter  ItemAppBinding &com.appblock.ui.adapter.AppListAdapter  LayoutInflater &com.appblock.ui.adapter.AppListAdapter  RecyclerView &com.appblock.ui.adapter.AppListAdapter  Unit &com.appblock.ui.adapter.AppListAdapter  	ViewGroup &com.appblock.ui.adapter.AppListAdapter  android &com.appblock.ui.adapter.AppListAdapter  androidx &com.appblock.ui.adapter.AppListAdapter  apply &com.appblock.ui.adapter.AppListAdapter  com &com.appblock.ui.adapter.AppListAdapter  
getANDROID &com.appblock.ui.adapter.AppListAdapter  getANDROIDX &com.appblock.ui.adapter.AppListAdapter  getAPPLY &com.appblock.ui.adapter.AppListAdapter  
getAndroid &com.appblock.ui.adapter.AppListAdapter  getAndroidx &com.appblock.ui.adapter.AppListAdapter  getApply &com.appblock.ui.adapter.AppListAdapter  getCOM &com.appblock.ui.adapter.AppListAdapter  getCom &com.appblock.ui.adapter.AppListAdapter  getItem &com.appblock.ui.adapter.AppListAdapter  invoke &com.appblock.ui.adapter.AppListAdapter  notifyDataSetChanged &com.appblock.ui.adapter.AppListAdapter  notifyItemChanged &com.appblock.ui.adapter.AppListAdapter  onAppSelectionChanged &com.appblock.ui.adapter.AppListAdapter  
submitList &com.appblock.ui.adapter.AppListAdapter  AppSelectionActivity 6com.appblock.ui.adapter.AppListAdapter.AppDiffCallback  Boolean 6com.appblock.ui.adapter.AppListAdapter.AppDiffCallback  AppSelectionActivity 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  ItemAppBinding 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  android 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  androidx 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  apply 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  bind 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  binding 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  com 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  
getANDROID 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getANDROIDX 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getAPPLY 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  
getAndroid 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getAndroidx 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getApply 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getCOM 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getCom 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getONAppSelectionChanged 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  getOnAppSelectionChanged 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  invoke 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  onAppSelectionChanged 4com.appblock.ui.adapter.AppListAdapter.AppViewHolder  AppBlockAccessibilityService com.appblock.utils  
AppOpsManager com.appblock.utils  Boolean com.appblock.utils  Context com.appblock.utils  Intent com.appblock.utils  List com.appblock.utils  Log com.appblock.utils  Map com.appblock.utils  PermissionUtils com.appblock.utils  Settings com.appblock.utils  String com.appblock.utils  android com.appblock.utils  androidx com.appblock.utils  com com.appblock.utils  
component1 com.appblock.utils  
component2 com.appblock.utils  contains com.appblock.utils  forEach com.appblock.utils  
isNullOrEmpty com.appblock.utils  java com.appblock.utils  mapOf com.appblock.utils  
mutableListOf com.appblock.utils  to com.appblock.utils  AppBlockAccessibilityService "com.appblock.utils.PermissionUtils  
AppOpsManager "com.appblock.utils.PermissionUtils  Boolean "com.appblock.utils.PermissionUtils  Context "com.appblock.utils.PermissionUtils  Intent "com.appblock.utils.PermissionUtils  List "com.appblock.utils.PermissionUtils  Log "com.appblock.utils.PermissionUtils  Map "com.appblock.utils.PermissionUtils  Settings "com.appblock.utils.PermissionUtils  String "com.appblock.utils.PermissionUtils  TAG "com.appblock.utils.PermissionUtils  android "com.appblock.utils.PermissionUtils  androidx "com.appblock.utils.PermissionUtils  canDrawOverlays "com.appblock.utils.PermissionUtils  com "com.appblock.utils.PermissionUtils  
component1 "com.appblock.utils.PermissionUtils  
component2 "com.appblock.utils.PermissionUtils  contains "com.appblock.utils.PermissionUtils  
getANDROID "com.appblock.utils.PermissionUtils  getANDROIDX "com.appblock.utils.PermissionUtils  
getAndroid "com.appblock.utils.PermissionUtils  getAndroidx "com.appblock.utils.PermissionUtils  getCOM "com.appblock.utils.PermissionUtils  getCONTAINS "com.appblock.utils.PermissionUtils  getCom "com.appblock.utils.PermissionUtils  
getComponent1 "com.appblock.utils.PermissionUtils  
getComponent2 "com.appblock.utils.PermissionUtils  getContains "com.appblock.utils.PermissionUtils  getISNullOrEmpty "com.appblock.utils.PermissionUtils  getIsNullOrEmpty "com.appblock.utils.PermissionUtils  getMAPOf "com.appblock.utils.PermissionUtils  getMUTABLEListOf "com.appblock.utils.PermissionUtils  getMapOf "com.appblock.utils.PermissionUtils  getMissingPermissions "com.appblock.utils.PermissionUtils  getMutableListOf "com.appblock.utils.PermissionUtils  getPermissionStatusSummary "com.appblock.utils.PermissionUtils  getTO "com.appblock.utils.PermissionUtils  getTo "com.appblock.utils.PermissionUtils  hasAllCriticalPermissions "com.appblock.utils.PermissionUtils  hasNotificationPermission "com.appblock.utils.PermissionUtils  hasUsageStatsPermission "com.appblock.utils.PermissionUtils  isAccessibilityServiceEnabled "com.appblock.utils.PermissionUtils  isDeviceAdminActive "com.appblock.utils.PermissionUtils  
isNullOrEmpty "com.appblock.utils.PermissionUtils  java "com.appblock.utils.PermissionUtils  mapOf "com.appblock.utils.PermissionUtils  
mutableListOf "com.appblock.utils.PermissionUtils  openAccessibilitySettings "com.appblock.utils.PermissionUtils  openOverlaySettings "com.appblock.utils.PermissionUtils  openUsageStatsSettings "com.appblock.utils.PermissionUtils  requestDeviceAdmin "com.appblock.utils.PermissionUtils  to "com.appblock.utils.PermissionUtils  getISEnabled 1com.google.android.material.button.MaterialButton  
getISSelected 1com.google.android.material.button.MaterialButton  getIsEnabled 1com.google.android.material.button.MaterialButton  
getIsSelected 1com.google.android.material.button.MaterialButton  getTEXT 1com.google.android.material.button.MaterialButton  getText 1com.google.android.material.button.MaterialButton  
getVISIBILITY 1com.google.android.material.button.MaterialButton  
getVisibility 1com.google.android.material.button.MaterialButton  	isEnabled 1com.google.android.material.button.MaterialButton  
isSelected 1com.google.android.material.button.MaterialButton  
setEnabled 1com.google.android.material.button.MaterialButton  setOnClickListener 1com.google.android.material.button.MaterialButton  setSelected 1com.google.android.material.button.MaterialButton  setText 1com.google.android.material.button.MaterialButton  
setVisibility 1com.google.android.material.button.MaterialButton  text 1com.google.android.material.button.MaterialButton  
visibility 1com.google.android.material.button.MaterialButton  setOnClickListener 1com.google.android.material.card.MaterialCardView  getISChecked 9com.google.android.material.switchmaterial.SwitchMaterial  getIsChecked 9com.google.android.material.switchmaterial.SwitchMaterial  	isChecked 9com.google.android.material.switchmaterial.SwitchMaterial  
setChecked 9com.google.android.material.switchmaterial.SwitchMaterial  setOnCheckedChangeListener 9com.google.android.material.switchmaterial.SwitchMaterial  TextInputEditText %com.google.android.material.textfield  addTextChangedListener 7com.google.android.material.textfield.TextInputEditText  error 7com.google.android.material.textfield.TextInputEditText  getERROR 7com.google.android.material.textfield.TextInputEditText  getError 7com.google.android.material.textfield.TextInputEditText  getTEXT 7com.google.android.material.textfield.TextInputEditText  getText 7com.google.android.material.textfield.TextInputEditText  setError 7com.google.android.material.textfield.TextInputEditText  setSelection 7com.google.android.material.textfield.TextInputEditText  setText 7com.google.android.material.textfield.TextInputEditText  text 7com.google.android.material.textfield.TextInputEditText  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  ACTION_CHECK_PERMISSIONS 	java.lang  AccessibilityEvent 	java.lang  AccessibilityServiceInfo 	java.lang  Activity 	java.lang  ActivityAppSelectionBinding 	java.lang  ActivityBlockOverlayBinding 	java.lang  ActivityMainBinding 	java.lang  ActivityPinAuthBinding 	java.lang  ActivityResultContracts 	java.lang  AlertDialog 	java.lang  AppBlockAccessibilityService 	java.lang  AppBlockDeviceAdminReceiver 	java.lang  AppInfo 	java.lang  AppListAdapter 	java.lang  
AppOpsManager 	java.lang  AppSelectionActivity 	java.lang  ApplicationInfo 	java.lang  BlockedAppsManager 	java.lang  Build 	java.lang  Button 	java.lang  
CHANNEL_ID 	java.lang  Class 	java.lang  
ComponentName 	java.lang  ConcurrentHashMap 	java.lang  Context 	java.lang  CoreFunctionalityTest 	java.lang  CoroutineScope 	java.lang  DevicePolicyManager 	java.lang  Dispatchers 	java.lang  	Exception 	java.lang  	Executors 	java.lang  FrameLayout 	java.lang  Gravity 	java.lang  Gson 	java.lang  Handler 	java.lang  	ImageView 	java.lang  Intent 	java.lang  InterruptedException 	java.lang  ItemAppBinding 	java.lang  KEY_APP_BLOCKING_ENABLED 	java.lang  KEY_BLOCKED_APPS 	java.lang  KEY_BOOT_EVENTS 	java.lang  KEY_DEVICE_ADMIN_ENABLED 	java.lang  KEY_FIRST_SETUP_COMPLETE 	java.lang  KEY_HIDE_APP_ICON 	java.lang  KEY_LAST_ACTIVITY 	java.lang  KEY_LOCK_ON_TAMPERING 	java.lang  KEY_PARENT_PIN 	java.lang  KEY_SETTINGS_BLOCKED 	java.lang  KEY_TAMPERING_ATTEMPTS 	java.lang  KEY_TEMPORARY_ALLOWS 	java.lang  KEY_TIME_LIMITS 	java.lang  LayoutInflater 	java.lang  LinearLayout 	java.lang  LinearLayoutManager 	java.lang  ListView 	java.lang  Log 	java.lang  Looper 	java.lang  MAX_ATTEMPTS 	java.lang  NOTIFICATION_ID 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  POLLING_INTERVAL_SECONDS 	java.lang  
PREFS_NAME 	java.lang  PackageManager 	java.lang  PermissionUtils 	java.lang  PinAuthActivity 	java.lang  PixelFormat 	java.lang  ProgressBar 	java.lang  R 	java.lang  Runnable 	java.lang  START_STICKY 	java.lang  Settings 	java.lang  SettingsManager 	java.lang  
SupervisorJob 	java.lang  System 	java.lang  TAG 	java.lang  TextView 	java.lang  Thread 	java.lang  
TimeLimitInfo 	java.lang  TimeUnit 	java.lang  Toast 	java.lang  UsageEvents 	java.lang  UsageStatsManager 	java.lang  UsageStatsMonitorService 	java.lang  View 	java.lang  
WindowManager 	java.lang  all 	java.lang  allApps 	java.lang  android 	java.lang  androidx 	java.lang  appListAdapter 	java.lang  apply 	java.lang  arrayOf 	java.lang  binding 	java.lang  blockedPackageName 	java.lang  cancel 	java.lang  com 	java.lang  
component1 	java.lang  
component2 	java.lang  contains 	java.lang  count 	java.lang  dropLast 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  filter 	java.lang  
filterApps 	java.lang  filterValues 	java.lang  find 	java.lang  forEach 	java.lang  forEachIndexed 	java.lang  getComponentName 	java.lang  instance 	java.lang  isBlank 	java.lang  
isNotEmpty 	java.lang  
isNullOrEmpty 	java.lang  java 	java.lang  joinToString 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  listView 	java.lang  loadInstalledApps 	java.lang  	lowercase 	java.lang  map 	java.lang  mapOf 	java.lang  maxOf 	java.lang  
mutableListOf 	java.lang  onAppSelectionChanged 	java.lang  openAppSelection 	java.lang  progressBar 	java.lang  removePrefix 	java.lang  set 	java.lang  
setupListView 	java.lang  sortedBy 	java.lang  to 	java.lang  toList 	java.lang  toMap 	java.lang  
toMutableList 	java.lang  toSet 	java.lang  toString 	java.lang  	verifyPin 	java.lang  withContext 	java.lang  getNAME java.lang.Class  getName java.lang.Class  name java.lang.Class  setName java.lang.Class  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  sleep java.lang.Thread  start java.lang.Thread  Type java.lang.reflect  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Calendar 	java.util  Date 	java.util  Locale 	java.util  clear java.util.AbstractMap  filterValues java.util.AbstractMap  get java.util.AbstractMap  putAll java.util.AbstractMap  remove java.util.AbstractMap  set java.util.AbstractMap  toMap java.util.AbstractMap  HOUR_OF_DAY java.util.Calendar  MINUTE java.util.Calendar  SECOND java.util.Calendar  apply java.util.Calendar  get java.util.Calendar  getAPPLY java.util.Calendar  getApply java.util.Calendar  getInstance java.util.Calendar  getJAVA java.util.Calendar  getJava java.util.Calendar  getTIMEInMillis java.util.Calendar  getTimeInMillis java.util.Calendar  java java.util.Calendar  set java.util.Calendar  setTimeInMillis java.util.Calendar  timeInMillis java.util.Calendar  
getDefault java.util.Locale  ConcurrentHashMap java.util.concurrent  	Executors java.util.concurrent  ScheduledExecutorService java.util.concurrent  ScheduledFuture java.util.concurrent  TimeUnit java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  filterValues &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  getFILTERValues &java.util.concurrent.ConcurrentHashMap  getFilterValues &java.util.concurrent.ConcurrentHashMap  getSET &java.util.concurrent.ConcurrentHashMap  getSet &java.util.concurrent.ConcurrentHashMap  getTOMap &java.util.concurrent.ConcurrentHashMap  getToMap &java.util.concurrent.ConcurrentHashMap  keys &java.util.concurrent.ConcurrentHashMap  putAll &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  size &java.util.concurrent.ConcurrentHashMap  toMap &java.util.concurrent.ConcurrentHashMap   newSingleThreadScheduledExecutor java.util.concurrent.Executors  awaitTermination -java.util.concurrent.ScheduledExecutorService  scheduleAtFixedRate -java.util.concurrent.ScheduledExecutorService  shutdown -java.util.concurrent.ScheduledExecutorService  shutdownNow -java.util.concurrent.ScheduledExecutorService  SECONDS java.util.concurrent.TimeUnit  toMillis java.util.concurrent.TimeUnit  ACTION_CHECK_PERMISSIONS kotlin  AccessibilityEvent kotlin  AccessibilityServiceInfo kotlin  Activity kotlin  ActivityAppSelectionBinding kotlin  ActivityBlockOverlayBinding kotlin  ActivityMainBinding kotlin  ActivityPinAuthBinding kotlin  ActivityResultContracts kotlin  AlertDialog kotlin  Any kotlin  AppBlockAccessibilityService kotlin  AppBlockDeviceAdminReceiver kotlin  AppInfo kotlin  AppListAdapter kotlin  
AppOpsManager kotlin  AppSelectionActivity kotlin  ApplicationInfo kotlin  Array kotlin  BlockedAppsManager kotlin  Boolean kotlin  Build kotlin  Button kotlin  
CHANNEL_ID kotlin  CharSequence kotlin  
ComponentName kotlin  ConcurrentHashMap kotlin  Context kotlin  CoreFunctionalityTest kotlin  CoroutineScope kotlin  DevicePolicyManager kotlin  Dispatchers kotlin  	Exception kotlin  	Executors kotlin  Float kotlin  FrameLayout kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function4 kotlin  Gravity kotlin  Gson kotlin  Handler kotlin  	ImageView kotlin  Int kotlin  Intent kotlin  InterruptedException kotlin  ItemAppBinding kotlin  KEY_APP_BLOCKING_ENABLED kotlin  KEY_BLOCKED_APPS kotlin  KEY_BOOT_EVENTS kotlin  KEY_DEVICE_ADMIN_ENABLED kotlin  KEY_FIRST_SETUP_COMPLETE kotlin  KEY_HIDE_APP_ICON kotlin  KEY_LAST_ACTIVITY kotlin  KEY_LOCK_ON_TAMPERING kotlin  KEY_PARENT_PIN kotlin  KEY_SETTINGS_BLOCKED kotlin  KEY_TAMPERING_ATTEMPTS kotlin  KEY_TEMPORARY_ALLOWS kotlin  KEY_TIME_LIMITS kotlin  LayoutInflater kotlin  LinearLayout kotlin  LinearLayoutManager kotlin  ListView kotlin  Log kotlin  Long kotlin  Looper kotlin  MAX_ATTEMPTS kotlin  NOTIFICATION_ID kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  POLLING_INTERVAL_SECONDS kotlin  
PREFS_NAME kotlin  PackageManager kotlin  Pair kotlin  PermissionUtils kotlin  PinAuthActivity kotlin  PixelFormat kotlin  ProgressBar kotlin  R kotlin  START_STICKY kotlin  Settings kotlin  SettingsManager kotlin  String kotlin  
SupervisorJob kotlin  Suppress kotlin  System kotlin  TAG kotlin  TextView kotlin  Thread kotlin  
TimeLimitInfo kotlin  TimeUnit kotlin  Toast kotlin  Unit kotlin  UsageEvents kotlin  UsageStatsManager kotlin  UsageStatsMonitorService kotlin  View kotlin  Volatile kotlin  
WindowManager kotlin  all kotlin  allApps kotlin  android kotlin  androidx kotlin  appListAdapter kotlin  apply kotlin  arrayOf kotlin  binding kotlin  blockedPackageName kotlin  cancel kotlin  com kotlin  
component1 kotlin  
component2 kotlin  contains kotlin  count kotlin  dropLast kotlin  	emptyList kotlin  emptyMap kotlin  filter kotlin  
filterApps kotlin  filterValues kotlin  find kotlin  forEach kotlin  forEachIndexed kotlin  getComponentName kotlin  instance kotlin  isBlank kotlin  
isNotEmpty kotlin  
isNullOrEmpty kotlin  java kotlin  joinToString kotlin  launch kotlin  let kotlin  listOf kotlin  listView kotlin  loadInstalledApps kotlin  	lowercase kotlin  map kotlin  mapOf kotlin  maxOf kotlin  
mutableListOf kotlin  onAppSelectionChanged kotlin  openAppSelection kotlin  progressBar kotlin  removePrefix kotlin  set kotlin  
setupListView kotlin  sortedBy kotlin  to kotlin  toList kotlin  toMap kotlin  
toMutableList kotlin  toSet kotlin  toString kotlin  	verifyPin kotlin  withContext kotlin  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getDROPLast 
kotlin.String  getDropLast 
kotlin.String  
getISBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  getREMOVEPrefix 
kotlin.String  getRemovePrefix 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  ACTION_CHECK_PERMISSIONS kotlin.annotation  AccessibilityEvent kotlin.annotation  AccessibilityServiceInfo kotlin.annotation  Activity kotlin.annotation  ActivityAppSelectionBinding kotlin.annotation  ActivityBlockOverlayBinding kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivityPinAuthBinding kotlin.annotation  ActivityResultContracts kotlin.annotation  AlertDialog kotlin.annotation  AppBlockAccessibilityService kotlin.annotation  AppBlockDeviceAdminReceiver kotlin.annotation  AppInfo kotlin.annotation  AppListAdapter kotlin.annotation  
AppOpsManager kotlin.annotation  AppSelectionActivity kotlin.annotation  ApplicationInfo kotlin.annotation  BlockedAppsManager kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  
CHANNEL_ID kotlin.annotation  
ComponentName kotlin.annotation  ConcurrentHashMap kotlin.annotation  Context kotlin.annotation  CoreFunctionalityTest kotlin.annotation  CoroutineScope kotlin.annotation  DevicePolicyManager kotlin.annotation  Dispatchers kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  FrameLayout kotlin.annotation  Gravity kotlin.annotation  Gson kotlin.annotation  Handler kotlin.annotation  	ImageView kotlin.annotation  Intent kotlin.annotation  InterruptedException kotlin.annotation  ItemAppBinding kotlin.annotation  KEY_APP_BLOCKING_ENABLED kotlin.annotation  KEY_BLOCKED_APPS kotlin.annotation  KEY_BOOT_EVENTS kotlin.annotation  KEY_DEVICE_ADMIN_ENABLED kotlin.annotation  KEY_FIRST_SETUP_COMPLETE kotlin.annotation  KEY_HIDE_APP_ICON kotlin.annotation  KEY_LAST_ACTIVITY kotlin.annotation  KEY_LOCK_ON_TAMPERING kotlin.annotation  KEY_PARENT_PIN kotlin.annotation  KEY_SETTINGS_BLOCKED kotlin.annotation  KEY_TAMPERING_ATTEMPTS kotlin.annotation  KEY_TEMPORARY_ALLOWS kotlin.annotation  KEY_TIME_LIMITS kotlin.annotation  LayoutInflater kotlin.annotation  LinearLayout kotlin.annotation  LinearLayoutManager kotlin.annotation  ListView kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  MAX_ATTEMPTS kotlin.annotation  NOTIFICATION_ID kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  POLLING_INTERVAL_SECONDS kotlin.annotation  
PREFS_NAME kotlin.annotation  PackageManager kotlin.annotation  PermissionUtils kotlin.annotation  PinAuthActivity kotlin.annotation  PixelFormat kotlin.annotation  ProgressBar kotlin.annotation  R kotlin.annotation  START_STICKY kotlin.annotation  Settings kotlin.annotation  SettingsManager kotlin.annotation  
SupervisorJob kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  TextView kotlin.annotation  Thread kotlin.annotation  
TimeLimitInfo kotlin.annotation  TimeUnit kotlin.annotation  Toast kotlin.annotation  UsageEvents kotlin.annotation  UsageStatsManager kotlin.annotation  UsageStatsMonitorService kotlin.annotation  View kotlin.annotation  Volatile kotlin.annotation  
WindowManager kotlin.annotation  all kotlin.annotation  allApps kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  appListAdapter kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  binding kotlin.annotation  blockedPackageName kotlin.annotation  cancel kotlin.annotation  com kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  contains kotlin.annotation  count kotlin.annotation  dropLast kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  filter kotlin.annotation  
filterApps kotlin.annotation  filterValues kotlin.annotation  find kotlin.annotation  forEach kotlin.annotation  forEachIndexed kotlin.annotation  getComponentName kotlin.annotation  instance kotlin.annotation  isBlank kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrEmpty kotlin.annotation  java kotlin.annotation  joinToString kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  listView kotlin.annotation  loadInstalledApps kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  maxOf kotlin.annotation  
mutableListOf kotlin.annotation  onAppSelectionChanged kotlin.annotation  openAppSelection kotlin.annotation  progressBar kotlin.annotation  removePrefix kotlin.annotation  set kotlin.annotation  
setupListView kotlin.annotation  sortedBy kotlin.annotation  to kotlin.annotation  toList kotlin.annotation  toMap kotlin.annotation  
toMutableList kotlin.annotation  toSet kotlin.annotation  toString kotlin.annotation  	verifyPin kotlin.annotation  withContext kotlin.annotation  ACTION_CHECK_PERMISSIONS kotlin.collections  AccessibilityEvent kotlin.collections  AccessibilityServiceInfo kotlin.collections  Activity kotlin.collections  ActivityAppSelectionBinding kotlin.collections  ActivityBlockOverlayBinding kotlin.collections  ActivityMainBinding kotlin.collections  ActivityPinAuthBinding kotlin.collections  ActivityResultContracts kotlin.collections  AlertDialog kotlin.collections  AppBlockAccessibilityService kotlin.collections  AppBlockDeviceAdminReceiver kotlin.collections  AppInfo kotlin.collections  AppListAdapter kotlin.collections  
AppOpsManager kotlin.collections  AppSelectionActivity kotlin.collections  ApplicationInfo kotlin.collections  BlockedAppsManager kotlin.collections  Build kotlin.collections  Button kotlin.collections  
CHANNEL_ID kotlin.collections  
ComponentName kotlin.collections  ConcurrentHashMap kotlin.collections  Context kotlin.collections  CoreFunctionalityTest kotlin.collections  CoroutineScope kotlin.collections  DevicePolicyManager kotlin.collections  Dispatchers kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  FrameLayout kotlin.collections  Gravity kotlin.collections  Gson kotlin.collections  Handler kotlin.collections  	ImageView kotlin.collections  Intent kotlin.collections  InterruptedException kotlin.collections  ItemAppBinding kotlin.collections  KEY_APP_BLOCKING_ENABLED kotlin.collections  KEY_BLOCKED_APPS kotlin.collections  KEY_BOOT_EVENTS kotlin.collections  KEY_DEVICE_ADMIN_ENABLED kotlin.collections  KEY_FIRST_SETUP_COMPLETE kotlin.collections  KEY_HIDE_APP_ICON kotlin.collections  KEY_LAST_ACTIVITY kotlin.collections  KEY_LOCK_ON_TAMPERING kotlin.collections  KEY_PARENT_PIN kotlin.collections  KEY_SETTINGS_BLOCKED kotlin.collections  KEY_TAMPERING_ATTEMPTS kotlin.collections  KEY_TEMPORARY_ALLOWS kotlin.collections  KEY_TIME_LIMITS kotlin.collections  LayoutInflater kotlin.collections  LinearLayout kotlin.collections  LinearLayoutManager kotlin.collections  List kotlin.collections  ListView kotlin.collections  Log kotlin.collections  Looper kotlin.collections  MAX_ATTEMPTS kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  NOTIFICATION_ID kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  POLLING_INTERVAL_SECONDS kotlin.collections  
PREFS_NAME kotlin.collections  PackageManager kotlin.collections  PermissionUtils kotlin.collections  PinAuthActivity kotlin.collections  PixelFormat kotlin.collections  ProgressBar kotlin.collections  R kotlin.collections  START_STICKY kotlin.collections  Set kotlin.collections  Settings kotlin.collections  SettingsManager kotlin.collections  
SupervisorJob kotlin.collections  System kotlin.collections  TAG kotlin.collections  TextView kotlin.collections  Thread kotlin.collections  
TimeLimitInfo kotlin.collections  TimeUnit kotlin.collections  Toast kotlin.collections  UsageEvents kotlin.collections  UsageStatsManager kotlin.collections  UsageStatsMonitorService kotlin.collections  View kotlin.collections  Volatile kotlin.collections  
WindowManager kotlin.collections  all kotlin.collections  allApps kotlin.collections  android kotlin.collections  androidx kotlin.collections  appListAdapter kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  binding kotlin.collections  blockedPackageName kotlin.collections  cancel kotlin.collections  com kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  dropLast kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  
filterApps kotlin.collections  filterValues kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  getComponentName kotlin.collections  instance kotlin.collections  isBlank kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  java kotlin.collections  joinToString kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  listView kotlin.collections  loadInstalledApps kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  
mutableListOf kotlin.collections  onAppSelectionChanged kotlin.collections  openAppSelection kotlin.collections  progressBar kotlin.collections  removePrefix kotlin.collections  set kotlin.collections  
setupListView kotlin.collections  sortedBy kotlin.collections  to kotlin.collections  toList kotlin.collections  toMap kotlin.collections  
toMutableList kotlin.collections  toSet kotlin.collections  toString kotlin.collections  	verifyPin kotlin.collections  withContext kotlin.collections  getALL kotlin.collections.Collection  getAll kotlin.collections.Collection  getCOUNT kotlin.collections.Collection  getCount kotlin.collections.Collection  getCOUNT kotlin.collections.List  getCount kotlin.collections.List  	getFILTER kotlin.collections.List  getFOREachIndexed kotlin.collections.List  	getFilter kotlin.collections.List  getForEachIndexed kotlin.collections.List  getJOINToString kotlin.collections.List  getJoinToString kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getSORTEDBy kotlin.collections.List  getSortedBy kotlin.collections.List  getTOMutableList kotlin.collections.List  getTOSet kotlin.collections.List  getToMutableList kotlin.collections.List  getToSet kotlin.collections.List  Entry kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  	getFILTER kotlin.collections.MutableList  getFIND kotlin.collections.MutableList  	getFilter kotlin.collections.MutableList  getFind kotlin.collections.MutableList  	getTOList kotlin.collections.MutableSet  	getToList kotlin.collections.MutableSet  ACTION_CHECK_PERMISSIONS kotlin.comparisons  AccessibilityEvent kotlin.comparisons  AccessibilityServiceInfo kotlin.comparisons  Activity kotlin.comparisons  ActivityAppSelectionBinding kotlin.comparisons  ActivityBlockOverlayBinding kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivityPinAuthBinding kotlin.comparisons  ActivityResultContracts kotlin.comparisons  AlertDialog kotlin.comparisons  AppBlockAccessibilityService kotlin.comparisons  AppBlockDeviceAdminReceiver kotlin.comparisons  AppInfo kotlin.comparisons  AppListAdapter kotlin.comparisons  
AppOpsManager kotlin.comparisons  AppSelectionActivity kotlin.comparisons  ApplicationInfo kotlin.comparisons  BlockedAppsManager kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  
ComponentName kotlin.comparisons  ConcurrentHashMap kotlin.comparisons  Context kotlin.comparisons  CoreFunctionalityTest kotlin.comparisons  CoroutineScope kotlin.comparisons  DevicePolicyManager kotlin.comparisons  Dispatchers kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  FrameLayout kotlin.comparisons  Gravity kotlin.comparisons  Gson kotlin.comparisons  Handler kotlin.comparisons  	ImageView kotlin.comparisons  Intent kotlin.comparisons  InterruptedException kotlin.comparisons  ItemAppBinding kotlin.comparisons  KEY_APP_BLOCKING_ENABLED kotlin.comparisons  KEY_BLOCKED_APPS kotlin.comparisons  KEY_BOOT_EVENTS kotlin.comparisons  KEY_DEVICE_ADMIN_ENABLED kotlin.comparisons  KEY_FIRST_SETUP_COMPLETE kotlin.comparisons  KEY_HIDE_APP_ICON kotlin.comparisons  KEY_LAST_ACTIVITY kotlin.comparisons  KEY_LOCK_ON_TAMPERING kotlin.comparisons  KEY_PARENT_PIN kotlin.comparisons  KEY_SETTINGS_BLOCKED kotlin.comparisons  KEY_TAMPERING_ATTEMPTS kotlin.comparisons  KEY_TEMPORARY_ALLOWS kotlin.comparisons  KEY_TIME_LIMITS kotlin.comparisons  LayoutInflater kotlin.comparisons  LinearLayout kotlin.comparisons  LinearLayoutManager kotlin.comparisons  ListView kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  MAX_ATTEMPTS kotlin.comparisons  NOTIFICATION_ID kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  POLLING_INTERVAL_SECONDS kotlin.comparisons  
PREFS_NAME kotlin.comparisons  PackageManager kotlin.comparisons  PermissionUtils kotlin.comparisons  PinAuthActivity kotlin.comparisons  PixelFormat kotlin.comparisons  ProgressBar kotlin.comparisons  R kotlin.comparisons  START_STICKY kotlin.comparisons  Settings kotlin.comparisons  SettingsManager kotlin.comparisons  
SupervisorJob kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  TextView kotlin.comparisons  Thread kotlin.comparisons  
TimeLimitInfo kotlin.comparisons  TimeUnit kotlin.comparisons  Toast kotlin.comparisons  UsageEvents kotlin.comparisons  UsageStatsManager kotlin.comparisons  UsageStatsMonitorService kotlin.comparisons  View kotlin.comparisons  Volatile kotlin.comparisons  
WindowManager kotlin.comparisons  all kotlin.comparisons  allApps kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  appListAdapter kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  binding kotlin.comparisons  blockedPackageName kotlin.comparisons  cancel kotlin.comparisons  com kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  contains kotlin.comparisons  count kotlin.comparisons  dropLast kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  filter kotlin.comparisons  
filterApps kotlin.comparisons  filterValues kotlin.comparisons  find kotlin.comparisons  forEach kotlin.comparisons  forEachIndexed kotlin.comparisons  getComponentName kotlin.comparisons  instance kotlin.comparisons  isBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  java kotlin.comparisons  joinToString kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  listView kotlin.comparisons  loadInstalledApps kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  maxOf kotlin.comparisons  
mutableListOf kotlin.comparisons  onAppSelectionChanged kotlin.comparisons  openAppSelection kotlin.comparisons  progressBar kotlin.comparisons  removePrefix kotlin.comparisons  set kotlin.comparisons  
setupListView kotlin.comparisons  sortedBy kotlin.comparisons  to kotlin.comparisons  toList kotlin.comparisons  toMap kotlin.comparisons  
toMutableList kotlin.comparisons  toSet kotlin.comparisons  toString kotlin.comparisons  	verifyPin kotlin.comparisons  withContext kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  ACTION_CHECK_PERMISSIONS 	kotlin.io  AccessibilityEvent 	kotlin.io  AccessibilityServiceInfo 	kotlin.io  Activity 	kotlin.io  ActivityAppSelectionBinding 	kotlin.io  ActivityBlockOverlayBinding 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivityPinAuthBinding 	kotlin.io  ActivityResultContracts 	kotlin.io  AlertDialog 	kotlin.io  AppBlockAccessibilityService 	kotlin.io  AppBlockDeviceAdminReceiver 	kotlin.io  AppInfo 	kotlin.io  AppListAdapter 	kotlin.io  
AppOpsManager 	kotlin.io  AppSelectionActivity 	kotlin.io  ApplicationInfo 	kotlin.io  BlockedAppsManager 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  
CHANNEL_ID 	kotlin.io  
ComponentName 	kotlin.io  ConcurrentHashMap 	kotlin.io  Context 	kotlin.io  CoreFunctionalityTest 	kotlin.io  CoroutineScope 	kotlin.io  DevicePolicyManager 	kotlin.io  Dispatchers 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  FrameLayout 	kotlin.io  Gravity 	kotlin.io  Gson 	kotlin.io  Handler 	kotlin.io  	ImageView 	kotlin.io  Intent 	kotlin.io  InterruptedException 	kotlin.io  ItemAppBinding 	kotlin.io  KEY_APP_BLOCKING_ENABLED 	kotlin.io  KEY_BLOCKED_APPS 	kotlin.io  KEY_BOOT_EVENTS 	kotlin.io  KEY_DEVICE_ADMIN_ENABLED 	kotlin.io  KEY_FIRST_SETUP_COMPLETE 	kotlin.io  KEY_HIDE_APP_ICON 	kotlin.io  KEY_LAST_ACTIVITY 	kotlin.io  KEY_LOCK_ON_TAMPERING 	kotlin.io  KEY_PARENT_PIN 	kotlin.io  KEY_SETTINGS_BLOCKED 	kotlin.io  KEY_TAMPERING_ATTEMPTS 	kotlin.io  KEY_TEMPORARY_ALLOWS 	kotlin.io  KEY_TIME_LIMITS 	kotlin.io  LayoutInflater 	kotlin.io  LinearLayout 	kotlin.io  LinearLayoutManager 	kotlin.io  ListView 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  MAX_ATTEMPTS 	kotlin.io  NOTIFICATION_ID 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  POLLING_INTERVAL_SECONDS 	kotlin.io  
PREFS_NAME 	kotlin.io  PackageManager 	kotlin.io  PermissionUtils 	kotlin.io  PinAuthActivity 	kotlin.io  PixelFormat 	kotlin.io  ProgressBar 	kotlin.io  R 	kotlin.io  START_STICKY 	kotlin.io  Settings 	kotlin.io  SettingsManager 	kotlin.io  
SupervisorJob 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  TextView 	kotlin.io  Thread 	kotlin.io  
TimeLimitInfo 	kotlin.io  TimeUnit 	kotlin.io  Toast 	kotlin.io  UsageEvents 	kotlin.io  UsageStatsManager 	kotlin.io  UsageStatsMonitorService 	kotlin.io  View 	kotlin.io  Volatile 	kotlin.io  
WindowManager 	kotlin.io  all 	kotlin.io  allApps 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  appListAdapter 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  binding 	kotlin.io  blockedPackageName 	kotlin.io  cancel 	kotlin.io  com 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  contains 	kotlin.io  count 	kotlin.io  dropLast 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  filter 	kotlin.io  
filterApps 	kotlin.io  filterValues 	kotlin.io  find 	kotlin.io  forEach 	kotlin.io  forEachIndexed 	kotlin.io  getComponentName 	kotlin.io  instance 	kotlin.io  isBlank 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrEmpty 	kotlin.io  java 	kotlin.io  joinToString 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  listView 	kotlin.io  loadInstalledApps 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  maxOf 	kotlin.io  
mutableListOf 	kotlin.io  onAppSelectionChanged 	kotlin.io  openAppSelection 	kotlin.io  progressBar 	kotlin.io  removePrefix 	kotlin.io  set 	kotlin.io  
setupListView 	kotlin.io  sortedBy 	kotlin.io  to 	kotlin.io  toList 	kotlin.io  toMap 	kotlin.io  
toMutableList 	kotlin.io  toSet 	kotlin.io  toString 	kotlin.io  	verifyPin 	kotlin.io  withContext 	kotlin.io  ACTION_CHECK_PERMISSIONS 
kotlin.jvm  AccessibilityEvent 
kotlin.jvm  AccessibilityServiceInfo 
kotlin.jvm  Activity 
kotlin.jvm  ActivityAppSelectionBinding 
kotlin.jvm  ActivityBlockOverlayBinding 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivityPinAuthBinding 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  AlertDialog 
kotlin.jvm  AppBlockAccessibilityService 
kotlin.jvm  AppBlockDeviceAdminReceiver 
kotlin.jvm  AppInfo 
kotlin.jvm  AppListAdapter 
kotlin.jvm  
AppOpsManager 
kotlin.jvm  AppSelectionActivity 
kotlin.jvm  ApplicationInfo 
kotlin.jvm  BlockedAppsManager 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  
ComponentName 
kotlin.jvm  ConcurrentHashMap 
kotlin.jvm  Context 
kotlin.jvm  CoreFunctionalityTest 
kotlin.jvm  CoroutineScope 
kotlin.jvm  DevicePolicyManager 
kotlin.jvm  Dispatchers 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  FrameLayout 
kotlin.jvm  Gravity 
kotlin.jvm  Gson 
kotlin.jvm  Handler 
kotlin.jvm  	ImageView 
kotlin.jvm  Intent 
kotlin.jvm  InterruptedException 
kotlin.jvm  ItemAppBinding 
kotlin.jvm  KEY_APP_BLOCKING_ENABLED 
kotlin.jvm  KEY_BLOCKED_APPS 
kotlin.jvm  KEY_BOOT_EVENTS 
kotlin.jvm  KEY_DEVICE_ADMIN_ENABLED 
kotlin.jvm  KEY_FIRST_SETUP_COMPLETE 
kotlin.jvm  KEY_HIDE_APP_ICON 
kotlin.jvm  KEY_LAST_ACTIVITY 
kotlin.jvm  KEY_LOCK_ON_TAMPERING 
kotlin.jvm  KEY_PARENT_PIN 
kotlin.jvm  KEY_SETTINGS_BLOCKED 
kotlin.jvm  KEY_TAMPERING_ATTEMPTS 
kotlin.jvm  KEY_TEMPORARY_ALLOWS 
kotlin.jvm  KEY_TIME_LIMITS 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LinearLayout 
kotlin.jvm  LinearLayoutManager 
kotlin.jvm  ListView 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  MAX_ATTEMPTS 
kotlin.jvm  NOTIFICATION_ID 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  POLLING_INTERVAL_SECONDS 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  PackageManager 
kotlin.jvm  PermissionUtils 
kotlin.jvm  PinAuthActivity 
kotlin.jvm  PixelFormat 
kotlin.jvm  ProgressBar 
kotlin.jvm  R 
kotlin.jvm  START_STICKY 
kotlin.jvm  Settings 
kotlin.jvm  SettingsManager 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  TextView 
kotlin.jvm  Thread 
kotlin.jvm  
TimeLimitInfo 
kotlin.jvm  TimeUnit 
kotlin.jvm  Toast 
kotlin.jvm  UsageEvents 
kotlin.jvm  UsageStatsManager 
kotlin.jvm  UsageStatsMonitorService 
kotlin.jvm  View 
kotlin.jvm  Volatile 
kotlin.jvm  
WindowManager 
kotlin.jvm  all 
kotlin.jvm  allApps 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  appListAdapter 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  binding 
kotlin.jvm  blockedPackageName 
kotlin.jvm  cancel 
kotlin.jvm  com 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  contains 
kotlin.jvm  count 
kotlin.jvm  dropLast 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  filter 
kotlin.jvm  
filterApps 
kotlin.jvm  filterValues 
kotlin.jvm  find 
kotlin.jvm  forEach 
kotlin.jvm  forEachIndexed 
kotlin.jvm  getComponentName 
kotlin.jvm  instance 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  java 
kotlin.jvm  joinToString 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  listView 
kotlin.jvm  loadInstalledApps 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  maxOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  onAppSelectionChanged 
kotlin.jvm  openAppSelection 
kotlin.jvm  progressBar 
kotlin.jvm  removePrefix 
kotlin.jvm  set 
kotlin.jvm  
setupListView 
kotlin.jvm  sortedBy 
kotlin.jvm  to 
kotlin.jvm  toList 
kotlin.jvm  toMap 
kotlin.jvm  
toMutableList 
kotlin.jvm  toSet 
kotlin.jvm  toString 
kotlin.jvm  	verifyPin 
kotlin.jvm  withContext 
kotlin.jvm  ACTION_CHECK_PERMISSIONS 
kotlin.ranges  AccessibilityEvent 
kotlin.ranges  AccessibilityServiceInfo 
kotlin.ranges  Activity 
kotlin.ranges  ActivityAppSelectionBinding 
kotlin.ranges  ActivityBlockOverlayBinding 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivityPinAuthBinding 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  AlertDialog 
kotlin.ranges  AppBlockAccessibilityService 
kotlin.ranges  AppBlockDeviceAdminReceiver 
kotlin.ranges  AppInfo 
kotlin.ranges  AppListAdapter 
kotlin.ranges  
AppOpsManager 
kotlin.ranges  AppSelectionActivity 
kotlin.ranges  ApplicationInfo 
kotlin.ranges  BlockedAppsManager 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  
ComponentName 
kotlin.ranges  ConcurrentHashMap 
kotlin.ranges  Context 
kotlin.ranges  CoreFunctionalityTest 
kotlin.ranges  CoroutineScope 
kotlin.ranges  DevicePolicyManager 
kotlin.ranges  Dispatchers 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  FrameLayout 
kotlin.ranges  Gravity 
kotlin.ranges  Gson 
kotlin.ranges  Handler 
kotlin.ranges  	ImageView 
kotlin.ranges  Intent 
kotlin.ranges  InterruptedException 
kotlin.ranges  ItemAppBinding 
kotlin.ranges  KEY_APP_BLOCKING_ENABLED 
kotlin.ranges  KEY_BLOCKED_APPS 
kotlin.ranges  KEY_BOOT_EVENTS 
kotlin.ranges  KEY_DEVICE_ADMIN_ENABLED 
kotlin.ranges  KEY_FIRST_SETUP_COMPLETE 
kotlin.ranges  KEY_HIDE_APP_ICON 
kotlin.ranges  KEY_LAST_ACTIVITY 
kotlin.ranges  KEY_LOCK_ON_TAMPERING 
kotlin.ranges  KEY_PARENT_PIN 
kotlin.ranges  KEY_SETTINGS_BLOCKED 
kotlin.ranges  KEY_TAMPERING_ATTEMPTS 
kotlin.ranges  KEY_TEMPORARY_ALLOWS 
kotlin.ranges  KEY_TIME_LIMITS 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LinearLayout 
kotlin.ranges  LinearLayoutManager 
kotlin.ranges  ListView 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  MAX_ATTEMPTS 
kotlin.ranges  NOTIFICATION_ID 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  POLLING_INTERVAL_SECONDS 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  PackageManager 
kotlin.ranges  PermissionUtils 
kotlin.ranges  PinAuthActivity 
kotlin.ranges  PixelFormat 
kotlin.ranges  ProgressBar 
kotlin.ranges  R 
kotlin.ranges  START_STICKY 
kotlin.ranges  Settings 
kotlin.ranges  SettingsManager 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  TextView 
kotlin.ranges  Thread 
kotlin.ranges  
TimeLimitInfo 
kotlin.ranges  TimeUnit 
kotlin.ranges  Toast 
kotlin.ranges  UsageEvents 
kotlin.ranges  UsageStatsManager 
kotlin.ranges  UsageStatsMonitorService 
kotlin.ranges  View 
kotlin.ranges  Volatile 
kotlin.ranges  
WindowManager 
kotlin.ranges  all 
kotlin.ranges  allApps 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  appListAdapter 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  binding 
kotlin.ranges  blockedPackageName 
kotlin.ranges  cancel 
kotlin.ranges  com 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  contains 
kotlin.ranges  count 
kotlin.ranges  dropLast 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  filter 
kotlin.ranges  
filterApps 
kotlin.ranges  filterValues 
kotlin.ranges  find 
kotlin.ranges  forEach 
kotlin.ranges  forEachIndexed 
kotlin.ranges  getComponentName 
kotlin.ranges  instance 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  java 
kotlin.ranges  joinToString 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  listView 
kotlin.ranges  loadInstalledApps 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  maxOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  onAppSelectionChanged 
kotlin.ranges  openAppSelection 
kotlin.ranges  progressBar 
kotlin.ranges  removePrefix 
kotlin.ranges  set 
kotlin.ranges  
setupListView 
kotlin.ranges  sortedBy 
kotlin.ranges  to 
kotlin.ranges  toList 
kotlin.ranges  toMap 
kotlin.ranges  
toMutableList 
kotlin.ranges  toSet 
kotlin.ranges  toString 
kotlin.ranges  	verifyPin 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ACTION_CHECK_PERMISSIONS kotlin.sequences  AccessibilityEvent kotlin.sequences  AccessibilityServiceInfo kotlin.sequences  Activity kotlin.sequences  ActivityAppSelectionBinding kotlin.sequences  ActivityBlockOverlayBinding kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivityPinAuthBinding kotlin.sequences  ActivityResultContracts kotlin.sequences  AlertDialog kotlin.sequences  AppBlockAccessibilityService kotlin.sequences  AppBlockDeviceAdminReceiver kotlin.sequences  AppInfo kotlin.sequences  AppListAdapter kotlin.sequences  
AppOpsManager kotlin.sequences  AppSelectionActivity kotlin.sequences  ApplicationInfo kotlin.sequences  BlockedAppsManager kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  
CHANNEL_ID kotlin.sequences  
ComponentName kotlin.sequences  ConcurrentHashMap kotlin.sequences  Context kotlin.sequences  CoreFunctionalityTest kotlin.sequences  CoroutineScope kotlin.sequences  DevicePolicyManager kotlin.sequences  Dispatchers kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  FrameLayout kotlin.sequences  Gravity kotlin.sequences  Gson kotlin.sequences  Handler kotlin.sequences  	ImageView kotlin.sequences  Intent kotlin.sequences  InterruptedException kotlin.sequences  ItemAppBinding kotlin.sequences  KEY_APP_BLOCKING_ENABLED kotlin.sequences  KEY_BLOCKED_APPS kotlin.sequences  KEY_BOOT_EVENTS kotlin.sequences  KEY_DEVICE_ADMIN_ENABLED kotlin.sequences  KEY_FIRST_SETUP_COMPLETE kotlin.sequences  KEY_HIDE_APP_ICON kotlin.sequences  KEY_LAST_ACTIVITY kotlin.sequences  KEY_LOCK_ON_TAMPERING kotlin.sequences  KEY_PARENT_PIN kotlin.sequences  KEY_SETTINGS_BLOCKED kotlin.sequences  KEY_TAMPERING_ATTEMPTS kotlin.sequences  KEY_TEMPORARY_ALLOWS kotlin.sequences  KEY_TIME_LIMITS kotlin.sequences  LayoutInflater kotlin.sequences  LinearLayout kotlin.sequences  LinearLayoutManager kotlin.sequences  ListView kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  MAX_ATTEMPTS kotlin.sequences  NOTIFICATION_ID kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  POLLING_INTERVAL_SECONDS kotlin.sequences  
PREFS_NAME kotlin.sequences  PackageManager kotlin.sequences  PermissionUtils kotlin.sequences  PinAuthActivity kotlin.sequences  PixelFormat kotlin.sequences  ProgressBar kotlin.sequences  R kotlin.sequences  START_STICKY kotlin.sequences  Settings kotlin.sequences  SettingsManager kotlin.sequences  
SupervisorJob kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  TextView kotlin.sequences  Thread kotlin.sequences  
TimeLimitInfo kotlin.sequences  TimeUnit kotlin.sequences  Toast kotlin.sequences  UsageEvents kotlin.sequences  UsageStatsManager kotlin.sequences  UsageStatsMonitorService kotlin.sequences  View kotlin.sequences  Volatile kotlin.sequences  
WindowManager kotlin.sequences  all kotlin.sequences  allApps kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  appListAdapter kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  binding kotlin.sequences  blockedPackageName kotlin.sequences  cancel kotlin.sequences  com kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  dropLast kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  filter kotlin.sequences  
filterApps kotlin.sequences  filterValues kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  getComponentName kotlin.sequences  instance kotlin.sequences  isBlank kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrEmpty kotlin.sequences  java kotlin.sequences  joinToString kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  listView kotlin.sequences  loadInstalledApps kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  maxOf kotlin.sequences  
mutableListOf kotlin.sequences  onAppSelectionChanged kotlin.sequences  openAppSelection kotlin.sequences  progressBar kotlin.sequences  removePrefix kotlin.sequences  set kotlin.sequences  
setupListView kotlin.sequences  sortedBy kotlin.sequences  to kotlin.sequences  toList kotlin.sequences  toMap kotlin.sequences  
toMutableList kotlin.sequences  toSet kotlin.sequences  toString kotlin.sequences  	verifyPin kotlin.sequences  withContext kotlin.sequences  ACTION_CHECK_PERMISSIONS kotlin.text  AccessibilityEvent kotlin.text  AccessibilityServiceInfo kotlin.text  Activity kotlin.text  ActivityAppSelectionBinding kotlin.text  ActivityBlockOverlayBinding kotlin.text  ActivityMainBinding kotlin.text  ActivityPinAuthBinding kotlin.text  ActivityResultContracts kotlin.text  AlertDialog kotlin.text  AppBlockAccessibilityService kotlin.text  AppBlockDeviceAdminReceiver kotlin.text  AppInfo kotlin.text  AppListAdapter kotlin.text  
AppOpsManager kotlin.text  AppSelectionActivity kotlin.text  ApplicationInfo kotlin.text  BlockedAppsManager kotlin.text  Build kotlin.text  Button kotlin.text  
CHANNEL_ID kotlin.text  
ComponentName kotlin.text  ConcurrentHashMap kotlin.text  Context kotlin.text  CoreFunctionalityTest kotlin.text  CoroutineScope kotlin.text  DevicePolicyManager kotlin.text  Dispatchers kotlin.text  	Exception kotlin.text  	Executors kotlin.text  FrameLayout kotlin.text  Gravity kotlin.text  Gson kotlin.text  Handler kotlin.text  	ImageView kotlin.text  Intent kotlin.text  InterruptedException kotlin.text  ItemAppBinding kotlin.text  KEY_APP_BLOCKING_ENABLED kotlin.text  KEY_BLOCKED_APPS kotlin.text  KEY_BOOT_EVENTS kotlin.text  KEY_DEVICE_ADMIN_ENABLED kotlin.text  KEY_FIRST_SETUP_COMPLETE kotlin.text  KEY_HIDE_APP_ICON kotlin.text  KEY_LAST_ACTIVITY kotlin.text  KEY_LOCK_ON_TAMPERING kotlin.text  KEY_PARENT_PIN kotlin.text  KEY_SETTINGS_BLOCKED kotlin.text  KEY_TAMPERING_ATTEMPTS kotlin.text  KEY_TEMPORARY_ALLOWS kotlin.text  KEY_TIME_LIMITS kotlin.text  LayoutInflater kotlin.text  LinearLayout kotlin.text  LinearLayoutManager kotlin.text  ListView kotlin.text  Log kotlin.text  Looper kotlin.text  MAX_ATTEMPTS kotlin.text  NOTIFICATION_ID kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  POLLING_INTERVAL_SECONDS kotlin.text  
PREFS_NAME kotlin.text  PackageManager kotlin.text  PermissionUtils kotlin.text  PinAuthActivity kotlin.text  PixelFormat kotlin.text  ProgressBar kotlin.text  R kotlin.text  START_STICKY kotlin.text  Settings kotlin.text  SettingsManager kotlin.text  
SupervisorJob kotlin.text  System kotlin.text  TAG kotlin.text  TextView kotlin.text  Thread kotlin.text  
TimeLimitInfo kotlin.text  TimeUnit kotlin.text  Toast kotlin.text  UsageEvents kotlin.text  UsageStatsManager kotlin.text  UsageStatsMonitorService kotlin.text  View kotlin.text  Volatile kotlin.text  
WindowManager kotlin.text  all kotlin.text  allApps kotlin.text  android kotlin.text  androidx kotlin.text  appListAdapter kotlin.text  apply kotlin.text  arrayOf kotlin.text  binding kotlin.text  blockedPackageName kotlin.text  cancel kotlin.text  com kotlin.text  
component1 kotlin.text  
component2 kotlin.text  contains kotlin.text  count kotlin.text  dropLast kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  filter kotlin.text  
filterApps kotlin.text  filterValues kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  getComponentName kotlin.text  instance kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  java kotlin.text  joinToString kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  listView kotlin.text  loadInstalledApps kotlin.text  	lowercase kotlin.text  map kotlin.text  mapOf kotlin.text  maxOf kotlin.text  
mutableListOf kotlin.text  onAppSelectionChanged kotlin.text  openAppSelection kotlin.text  progressBar kotlin.text  removePrefix kotlin.text  set kotlin.text  
setupListView kotlin.text  sortedBy kotlin.text  to kotlin.text  toList kotlin.text  toMap kotlin.text  
toMutableList kotlin.text  toSet kotlin.text  toString kotlin.text  	verifyPin kotlin.text  withContext kotlin.text  ActivityAppSelectionBinding kotlinx.coroutines  AlertDialog kotlinx.coroutines  AppInfo kotlinx.coroutines  AppListAdapter kotlinx.coroutines  ApplicationInfo kotlinx.coroutines  BaseAdapter kotlinx.coroutines  BlockedAppsManager kotlinx.coroutines  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  	Exception kotlinx.coroutines  	ImageView kotlinx.coroutines  Job kotlinx.coroutines  LinearLayout kotlinx.coroutines  LinearLayoutManager kotlinx.coroutines  ListView kotlinx.coroutines  Log kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  PackageManager kotlinx.coroutines  ProgressBar kotlinx.coroutines  R kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  TAG kotlinx.coroutines  TextView kotlinx.coroutines  Toast kotlinx.coroutines  View kotlinx.coroutines  allApps kotlinx.coroutines  android kotlinx.coroutines  appListAdapter kotlinx.coroutines  apply kotlinx.coroutines  binding kotlinx.coroutines  cancel kotlinx.coroutines  contains kotlinx.coroutines  count kotlinx.coroutines  filter kotlinx.coroutines  
filterApps kotlinx.coroutines  forEach kotlinx.coroutines  isBlank kotlinx.coroutines  launch kotlinx.coroutines  listOf kotlinx.coroutines  listView kotlinx.coroutines  loadInstalledApps kotlinx.coroutines  	lowercase kotlinx.coroutines  map kotlinx.coroutines  
mutableListOf kotlinx.coroutines  progressBar kotlinx.coroutines  
setupListView kotlinx.coroutines  sortedBy kotlinx.coroutines  toSet kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  Dispatchers !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  View !kotlinx.coroutines.CoroutineScope  allApps !kotlinx.coroutines.CoroutineScope  binding !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  
filterApps !kotlinx.coroutines.CoroutineScope  
getALLApps !kotlinx.coroutines.CoroutineScope  
getAllApps !kotlinx.coroutines.CoroutineScope  
getBINDING !kotlinx.coroutines.CoroutineScope  
getBinding !kotlinx.coroutines.CoroutineScope  	getCANCEL !kotlinx.coroutines.CoroutineScope  	getCancel !kotlinx.coroutines.CoroutineScope  
getFILTERApps !kotlinx.coroutines.CoroutineScope  
getFilterApps !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLISTView !kotlinx.coroutines.CoroutineScope  getLOADInstalledApps !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getListView !kotlinx.coroutines.CoroutineScope  getLoadInstalledApps !kotlinx.coroutines.CoroutineScope  getPROGRESSBar !kotlinx.coroutines.CoroutineScope  getProgressBar !kotlinx.coroutines.CoroutineScope  getSETUPListView !kotlinx.coroutines.CoroutineScope  getSetupListView !kotlinx.coroutines.CoroutineScope  getWITHContext !kotlinx.coroutines.CoroutineScope  getWithContext !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  listView !kotlinx.coroutines.CoroutineScope  loadInstalledApps !kotlinx.coroutines.CoroutineScope  progressBar !kotlinx.coroutines.CoroutineScope  
setupListView !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  blockAppImmediately 1android.accessibilityservice.AccessibilityService  blockAppImmediately android.app.Service  blockAppViaFallback android.app.Service  blockAppImmediately android.content.Context  blockAppViaFallback android.content.Context  blockAppImmediately android.content.ContextWrapper  blockAppViaFallback android.content.ContextWrapper  getISFocusable android.view.View  getISFocusableInTouchMode android.view.View  getIsFocusable android.view.View  getIsFocusableInTouchMode android.view.View  isFocusable android.view.View  isFocusableInTouchMode android.view.View  setFocusable android.view.View  setFocusableInTouchMode android.view.View  FLAG_NOT_TOUCH_MODAL 'android.view.WindowManager.LayoutParams  addView android.widget.FrameLayout  android android.widget.FrameLayout  apply android.widget.FrameLayout  
getANDROID android.widget.FrameLayout  getAPPLY android.widget.FrameLayout  
getAndroid android.widget.FrameLayout  getApply android.widget.FrameLayout  setBackgroundColor android.widget.FrameLayout  blockedCountText com.appblock.MainActivity  
statusText com.appblock.MainActivity  blockAppImmediately 1com.appblock.service.AppBlockAccessibilityService  blockAppViaFallback -com.appblock.service.UsageStatsMonitorService  isServiceRunning -com.appblock.service.UsageStatsMonitorService  isServiceRunning 7com.appblock.service.UsageStatsMonitorService.Companion  PermissionSetupActivity com.appblock.ui  	Companion 'com.appblock.ui.PermissionSetupActivity  black android.R.color  holo_green_light android.R.color  holo_orange_light android.R.color  Array android.app.Activity  
ContextCompat android.app.Activity  IntArray android.app.Activity  REQUEST_ACCESSIBILITY android.app.Activity  REQUEST_DEVICE_ADMIN android.app.Activity  REQUEST_NOTIFICATIONS android.app.Activity  REQUEST_OVERLAY android.app.Activity  REQUEST_USAGE_STATS android.app.Activity  
ScrollView android.app.Activity  createPermissionCard android.app.Activity  finishSetup android.app.Activity  onActivityResult android.app.Activity  onRequestPermissionsResult android.app.Activity  requestPermission android.app.Activity  requestPermissions android.app.Activity  shouldShowPermissionSetup android.app.Activity  showPermissionSetup android.app.Activity  startActivityForResult android.app.Activity  updateOverallStatus android.app.Activity  updatePermissionStatus android.app.Activity  Array android.content.Context  
ContextCompat android.content.Context  IntArray android.content.Context  REQUEST_ACCESSIBILITY android.content.Context  REQUEST_DEVICE_ADMIN android.content.Context  REQUEST_NOTIFICATIONS android.content.Context  REQUEST_OVERLAY android.content.Context  REQUEST_USAGE_STATS android.content.Context  
ScrollView android.content.Context  createPermissionCard android.content.Context  finishSetup android.content.Context  onActivityResult android.content.Context  onRequestPermissionsResult android.content.Context  requestPermission android.content.Context  requestPermissions android.content.Context  shouldShowPermissionSetup android.content.Context  showPermissionSetup android.content.Context  startActivityForResult android.content.Context  updateOverallStatus android.content.Context  updatePermissionStatus android.content.Context  Array android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  IntArray android.content.ContextWrapper  REQUEST_ACCESSIBILITY android.content.ContextWrapper  REQUEST_DEVICE_ADMIN android.content.ContextWrapper  REQUEST_NOTIFICATIONS android.content.ContextWrapper  REQUEST_OVERLAY android.content.ContextWrapper  REQUEST_USAGE_STATS android.content.ContextWrapper  
ScrollView android.content.ContextWrapper  createPermissionCard android.content.ContextWrapper  finishSetup android.content.ContextWrapper  onActivityResult android.content.ContextWrapper  onRequestPermissionsResult android.content.ContextWrapper  requestPermission android.content.ContextWrapper  requestPermissions android.content.ContextWrapper  shouldShowPermissionSetup android.content.ContextWrapper  showPermissionSetup android.content.ContextWrapper  startActivityForResult android.content.ContextWrapper  updateOverallStatus android.content.ContextWrapper  updatePermissionStatus android.content.ContextWrapper  Array  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  REQUEST_ACCESSIBILITY  android.view.ContextThemeWrapper  REQUEST_DEVICE_ADMIN  android.view.ContextThemeWrapper  REQUEST_NOTIFICATIONS  android.view.ContextThemeWrapper  REQUEST_OVERLAY  android.view.ContextThemeWrapper  REQUEST_USAGE_STATS  android.view.ContextThemeWrapper  
ScrollView  android.view.ContextThemeWrapper  createPermissionCard  android.view.ContextThemeWrapper  finishSetup  android.view.ContextThemeWrapper  onActivityResult  android.view.ContextThemeWrapper  onRequestPermissionsResult  android.view.ContextThemeWrapper  requestPermission  android.view.ContextThemeWrapper  requestPermissions  android.view.ContextThemeWrapper  shouldShowPermissionSetup  android.view.ContextThemeWrapper  showPermissionSetup  android.view.ContextThemeWrapper  startActivityForResult  android.view.ContextThemeWrapper  updateOverallStatus  android.view.ContextThemeWrapper  updatePermissionStatus  android.view.ContextThemeWrapper  removeAllViews android.view.View  removeAllViews android.view.ViewGroup  
ContextCompat android.widget  REQUEST_ACCESSIBILITY android.widget  REQUEST_DEVICE_ADMIN android.widget  REQUEST_NOTIFICATIONS android.widget  REQUEST_OVERLAY android.widget  REQUEST_USAGE_STATS android.widget  
ScrollView android.widget  all android.widget  arrayOf android.widget  count android.widget  finishSetup android.widget  listOf android.widget  requestPermission android.widget  finishSetup android.widget.Button  getFINISHSetup android.widget.Button  getFinishSetup android.widget.Button  getISEnabled android.widget.Button  getIsEnabled android.widget.Button  getREQUESTPermission android.widget.Button  getRequestPermission android.widget.Button  	isEnabled android.widget.Button  requestPermission android.widget.Button  
setEnabled android.widget.Button  
ContextCompat android.widget.LinearLayout  android android.widget.LinearLayout  
getANDROID android.widget.LinearLayout  
getAndroid android.widget.LinearLayout  removeAllViews android.widget.LinearLayout  MATCH_PARENT (android.widget.LinearLayout.LayoutParams  addView android.widget.ScrollView  
ContextCompat android.widget.TextView  Boolean com.appblock  Boolean com.appblock.MainActivity  shouldShowPermissionSetup com.appblock.MainActivity  showPermissionSetup com.appblock.MainActivity  Boolean #com.appblock.MainActivity.Companion  Array com.appblock.ui  Button com.appblock.ui  
ContextCompat com.appblock.ui  IntArray com.appblock.ui  REQUEST_ACCESSIBILITY com.appblock.ui  REQUEST_DEVICE_ADMIN com.appblock.ui  REQUEST_NOTIFICATIONS com.appblock.ui  REQUEST_OVERLAY com.appblock.ui  REQUEST_USAGE_STATS com.appblock.ui  
ScrollView com.appblock.ui  finishSetup com.appblock.ui  requestPermission com.appblock.ui  Array 'com.appblock.ui.PermissionSetupActivity  Boolean 'com.appblock.ui.PermissionSetupActivity  Bundle 'com.appblock.ui.PermissionSetupActivity  Button 'com.appblock.ui.PermissionSetupActivity  
ContextCompat 'com.appblock.ui.PermissionSetupActivity  	Exception 'com.appblock.ui.PermissionSetupActivity  Int 'com.appblock.ui.PermissionSetupActivity  IntArray 'com.appblock.ui.PermissionSetupActivity  Intent 'com.appblock.ui.PermissionSetupActivity  LinearLayout 'com.appblock.ui.PermissionSetupActivity  Log 'com.appblock.ui.PermissionSetupActivity  PermissionUtils 'com.appblock.ui.PermissionSetupActivity  REQUEST_ACCESSIBILITY 'com.appblock.ui.PermissionSetupActivity  REQUEST_DEVICE_ADMIN 'com.appblock.ui.PermissionSetupActivity  REQUEST_NOTIFICATIONS 'com.appblock.ui.PermissionSetupActivity  REQUEST_OVERLAY 'com.appblock.ui.PermissionSetupActivity  REQUEST_USAGE_STATS 'com.appblock.ui.PermissionSetupActivity  
ScrollView 'com.appblock.ui.PermissionSetupActivity  String 'com.appblock.ui.PermissionSetupActivity  TAG 'com.appblock.ui.PermissionSetupActivity  TextView 'com.appblock.ui.PermissionSetupActivity  Toast 'com.appblock.ui.PermissionSetupActivity  all 'com.appblock.ui.PermissionSetupActivity  android 'com.appblock.ui.PermissionSetupActivity  apply 'com.appblock.ui.PermissionSetupActivity  arrayOf 'com.appblock.ui.PermissionSetupActivity  btnContinue 'com.appblock.ui.PermissionSetupActivity  count 'com.appblock.ui.PermissionSetupActivity  createPermissionCard 'com.appblock.ui.PermissionSetupActivity  createUI 'com.appblock.ui.PermissionSetupActivity  finish 'com.appblock.ui.PermissionSetupActivity  finishSetup 'com.appblock.ui.PermissionSetupActivity  getALL 'com.appblock.ui.PermissionSetupActivity  
getANDROID 'com.appblock.ui.PermissionSetupActivity  getAPPLY 'com.appblock.ui.PermissionSetupActivity  
getARRAYOf 'com.appblock.ui.PermissionSetupActivity  getAll 'com.appblock.ui.PermissionSetupActivity  
getAndroid 'com.appblock.ui.PermissionSetupActivity  getApply 'com.appblock.ui.PermissionSetupActivity  
getArrayOf 'com.appblock.ui.PermissionSetupActivity  getCOUNT 'com.appblock.ui.PermissionSetupActivity  getCount 'com.appblock.ui.PermissionSetupActivity  	getLISTOf 'com.appblock.ui.PermissionSetupActivity  	getListOf 'com.appblock.ui.PermissionSetupActivity  listOf 'com.appblock.ui.PermissionSetupActivity  permissionContainer 'com.appblock.ui.PermissionSetupActivity  requestPermission 'com.appblock.ui.PermissionSetupActivity  requestPermissions 'com.appblock.ui.PermissionSetupActivity  
scrollView 'com.appblock.ui.PermissionSetupActivity  setContentView 'com.appblock.ui.PermissionSetupActivity  startActivityForResult 'com.appblock.ui.PermissionSetupActivity  tvStatus 'com.appblock.ui.PermissionSetupActivity  updateOverallStatus 'com.appblock.ui.PermissionSetupActivity  updatePermissionStatus 'com.appblock.ui.PermissionSetupActivity  Array 1com.appblock.ui.PermissionSetupActivity.Companion  Boolean 1com.appblock.ui.PermissionSetupActivity.Companion  Bundle 1com.appblock.ui.PermissionSetupActivity.Companion  Button 1com.appblock.ui.PermissionSetupActivity.Companion  
ContextCompat 1com.appblock.ui.PermissionSetupActivity.Companion  	Exception 1com.appblock.ui.PermissionSetupActivity.Companion  Int 1com.appblock.ui.PermissionSetupActivity.Companion  IntArray 1com.appblock.ui.PermissionSetupActivity.Companion  Intent 1com.appblock.ui.PermissionSetupActivity.Companion  LinearLayout 1com.appblock.ui.PermissionSetupActivity.Companion  Log 1com.appblock.ui.PermissionSetupActivity.Companion  PermissionUtils 1com.appblock.ui.PermissionSetupActivity.Companion  REQUEST_ACCESSIBILITY 1com.appblock.ui.PermissionSetupActivity.Companion  REQUEST_DEVICE_ADMIN 1com.appblock.ui.PermissionSetupActivity.Companion  REQUEST_NOTIFICATIONS 1com.appblock.ui.PermissionSetupActivity.Companion  REQUEST_OVERLAY 1com.appblock.ui.PermissionSetupActivity.Companion  REQUEST_USAGE_STATS 1com.appblock.ui.PermissionSetupActivity.Companion  
ScrollView 1com.appblock.ui.PermissionSetupActivity.Companion  String 1com.appblock.ui.PermissionSetupActivity.Companion  TAG 1com.appblock.ui.PermissionSetupActivity.Companion  TextView 1com.appblock.ui.PermissionSetupActivity.Companion  Toast 1com.appblock.ui.PermissionSetupActivity.Companion  all 1com.appblock.ui.PermissionSetupActivity.Companion  android 1com.appblock.ui.PermissionSetupActivity.Companion  apply 1com.appblock.ui.PermissionSetupActivity.Companion  arrayOf 1com.appblock.ui.PermissionSetupActivity.Companion  count 1com.appblock.ui.PermissionSetupActivity.Companion  finishSetup 1com.appblock.ui.PermissionSetupActivity.Companion  getALL 1com.appblock.ui.PermissionSetupActivity.Companion  
getANDROID 1com.appblock.ui.PermissionSetupActivity.Companion  getAPPLY 1com.appblock.ui.PermissionSetupActivity.Companion  
getARRAYOf 1com.appblock.ui.PermissionSetupActivity.Companion  getAll 1com.appblock.ui.PermissionSetupActivity.Companion  
getAndroid 1com.appblock.ui.PermissionSetupActivity.Companion  getApply 1com.appblock.ui.PermissionSetupActivity.Companion  
getArrayOf 1com.appblock.ui.PermissionSetupActivity.Companion  getCOUNT 1com.appblock.ui.PermissionSetupActivity.Companion  getCount 1com.appblock.ui.PermissionSetupActivity.Companion  	getLISTOf 1com.appblock.ui.PermissionSetupActivity.Companion  	getListOf 1com.appblock.ui.PermissionSetupActivity.Companion  listOf 1com.appblock.ui.PermissionSetupActivity.Companion  requestPermission 1com.appblock.ui.PermissionSetupActivity.Companion  
ContextCompat 	java.lang  REQUEST_ACCESSIBILITY 	java.lang  REQUEST_DEVICE_ADMIN 	java.lang  REQUEST_NOTIFICATIONS 	java.lang  REQUEST_OVERLAY 	java.lang  REQUEST_USAGE_STATS 	java.lang  
ScrollView 	java.lang  finishSetup 	java.lang  requestPermission 	java.lang  
ContextCompat kotlin  IntArray kotlin  REQUEST_ACCESSIBILITY kotlin  REQUEST_DEVICE_ADMIN kotlin  REQUEST_NOTIFICATIONS kotlin  REQUEST_OVERLAY kotlin  REQUEST_USAGE_STATS kotlin  
ScrollView kotlin  finishSetup kotlin  requestPermission kotlin  
ContextCompat kotlin.annotation  REQUEST_ACCESSIBILITY kotlin.annotation  REQUEST_DEVICE_ADMIN kotlin.annotation  REQUEST_NOTIFICATIONS kotlin.annotation  REQUEST_OVERLAY kotlin.annotation  REQUEST_USAGE_STATS kotlin.annotation  
ScrollView kotlin.annotation  finishSetup kotlin.annotation  requestPermission kotlin.annotation  
ContextCompat kotlin.collections  REQUEST_ACCESSIBILITY kotlin.collections  REQUEST_DEVICE_ADMIN kotlin.collections  REQUEST_NOTIFICATIONS kotlin.collections  REQUEST_OVERLAY kotlin.collections  REQUEST_USAGE_STATS kotlin.collections  
ScrollView kotlin.collections  finishSetup kotlin.collections  requestPermission kotlin.collections  getALL kotlin.collections.List  getAll kotlin.collections.List  
ContextCompat kotlin.comparisons  REQUEST_ACCESSIBILITY kotlin.comparisons  REQUEST_DEVICE_ADMIN kotlin.comparisons  REQUEST_NOTIFICATIONS kotlin.comparisons  REQUEST_OVERLAY kotlin.comparisons  REQUEST_USAGE_STATS kotlin.comparisons  
ScrollView kotlin.comparisons  finishSetup kotlin.comparisons  requestPermission kotlin.comparisons  
ContextCompat 	kotlin.io  REQUEST_ACCESSIBILITY 	kotlin.io  REQUEST_DEVICE_ADMIN 	kotlin.io  REQUEST_NOTIFICATIONS 	kotlin.io  REQUEST_OVERLAY 	kotlin.io  REQUEST_USAGE_STATS 	kotlin.io  
ScrollView 	kotlin.io  finishSetup 	kotlin.io  requestPermission 	kotlin.io  
ContextCompat 
kotlin.jvm  REQUEST_ACCESSIBILITY 
kotlin.jvm  REQUEST_DEVICE_ADMIN 
kotlin.jvm  REQUEST_NOTIFICATIONS 
kotlin.jvm  REQUEST_OVERLAY 
kotlin.jvm  REQUEST_USAGE_STATS 
kotlin.jvm  
ScrollView 
kotlin.jvm  finishSetup 
kotlin.jvm  requestPermission 
kotlin.jvm  
ContextCompat 
kotlin.ranges  REQUEST_ACCESSIBILITY 
kotlin.ranges  REQUEST_DEVICE_ADMIN 
kotlin.ranges  REQUEST_NOTIFICATIONS 
kotlin.ranges  REQUEST_OVERLAY 
kotlin.ranges  REQUEST_USAGE_STATS 
kotlin.ranges  
ScrollView 
kotlin.ranges  finishSetup 
kotlin.ranges  requestPermission 
kotlin.ranges  
ContextCompat kotlin.sequences  REQUEST_ACCESSIBILITY kotlin.sequences  REQUEST_DEVICE_ADMIN kotlin.sequences  REQUEST_NOTIFICATIONS kotlin.sequences  REQUEST_OVERLAY kotlin.sequences  REQUEST_USAGE_STATS kotlin.sequences  
ScrollView kotlin.sequences  finishSetup kotlin.sequences  requestPermission kotlin.sequences  
ContextCompat kotlin.text  REQUEST_ACCESSIBILITY kotlin.text  REQUEST_DEVICE_ADMIN kotlin.text  REQUEST_NOTIFICATIONS kotlin.text  REQUEST_OVERLAY kotlin.text  REQUEST_USAGE_STATS kotlin.text  
ScrollView kotlin.text  finishSetup kotlin.text  requestPermission kotlin.text  openPermissionSetup android.app.Activity  openPermissionSetup android.content.Context  openPermissionSetup android.content.ContextWrapper  openPermissionSetup  android.view.ContextThemeWrapper  openPermissionSetup android.widget  getOPENPermissionSetup android.widget.Button  getOpenPermissionSetup android.widget.Button  openPermissionSetup android.widget.Button  openPermissionSetup com.appblock  openPermissionSetup com.appblock.MainActivity  openPermissionSetup #com.appblock.MainActivity.Companion  openPermissionSetup 	java.lang  openPermissionSetup kotlin  openPermissionSetup kotlin.annotation  openPermissionSetup kotlin.collections  openPermissionSetup kotlin.comparisons  openPermissionSetup 	kotlin.io  openPermissionSetup 
kotlin.jvm  openPermissionSetup 
kotlin.ranges  openPermissionSetup kotlin.sequences  openPermissionSetup kotlin.text  ACTION_DOWN android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  	getACTION android.view.MotionEvent  	getAction android.view.MotionEvent  	setAction android.view.MotionEvent  blockAppInSplitScreen 1android.accessibilityservice.AccessibilityService  exitSplitScreenMode 1android.accessibilityservice.AccessibilityService  isInSplitScreenMode 1android.accessibilityservice.AccessibilityService  showFullScreenBlockOverlay 1android.accessibilityservice.AccessibilityService  ActivityManager android.app  RunningTaskInfo android.app.ActivityManager  getRunningTasks android.app.ActivityManager  
numActivities +android.app.ActivityManager.RunningTaskInfo  blockAppInSplitScreen android.app.Service  exitSplitScreenMode android.app.Service  isInSplitScreenMode android.app.Service  showFullScreenBlockOverlay android.app.Service  ACTIVITY_SERVICE android.content.Context  blockAppInSplitScreen android.content.Context  exitSplitScreenMode android.content.Context  isInSplitScreenMode android.content.Context  showFullScreenBlockOverlay android.content.Context  blockAppInSplitScreen android.content.ContextWrapper  exitSplitScreenMode android.content.ContextWrapper  isInSplitScreenMode android.content.ContextWrapper  showFullScreenBlockOverlay android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_HARDWARE_ACCELERATED 'android.view.WindowManager.LayoutParams  android 'android.view.WindowManager.LayoutParams  
getANDROID 'android.view.WindowManager.LayoutParams  
getAndroid 'android.view.WindowManager.LayoutParams  x 'android.view.WindowManager.LayoutParams  y 'android.view.WindowManager.LayoutParams  AccessibilityWindowInfo android.view.accessibility  blockAppInSplitScreen 1com.appblock.service.AppBlockAccessibilityService  exitSplitScreenMode 1com.appblock.service.AppBlockAccessibilityService  
getWINDOWS 1com.appblock.service.AppBlockAccessibilityService  
getWindows 1com.appblock.service.AppBlockAccessibilityService  isInSplitScreenMode 1com.appblock.service.AppBlockAccessibilityService  
setWindows 1com.appblock.service.AppBlockAccessibilityService  showFullScreenBlockOverlay 1com.appblock.service.AppBlockAccessibilityService  windows 1com.appblock.service.AppBlockAccessibilityService  InterruptedException 1android.accessibilityservice.AccessibilityService  Thread 1android.accessibilityservice.AccessibilityService  checkAllVisibleWindows 1android.accessibilityservice.AccessibilityService  handleViewAccessibilityFocused 1android.accessibilityservice.AccessibilityService  handleViewFocused 1android.accessibilityservice.AccessibilityService  handleWindowsChanged 1android.accessibilityservice.AccessibilityService  startContinuousMonitoring 1android.accessibilityservice.AccessibilityService  'FLAG_REQUEST_ENHANCED_WEB_ACCESSIBILITY 5android.accessibilityservice.AccessibilityServiceInfo  FLAG_REQUEST_FILTER_KEY_EVENTS 5android.accessibilityservice.AccessibilityServiceInfo  Thread android.app.Service  checkAllVisibleWindows android.app.Service  handleViewAccessibilityFocused android.app.Service  handleViewFocused android.app.Service  handleWindowsChanged android.app.Service  startContinuousMonitoring android.app.Service  Thread android.content.Context  checkAllVisibleWindows android.content.Context  handleViewAccessibilityFocused android.content.Context  handleViewFocused android.content.Context  handleWindowsChanged android.content.Context  startContinuousMonitoring android.content.Context  Thread android.content.ContextWrapper  checkAllVisibleWindows android.content.ContextWrapper  handleViewAccessibilityFocused android.content.ContextWrapper  handleViewFocused android.content.ContextWrapper  handleWindowsChanged android.content.ContextWrapper  startContinuousMonitoring android.content.ContextWrapper  TYPE_VIEW_ACCESSIBILITY_FOCUSED -android.view.accessibility.AccessibilityEvent  TYPE_VIEW_FOCUSED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOWS_CHANGED -android.view.accessibility.AccessibilityEvent  getPACKAGEName 0android.view.accessibility.AccessibilityNodeInfo  getPackageName 0android.view.accessibility.AccessibilityNodeInfo  packageName 0android.view.accessibility.AccessibilityNodeInfo  setPackageName 0android.view.accessibility.AccessibilityNodeInfo  getROOT 2android.view.accessibility.AccessibilityWindowInfo  getRoot 2android.view.accessibility.AccessibilityWindowInfo  root 2android.view.accessibility.AccessibilityWindowInfo  setRoot 2android.view.accessibility.AccessibilityWindowInfo  Thread com.appblock.service  InterruptedException 1com.appblock.service.AppBlockAccessibilityService  Thread 1com.appblock.service.AppBlockAccessibilityService  checkAllVisibleWindows 1com.appblock.service.AppBlockAccessibilityService  handleViewAccessibilityFocused 1com.appblock.service.AppBlockAccessibilityService  handleViewFocused 1com.appblock.service.AppBlockAccessibilityService  handleWindowsChanged 1com.appblock.service.AppBlockAccessibilityService  mainHandler 1com.appblock.service.AppBlockAccessibilityService  startContinuousMonitoring 1com.appblock.service.AppBlockAccessibilityService  InterruptedException ;com.appblock.service.AppBlockAccessibilityService.Companion  Thread ;com.appblock.service.AppBlockAccessibilityService.Companion  getISDaemon java.lang.Thread  getIsDaemon java.lang.Thread  isDaemon java.lang.Thread  	setDaemon java.lang.Thread                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  