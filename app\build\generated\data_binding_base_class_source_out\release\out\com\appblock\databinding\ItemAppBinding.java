// Generated by view binder compiler. Do not edit!
package com.appblock.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.appblock.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAppBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox checkboxBlocked;

  @NonNull
  public final ImageView ivAppIcon;

  @NonNull
  public final TextView tvAppName;

  @NonNull
  public final TextView tvPackageName;

  @NonNull
  public final TextView tvSystemApp;

  private ItemAppBinding(@NonNull LinearLayout rootView, @NonNull CheckBox checkboxBlocked,
      @NonNull ImageView ivAppIcon, @NonNull TextView tvAppName, @NonNull TextView tvPackageName,
      @NonNull TextView tvSystemApp) {
    this.rootView = rootView;
    this.checkboxBlocked = checkboxBlocked;
    this.ivAppIcon = ivAppIcon;
    this.tvAppName = tvAppName;
    this.tvPackageName = tvPackageName;
    this.tvSystemApp = tvSystemApp;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAppBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAppBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_app, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAppBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkbox_blocked;
      CheckBox checkboxBlocked = ViewBindings.findChildViewById(rootView, id);
      if (checkboxBlocked == null) {
        break missingId;
      }

      id = R.id.iv_app_icon;
      ImageView ivAppIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAppIcon == null) {
        break missingId;
      }

      id = R.id.tv_app_name;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      id = R.id.tv_package_name;
      TextView tvPackageName = ViewBindings.findChildViewById(rootView, id);
      if (tvPackageName == null) {
        break missingId;
      }

      id = R.id.tv_system_app;
      TextView tvSystemApp = ViewBindings.findChildViewById(rootView, id);
      if (tvSystemApp == null) {
        break missingId;
      }

      return new ItemAppBinding((LinearLayout) rootView, checkboxBlocked, ivAppIcon, tvAppName,
          tvPackageName, tvSystemApp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
