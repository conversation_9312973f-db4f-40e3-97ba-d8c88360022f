package com.appblock.test

import android.content.Context
import android.util.Log
import com.appblock.data.BlockedAppsManager
import com.appblock.data.SettingsManager

/**
 * Simple test class to verify core functionality without UI
 */
class CoreFunctionalityTest(private val context: Context) {

    companion object {
        private const val TAG = "CoreFunctionalityTest"
    }

    fun runBasicTests() {
        Log.d(TAG, "Starting core functionality tests...")
        
        testBlockedAppsManager()
        testSettingsManager()
        
        Log.d(TAG, "Core functionality tests completed")
    }

    private fun testBlockedAppsManager() {
        Log.d(TAG, "Testing BlockedAppsManager...")
        
        val blockedAppsManager = BlockedAppsManager(context)
        
        // Test adding blocked app
        blockedAppsManager.addBlockedApp("com.example.testapp")
        val isBlocked = blockedAppsManager.isAppBlocked("com.example.testapp")
        Log.d(TAG, "App blocking test: ${if (isBlocked) "PASS" else "FAIL"}")
        
        // Test removing blocked app
        blockedAppsManager.removeBlockedApp("com.example.testapp")
        val isStillBlocked = blockedAppsManager.isAppBlocked("com.example.testapp")
        Log.d(TAG, "App unblocking test: ${if (!isStillBlocked) "PASS" else "FAIL"}")
        
        // Test time limits
        blockedAppsManager.addBlockedApp("com.example.timelimitapp")
        blockedAppsManager.setTimeLimit("com.example.timelimitapp", 30 * 60 * 1000L) // 30 minutes
        val remainingTime = blockedAppsManager.getRemainingTime("com.example.timelimitapp")
        Log.d(TAG, "Time limit test: remaining time = ${remainingTime / (60 * 1000)} minutes")
        
        // Clean up
        blockedAppsManager.removeBlockedApp("com.example.timelimitapp")
    }

    private fun testSettingsManager() {
        Log.d(TAG, "Testing SettingsManager...")
        
        val settingsManager = SettingsManager(context)
        
        // Test app blocking toggle
        settingsManager.setAppBlockingEnabled(true)
        val isEnabled = settingsManager.isAppBlockingEnabled()
        Log.d(TAG, "App blocking enable test: ${if (isEnabled) "PASS" else "FAIL"}")
        
        // Test PIN functionality
        settingsManager.setParentPin("1234")
        val hasPin = settingsManager.hasParentPin()
        val pinCorrect = settingsManager.verifyParentPin("1234")
        val pinIncorrect = settingsManager.verifyParentPin("5678")
        
        Log.d(TAG, "PIN set test: ${if (hasPin) "PASS" else "FAIL"}")
        Log.d(TAG, "PIN verify correct test: ${if (pinCorrect) "PASS" else "FAIL"}")
        Log.d(TAG, "PIN verify incorrect test: ${if (!pinIncorrect) "PASS" else "FAIL"}")
        
        // Test tampering detection
        settingsManager.recordTamperingAttempt(System.currentTimeMillis())
        val tamperingCount = settingsManager.getTamperingAttemptsCount()
        Log.d(TAG, "Tampering detection test: count = $tamperingCount")
        
        // Clean up
        settingsManager.setAppBlockingEnabled(false)
    }
}
