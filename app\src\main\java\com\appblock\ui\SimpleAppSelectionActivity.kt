package com.appblock.ui

import android.app.Activity
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import com.appblock.data.BlockedAppsManager
import kotlinx.coroutines.*

class SimpleAppSelectionActivity : Activity() {

    companion object {
        private const val TAG = "AppSelection"
    }

    private lateinit var blockedAppsManager: BlockedAppsManager
    private lateinit var listView: ListView
    private lateinit var progressBar: ProgressBar
    private lateinit var adapter: AppListAdapter
    
    private var allApps = mutableListOf<AppInfo>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    data class AppInfo(
        val packageName: String,
        val appName: String,
        val icon: android.graphics.drawable.Drawable,
        var isBlocked: Boolean = false
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create simple layout programmatically
        createLayout()
        
        blockedAppsManager = BlockedAppsManager(this)
        
        loadApps()
    }

    private fun createLayout() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val title = TextView(this).apply {
            text = "Select Apps to Block"
            textSize = 20f
            setPadding(0, 0, 0, 16)
        }
        layout.addView(title)

        // Progress bar
        progressBar = ProgressBar(this).apply {
            visibility = View.VISIBLE
        }
        layout.addView(progressBar)

        // List view
        listView = ListView(this).apply {
            visibility = View.GONE
        }
        layout.addView(listView)

        setContentView(layout)
    }

    private fun loadApps() {
        Log.d(TAG, "Loading installed apps...")
        
        coroutineScope.launch {
            try {
                val apps = withContext(Dispatchers.IO) {
                    loadInstalledApps()
                }
                
                allApps.clear()
                allApps.addAll(apps)
                
                setupListView()
                
                progressBar.visibility = View.GONE
                listView.visibility = View.VISIBLE
                
                Log.d(TAG, "Loaded ${allApps.size} apps")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading apps", e)
                Toast.makeText(this@SimpleAppSelectionActivity, "Error loading apps: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private suspend fun loadInstalledApps(): List<AppInfo> {
        val packageManager = packageManager
        val installedApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
        val blockedApps = blockedAppsManager.getBlockedApps().toSet()
        
        return installedApps
            .filter { appInfo ->
                // Filter out our own app and some system essentials
                appInfo.packageName != packageName &&
                appInfo.packageName != "android" &&
                appInfo.packageName != "com.android.systemui" &&
                // Only show apps with launcher intent (user apps)
                packageManager.getLaunchIntentForPackage(appInfo.packageName) != null
            }
            .map { appInfo ->
                AppInfo(
                    packageName = appInfo.packageName,
                    appName = packageManager.getApplicationLabel(appInfo).toString(),
                    icon = packageManager.getApplicationIcon(appInfo),
                    isBlocked = blockedApps.contains(appInfo.packageName)
                )
            }
            .sortedBy { it.appName.lowercase() }
    }

    private fun setupListView() {
        adapter = AppListAdapter()
        listView.adapter = adapter
        
        listView.setOnItemClickListener { _, _, position, _ ->
            val app = allApps[position]
            toggleAppBlocking(app, position)
        }
    }

    private fun toggleAppBlocking(app: AppInfo, position: Int) {
        if (app.isBlocked) {
            // Unblock the app
            blockedAppsManager.removeBlockedApp(app.packageName)
            app.isBlocked = false
            Log.d(TAG, "Unblocked: ${app.appName}")
            Toast.makeText(this, "Unblocked ${app.appName}", Toast.LENGTH_SHORT).show()
        } else {
            // Block the app
            blockedAppsManager.addBlockedApp(app.packageName)
            app.isBlocked = true
            Log.d(TAG, "Blocked: ${app.appName}")
            Toast.makeText(this, "Blocked ${app.appName}", Toast.LENGTH_SHORT).show()
        }
        
        // Update the list view
        adapter.notifyDataSetChanged()
    }

    inner class AppListAdapter : BaseAdapter() {
        
        override fun getCount(): Int = allApps.size
        
        override fun getItem(position: Int): AppInfo = allApps[position]
        
        override fun getItemId(position: Int): Long = position.toLong()
        
        override fun getView(position: Int, convertView: View?, parent: android.view.ViewGroup?): View {
            val app = allApps[position]
            
            val layout = LinearLayout(this@SimpleAppSelectionActivity).apply {
                orientation = LinearLayout.HORIZONTAL
                setPadding(16, 12, 16, 12)
            }
            
            // App icon
            val iconView = ImageView(this@SimpleAppSelectionActivity).apply {
                setImageDrawable(app.icon)
                layoutParams = LinearLayout.LayoutParams(120, 120).apply {
                    setMargins(0, 0, 16, 0)
                }
            }
            layout.addView(iconView)
            
            // App info layout
            val infoLayout = LinearLayout(this@SimpleAppSelectionActivity).apply {
                orientation = LinearLayout.VERTICAL
                layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            }
            
            // App name
            val nameView = TextView(this@SimpleAppSelectionActivity).apply {
                text = app.appName
                textSize = 16f
                setTextColor(android.graphics.Color.BLACK)
            }
            infoLayout.addView(nameView)
            
            // Package name
            val packageView = TextView(this@SimpleAppSelectionActivity).apply {
                text = app.packageName
                textSize = 12f
                setTextColor(android.graphics.Color.GRAY)
            }
            infoLayout.addView(packageView)
            
            layout.addView(infoLayout)
            
            // Blocked status
            val statusView = TextView(this@SimpleAppSelectionActivity).apply {
                text = if (app.isBlocked) "BLOCKED" else "ALLOWED"
                textSize = 14f
                setTextColor(if (app.isBlocked) android.graphics.Color.RED else android.graphics.Color.GREEN)
                setPadding(16, 0, 0, 0)
            }
            layout.addView(statusView)
            
            // Set background color based on blocked status
            layout.setBackgroundColor(
                if (app.isBlocked) 
                    android.graphics.Color.parseColor("#FFEBEE") 
                else 
                    android.graphics.Color.WHITE
            )
            
            return layout
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        coroutineScope.cancel()
    }
}
