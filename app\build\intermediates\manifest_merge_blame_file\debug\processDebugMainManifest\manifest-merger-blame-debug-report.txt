1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appblock"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions for app blocking functionality -->
12    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
12-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:6:5-78
12-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:7:22-75
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
17-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
19-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
20
21    <permission
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
22        android:name="com.appblock.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.appblock.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:14:5-96:19
28        android:allowBackup="true"
28-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:15:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
30        android:debuggable="true"
31        android:extractNativeLibs="true"
32        android:label="App Block"
32-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:16:9-34
33        android:supportsRtl="true"
33-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:17:9-35
34        android:theme="@android:style/Theme.Material.Light" >
34-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:18:9-60
35
36        <!-- Main Activity -->
37        <activity
37-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:22:9-31:20
38            android:name="com.appblock.MainActivity"
38-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:23:13-41
39            android:exported="true"
39-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:24:13-36
40            android:label="App Block" >
40-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:25:13-38
41
42            <!-- Removed theme reference -->
43            <intent-filter>
43-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:27:13-30:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:28:17-69
44-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:28:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:29:17-77
46-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:29:27-74
47            </intent-filter>
48        </activity>
49
50        <!-- App Selection Activity -->
51        <activity
51-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:34:9-37:52
52            android:name="com.appblock.ui.SimpleAppSelectionActivity"
52-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:35:13-58
53            android:exported="false"
53-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:36:13-37
54            android:label="Select Apps to Block" />
54-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:37:13-49
55
56        <!-- Permission Setup Activity -->
57        <activity
57-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:40:9-43:47
58            android:name="com.appblock.ui.PermissionSetupActivity"
58-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:41:13-55
59            android:exported="false"
59-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:42:13-37
60            android:label="App Block Setup" />
60-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:43:13-44
61
62        <!-- Accessibility Service for app monitoring -->
63        <service
63-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:46:9-56:19
64            android:name="com.appblock.service.AppBlockAccessibilityService"
64-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:47:13-65
65            android:exported="false"
65-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:48:13-37
66            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
66-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:49:13-79
67            <intent-filter>
67-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:50:13-52:29
68                <action android:name="android.accessibilityservice.AccessibilityService" />
68-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:51:17-92
68-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:51:25-89
69            </intent-filter>
70
71            <meta-data
71-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:53:13-55:72
72                android:name="android.accessibilityservice"
72-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:54:17-60
73                android:resource="@xml/accessibility_service_config" />
73-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:55:17-69
74        </service>
75
76        <!-- Usage Stats Monitoring Service -->
77        <service
77-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:59:9-63:58
78            android:name="com.appblock.service.UsageStatsMonitorService"
78-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:60:13-61
79            android:exported="false"
79-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:61:13-37
80            android:foregroundServiceType="specialUse"
80-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:63:13-55
81            android:process=":monitor" />
81-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:62:13-39
82
83        <!-- Device Admin Receiver -->
84        <receiver
84-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:66:9-77:20
85            android:name="com.appblock.receiver.AppBlockDeviceAdminReceiver"
85-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:67:13-65
86            android:exported="true"
86-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:68:13-36
87            android:permission="android.permission.BIND_DEVICE_ADMIN" >
87-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:69:13-70
88            <meta-data
88-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:70:13-72:56
89                android:name="android.app.device_admin"
89-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:71:17-56
90                android:resource="@xml/device_admin" />
90-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:72:17-53
91
92            <intent-filter>
92-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:73:13-76:29
93                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
93-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:74:17-82
93-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:74:25-79
94                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
94-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:75:17-83
94-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:75:25-80
95            </intent-filter>
96        </receiver>
97
98        <!-- Boot Receiver -->
99        <receiver
99-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:80:9-89:20
100            android:name="com.appblock.receiver.BootReceiver"
100-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:81:13-50
101            android:exported="true" >
101-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:82:13-36
102            <intent-filter android:priority="1000" >
102-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:83:13-88:29
102-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:83:28-51
103                <action android:name="android.intent.action.BOOT_COMPLETED" />
103-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:84:17-79
103-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:84:25-76
104                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
104-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:85:17-84
104-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:85:25-81
105                <action android:name="android.intent.action.PACKAGE_REPLACED" />
105-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:86:17-81
105-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:86:25-78
106
107                <data android:scheme="package" />
107-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:87:17-50
107-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:87:23-47
108            </intent-filter>
109        </receiver>
110
111        <!-- Permission Monitor Receiver -->
112        <receiver
112-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:92:9-94:40
113            android:name="com.appblock.receiver.PermissionMonitorReceiver"
113-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:93:13-63
114            android:exported="false" />
114-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:94:13-37
115
116        <provider
116-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
117            android:name="androidx.startup.InitializationProvider"
117-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
118            android:authorities="com.appblock.androidx-startup"
118-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
119            android:exported="false" >
119-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
120            <meta-data
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.emoji2.text.EmojiCompatInitializer"
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
122                android:value="androidx.startup" />
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
123            <meta-data
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
124                android:name="androidx.work.WorkManagerInitializer"
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
125                android:value="androidx.startup" />
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <service
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
135            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
137            android:enabled="@bool/enable_system_alarm_service_default"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
138            android:exported="false" />
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
139        <service
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
140            android:name="androidx.work.impl.background.systemjob.SystemJobService"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
141            android:directBootAware="false"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
142            android:enabled="@bool/enable_system_job_service_default"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
143            android:exported="true"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
145        <service
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
146            android:name="androidx.work.impl.foreground.SystemForegroundService"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
148            android:enabled="@bool/enable_system_foreground_service_default"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
149            android:exported="false" />
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
150
151        <receiver
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
152            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
154            android:enabled="true"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
155            android:exported="false" />
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
156        <receiver
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
157            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
159            android:enabled="false"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
160            android:exported="false" >
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
161            <intent-filter>
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
162                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
163                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
164            </intent-filter>
165        </receiver>
166        <receiver
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
169            android:enabled="false"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
170            android:exported="false" >
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
171            <intent-filter>
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
172                <action android:name="android.intent.action.BATTERY_OKAY" />
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
173                <action android:name="android.intent.action.BATTERY_LOW" />
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
182                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
183                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
184            </intent-filter>
185        </receiver>
186        <receiver
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
188            android:directBootAware="false"
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
189            android:enabled="false"
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
190            android:exported="false" >
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
191            <intent-filter>
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
192                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
193            </intent-filter>
194        </receiver>
195        <receiver
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
196            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
197            android:directBootAware="false"
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
198            android:enabled="false"
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
199            android:exported="false" >
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
200            <intent-filter>
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
201                <action android:name="android.intent.action.BOOT_COMPLETED" />
201-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:84:17-79
201-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:84:25-76
202                <action android:name="android.intent.action.TIME_SET" />
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
203                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
204            </intent-filter>
205        </receiver>
206        <receiver
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
207            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
208            android:directBootAware="false"
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
209            android:enabled="@bool/enable_system_alarm_service_default"
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
210            android:exported="false" >
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
211            <intent-filter>
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
212                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
213            </intent-filter>
214        </receiver>
215        <receiver
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
216            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
217            android:directBootAware="false"
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
218            android:enabled="true"
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
219            android:exported="true"
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
220            android:permission="android.permission.DUMP" >
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
221            <intent-filter>
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
222                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
223            </intent-filter>
224        </receiver>
225
226        <uses-library
226-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
227            android:name="androidx.window.extensions"
227-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
228            android:required="false" />
228-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
229        <uses-library
229-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
230            android:name="androidx.window.sidecar"
230-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
231            android:required="false" />
231-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
232
233        <service
233-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
234            android:name="androidx.room.MultiInstanceInvalidationService"
234-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
235            android:directBootAware="true"
235-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
236            android:exported="false" />
236-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
237
238        <receiver
238-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
239            android:name="androidx.profileinstaller.ProfileInstallReceiver"
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
240            android:directBootAware="false"
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
241            android:enabled="true"
241-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
242            android:exported="true"
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
243            android:permission="android.permission.DUMP" >
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
245                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
248                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
248-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
248-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
251                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
251-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
251-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
254                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
254-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
254-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
255            </intent-filter>
256        </receiver>
257    </application>
258
259</manifest>
