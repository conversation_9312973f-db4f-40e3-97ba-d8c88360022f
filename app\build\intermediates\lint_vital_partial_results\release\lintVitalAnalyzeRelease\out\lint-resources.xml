http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_monitor.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_android.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_access_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_admin_panel_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/pin_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_security.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_block.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/block_card_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_shield.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_deselect_all.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_lock.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/system_app_badge.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_select_all.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_pin_auth.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_block_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/block_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_app_selection.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_app.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/app_selection_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_setup.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_verify.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/accessibility_service_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/device_admin.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:purple_200,0,V400020037,2e00020061,;"#FFBB86FC";blocked_app_background,0,V4001202bc,38001202f0,;"#FFEBEE";pin_button_pressed,0,V4001b0403,36001b0435,;"#FFE0E0E0";black,0,V40007011e,**********,;"#FF000000";pin_button_background,0,V4001a03c9,39001a03fe,;"#FFF5F5F5";accent,0,V4000d01f5,2a000d021b,;"#FFFF5722";teal_200,0,V4000500c4,2c000500ec,;"#FF03DAC5";success_green,0,V400150315,**********,;"#FF4CAF50";block_card_background,0,V400110284,37001102b7,;"#FFFFFF";error_red,0,V40017037a,2d001703a3,;"#FFF44336";white,0,V400080148,290008016d,;"#FFFFFFFF";teal_700,0,V4000600f1,2c00060119,;"#FF018786";block_overlay_background,0,V400100247,3c0010027f,;"#E6000000";purple_700,0,V400040095,2e000400bf,;"#FF3700B3";purple_500,0,V400030066,2e00030090,;"#FF6200EE";primary_dark,0,V4000c01c4,30000c01f0,;"#FF1976D2";warning_orange,0,V400160347,**********,;"#FFFF9800";primary,0,V4000b0198,2b000b01bf,;"#FF2196F3";+drawable:ic_monitor,1,F;ic_android,2,F;ic_access_time,3,F;ic_admin_panel_settings,4,F;ic_list,5,F;pin_button_background,6,F;ic_home,7,F;ic_security,8,F;ic_block,9,F;block_card_background,10,F;ic_settings,11,F;ic_shield,12,F;ic_deselect_all,13,F;ic_info,14,F;ic_lock,15,F;system_app_badge,16,F;ic_select_all,17,F;ic_search,18,F;+id:tv_title,19,F;btn_enter_pin,20,F;btn_enter_pin,21,F;btn_backspace,19,F;tv_message,20,F;tv_message,19,F;btn_6,19,F;btn_5,19,F;btn_show_all,22,F;iv_app_icon,20,F;iv_app_icon,23,F;btn_4,19,F;btn_3,19,F;card_blocked_apps,24,F;btn_9,19,F;btn_8,19,F;btn_7,19,F;action_about,25,F;btn_2,19,F;btn_1,19,F;btn_0,19,F;card_settings,24,F;tv_system_app,23,F;action_search,26,F;btn_setup_pin,24,F;card_permissions,24,F;action_blocked_count,26,F;btn_go_home,20,F;btn_go_home,21,F;tv_source,20,F;tv_remaining_time,20,F;btn_clear,19,F;btn_show_system_apps,22,F;switch_app_blocking,24,F;checkbox_blocked,23,F;progress_bar,22,F;btn_verify,19,F;action_deselect_all,26,F;tv_blocked_apps_count,24,F;btn_request_time,20,F;et_confirm_pin,27,F;tv_package_name,23,F;tv_app_count,22,F;et_pin,19,F;et_pin,27,F;et_pin,28,F;btn_device_admin,24,F;toolbar,22,F;toolbar,24,F;tv_permission_status,24,F;btn_show_user_apps,22,F;btn_cancel,19,F;recycler_view,22,F;action_select_all,26,F;action_logs,25,F;tv_app_name,20,F;tv_app_name,19,F;tv_app_name,23,F;+layout:item_app,23,F;activity_main,24,F;dialog_pin_setup,27,F;activity_pin_auth,19,F;dialog_pin_verify,28,F;activity_block_overlay,20,F;block_overlay,21,F;activity_app_selection,22,F;+menu:main_menu,25,F;app_selection_menu,26,F;+string:cancel,29,V4001d04c0,29001d04e5,;"Cancel";no,29,V400320898,21003208b5,;"No";show_user_apps,29,V4000f0249,2f000f0274,;"User";save,29,V400350910,2500350931,;"Save";show_all,29,V4000e0220,28000e0244,;"All";action_deselect_all,29,V4002a0761,3c002a0799,;"Deselect All";delete,29,V400360936,290036095b,;"Delete";select_apps_to_block,29,V4000d01da,45000d021b,;"Select Apps to Block";enter_pin,29,V4001603a1,2f001603cc,;"Enter PIN";notification_text,29,V400250687,58002506db,;"Parental controls are monitoring app usage";action_about,29,V4002d080a,2e002d0834,;"About";app_blocked_title,29,V400140304,3900140339,;"App Blocked";request_time,29,V4001803fd,350018042e,;"Request Time";app_blocking,29,V40008011e,350008014f,;"App Blocking";permissions,29,V400050086,33000500b5,;"Permissions";enable,29,V4003308ba,29003308df,;"Enable";verify,29,V4001c0496,29001c04bb,;"Verify";accessibility_service_description,29,V40021053a,ec00210622,;"App Block uses this service to monitor which apps are opened and block restricted apps by showing an overlay screen. This service does not collect or store any personal data.";ok,29,V400300852,210030086f,;"OK";show_system_apps,29,V400100279,33001002a8,;"System";settings,29,V4000700f0,2d00070119,;"Settings";go_home,29,V4001703d1,2b001703f8,;"Go Home";action_search,29,V4002806f7,3000280723,;"Search";yes,29,V400310874,2300310893,;"Yes";clear,29,V4001e04ea,27001e050d,;"Clear";enter_parent_pin,29,V4001b0458,3d001b0491,;"Enter Parent PIN";action_blocked_count,29,V4002b079e,3e002b07d8,;"Blocked Count";blocked_apps,29,V4000600ba,35000600eb,;"Blocked Apps";device_admin,29,V4000a0184,35000a01b5,;"Device Admin";app_name,29,V400020037,2e00020061,;"App Block";setup_pin,29,V400090154,2f0009017f,;"Setup PIN";disable,29,V4003408e4,2b0034090b,;"Disable";notification_title,29,V400240647,3f00240682,;"App Block Active";search_apps,29,V4001102ad,36001102df,;"Search apps...";action_select_all,29,V400290728,380029075c,;"Select All";action_logs,29,V4002c07dd,2c002c0805,;"Logs";app_blocked_message,29,V40015033e,620015039c,;"This app is currently blocked by parental controls";+style:Theme.AppBlock.Fullscreen,30,V40012037a,c0018050a,;DTheme.AppBlock,android\:windowFullscreen:true,android\:windowNoTitle:true,android\:windowBackground:@color/block_overlay_background,android\:windowIsTranslucent:false,android\:windowAnimationStyle:@null,;Theme.AppBlock.Dialog,30,V4001b0545,c001e0627,;DTheme.Material3.DayNight.Dialog,colorPrimary:@color/primary,android\:windowBackground:@android\:color/transparent,;Theme.AppBlock,30,V40003008b,c000f0340,;DTheme.Material3.DayNight,colorPrimary:@color/primary,colorPrimaryVariant:@color/primary_dark,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;PinButtonStyle,30,V40021064f,c002a083e,;Nandroid\:layout_width:0dp,android\:layout_height:60dp,android\:layout_weight:1,android\:layout_margin:4dp,android\:background:@drawable/pin_button_background,android\:textSize:18sp,android\:textStyle:bold,android\:textColor:@color/black,;+xml:accessibility_service_config,31,F;device_admin,32,F;data_extraction_rules,33,F;backup_rules,34,F;