package com.appblock

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import com.appblock.data.BlockedAppsManager
import com.appblock.service.UsageStatsMonitorService
import com.appblock.test.CoreFunctionalityTest
import com.appblock.utils.PermissionUtils

/**
 * Simplified MainActivity for core functionality testing
 */
class MainActivity : Activity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    // Store references to UI elements so we can update them
    private lateinit var statusText: TextView
    private lateinit var blockedCountText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "MainActivity created")

        // Create UI
        createUI()

        // Check if we need to show permission setup
        if (shouldShowPermissionSetup()) {
            showPermissionSetup()
        } else {
            // Run core functionality tests
            runCoreTests()

            // Start monitoring service if permissions are available
            startMonitoringIfReady()
        }
    }

    private fun createUI() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }

        // Title
        val title = TextView(this).apply {
            text = "App Block - Parental Control"
            textSize = 24f
            setPadding(0, 0, 0, 32)
            gravity = android.view.Gravity.CENTER
        }
        layout.addView(title)

        // Select Apps Button
        val selectAppsButton = Button(this).apply {
            text = "Select Apps to Block"
            textSize = 18f
            setPadding(16, 16, 16, 16)
            setOnClickListener {
                openAppSelection()
            }
        }
        layout.addView(selectAppsButton)

        // Permission Setup Button
        val permissionButton = Button(this).apply {
            text = "Check Permissions"
            textSize = 16f
            setPadding(16, 16, 16, 16)
            setOnClickListener {
                openPermissionSetup()
            }
        }
        layout.addView(permissionButton)

        // Status Text
        statusText = TextView(this).apply {
            text = "Status: Checking..."
            textSize = 16f
            setPadding(0, 32, 0, 16)
        }
        layout.addView(statusText)

        // Blocked Apps Count
        blockedCountText = TextView(this).apply {
            text = "Blocked apps: 0"
            textSize = 14f
            setPadding(0, 0, 0, 16)
        }
        layout.addView(blockedCountText)

        setContentView(layout)
    }

    private fun shouldShowPermissionSetup(): Boolean {
        // Show permission setup if critical permissions are missing
        val hasCriticalPermissions = PermissionUtils.hasUsageStatsPermission(this) &&
                                   PermissionUtils.isAccessibilityServiceEnabled(this)

        Log.d(TAG, "Critical permissions check: $hasCriticalPermissions")
        return !hasCriticalPermissions
    }

    private fun showPermissionSetup() {
        Log.d(TAG, "Launching permission setup activity")

        // Update UI to show we're in setup mode
        statusText.text = "Status: ⚙️ Setup Required"
        blockedCountText.text = "Please complete setup first"

        val intent = Intent(this, com.appblock.ui.PermissionSetupActivity::class.java)
        startActivity(intent)
    }

    private fun openAppSelection() {
        Log.d(TAG, "Opening app selection...")
        val intent = Intent(this, com.appblock.ui.SimpleAppSelectionActivity::class.java)
        startActivity(intent)
    }

    private fun openPermissionSetup() {
        Log.d(TAG, "Opening permission setup...")
        val intent = Intent(this, com.appblock.ui.PermissionSetupActivity::class.java)
        startActivity(intent)
    }

    private fun runCoreTests() {
        Log.d(TAG, "Running core functionality tests...")

        try {
            val coreTest = CoreFunctionalityTest(this)
            coreTest.runBasicTests()

            Toast.makeText(this, "Core tests completed - check logs", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            Log.e(TAG, "Error running core tests", e)
            Toast.makeText(this, "Core tests failed: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun checkPermissions() {
        Log.d(TAG, "Checking permissions...")

        val hasUsageStats = PermissionUtils.hasUsageStatsPermission(this)
        val hasAccessibility = PermissionUtils.isAccessibilityServiceEnabled(this)
        val hasDeviceAdmin = PermissionUtils.isDeviceAdminActive(this)

        Log.d(TAG, "Usage Stats permission: $hasUsageStats")
        Log.d(TAG, "Accessibility Service: $hasAccessibility")
        Log.d(TAG, "Device Admin: $hasDeviceAdmin")

        if (!hasUsageStats) {
            Log.w(TAG, "Usage Stats permission not granted")
            Toast.makeText(this, "Please grant Usage Stats permission", Toast.LENGTH_LONG).show()
        }

        if (!hasAccessibility) {
            Log.w(TAG, "Accessibility Service not enabled")
            Toast.makeText(this, "Please enable Accessibility Service", Toast.LENGTH_LONG).show()
        }
    }

    private fun startMonitoringIfReady() {
        if (PermissionUtils.hasUsageStatsPermission(this)) {
            Log.d(TAG, "Starting Usage Stats monitoring service...")

            val serviceIntent = Intent(this, UsageStatsMonitorService::class.java)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }

            Toast.makeText(this, "Monitoring service started", Toast.LENGTH_SHORT).show()
        } else {
            Log.w(TAG, "Cannot start monitoring - missing permissions")
        }
    }

    override fun onResume() {
        super.onResume()

        Log.d(TAG, "MainActivity resumed")

        // Check if we need to show permission setup again
        if (shouldShowPermissionSetup()) {
            showPermissionSetup()
        } else {
            // Update UI with current status
            updateStatus()

            // Start monitoring if not already running
            startMonitoringIfReady()
        }
    }

    private fun updateStatus() {
        Log.d(TAG, "Updating status...")

        val blockedAppsManager = BlockedAppsManager(this)
        val blockedCount = blockedAppsManager.getBlockedApps().size

        // Check service status
        val usageStatsRunning = UsageStatsMonitorService.isServiceRunning()
        val accessibilityRunning = com.appblock.service.AppBlockAccessibilityService.isServiceRunning()

        // Update status text
        val statusMessage = when {
            accessibilityRunning && usageStatsRunning -> "Status: ✅ Active (Both services running)"
            accessibilityRunning -> "Status: ✅ Active (Accessibility only)"
            usageStatsRunning -> "Status: ⚠️ Partial (Usage stats only)"
            else -> "Status: ❌ Inactive (No services running)"
        }

        statusText.text = statusMessage
        blockedCountText.text = "Blocked apps: $blockedCount"

        Log.d(TAG, "UI Updated - $statusMessage, Blocked: $blockedCount")

        if (blockedCount > 0) {
            Toast.makeText(this, "Currently blocking $blockedCount apps", Toast.LENGTH_SHORT).show()
        }
    }
}
