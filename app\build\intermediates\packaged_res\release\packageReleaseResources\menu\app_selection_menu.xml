<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <item
        android:id="@+id/action_search"
        android:title="@string/action_search"
        android:icon="@drawable/ic_search"
        app:showAsAction="ifRoom|collapseActionView"
        app:actionViewClass="androidx.appcompat.widget.SearchView" />
    
    <item
        android:id="@+id/action_select_all"
        android:title="@string/action_select_all"
        android:icon="@drawable/ic_select_all"
        app:showAsAction="never" />
    
    <item
        android:id="@+id/action_deselect_all"
        android:title="@string/action_deselect_all"
        android:icon="@drawable/ic_deselect_all"
        app:showAsAction="never" />
    
    <item
        android:id="@+id/action_blocked_count"
        android:title="@string/action_blocked_count"
        android:icon="@drawable/ic_info"
        app:showAsAction="never" />
        
</menu>
