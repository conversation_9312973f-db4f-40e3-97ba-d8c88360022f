<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_app_selection" modulePackage="com.appblock" filePath="app\src\main\res\layout\activity_app_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_app_selection_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="91" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="11" startOffset="8" endLine="16" endOffset="66"/></Target><Target id="@+id/btn_show_all" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="35" startOffset="12" endLine="41" endOffset="49"/></Target><Target id="@+id/btn_show_user_apps" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="43" startOffset="12" endLine="49" endOffset="55"/></Target><Target id="@+id/btn_show_system_apps" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="51" startOffset="12" endLine="57" endOffset="57"/></Target><Target id="@+id/tv_app_count" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="69" endOffset="37"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="72" startOffset="8" endLine="78" endOffset="39"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="81" startOffset="8" endLine="87" endOffset="42"/></Target></Targets></Layout>