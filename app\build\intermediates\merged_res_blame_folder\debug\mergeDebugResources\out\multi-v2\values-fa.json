{"logs": [{"outputFile": "com.appblock.app-mergeDebugResources-54:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6034af7fb018680d3c013049f167ec16\\transformed\\preference-1.2.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,258,335,467,636,718", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "167,253,330,462,631,713,791"}, "to": {"startLines": "48,50,105,107,110,111,112", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4447,4577,8854,9009,9324,9493,9575", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "4509,4658,8926,9136,9488,9570,9648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aa3c339c6f6dd0717546ddbb353d969b\\transformed\\navigation-ui-2.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "103,104", "startColumns": "4,4", "startOffsets": "8630,8739", "endColumns": "108,114", "endOffsets": "8734,8849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\94c90974a14a16e04a1c279783b656e5\\transformed\\material-1.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1046,1136,1205,1265,1356,1419,1483,1542,1609,1671,1726,1849,1907,1968,2023,2095,2232,2313,2395,2525,2599,2673,2759,2810,2864,2930,3001,3078,3159,3232,3306,3376,3450,3536,3610,3699,3791,3865,3938,4027,4078,4145,4228,4312,4374,4438,4501,4595,4702,4795,4900,4955,5013", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,85,50,53,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,54,57,77", "endOffsets": "254,329,406,488,581,668,765,894,978,1041,1131,1200,1260,1351,1414,1478,1537,1604,1666,1721,1844,1902,1963,2018,2090,2227,2308,2390,2520,2594,2668,2754,2805,2859,2925,2996,3073,3154,3227,3301,3371,3445,3531,3605,3694,3786,3860,3933,4022,4073,4140,4223,4307,4369,4433,4496,4590,4697,4790,4895,4950,5008,5086"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3074,3151,3233,3326,4137,4234,4363,4514,4663,4753,4822,4882,4973,5036,5100,5159,5226,5288,5343,5466,5524,5585,5640,5712,5849,5930,6012,6142,6216,6290,6376,6427,6481,6547,6618,6695,6776,6849,6923,6993,7067,7153,7227,7316,7408,7482,7555,7644,7695,7762,7845,7929,7991,8055,8118,8212,8319,8412,8517,8572,8931", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,106", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,85,50,53,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,54,57,77", "endOffsets": "304,3069,3146,3228,3321,3408,4229,4358,4442,4572,4748,4817,4877,4968,5031,5095,5154,5221,5283,5338,5461,5519,5580,5635,5707,5844,5925,6007,6137,6211,6285,6371,6422,6476,6542,6613,6690,6771,6844,6918,6988,7062,7148,7222,7311,7403,7477,7550,7639,7690,7757,7840,7924,7986,8050,8113,8207,8314,8407,8512,8567,8625,9004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78c237f2989eef627b8efaab812c395e\\transformed\\core-1.10.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3413,3512,3614,3713,3813,3914,4020,9223", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3507,3609,3708,3808,3909,4015,4132,9319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\01183fe8ec5b70d0914d8fb6bc2ec50c\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,9141", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,9218"}}]}]}