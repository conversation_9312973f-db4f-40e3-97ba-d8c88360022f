<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">App Block</string>
    
    <!-- Main Activity -->
    <string name="permissions">Permissions</string>
    <string name="blocked_apps">Blocked Apps</string>
    <string name="settings">Settings</string>
    <string name="app_blocking">App Blocking</string>
    <string name="setup_pin">Setup PIN</string>
    <string name="device_admin">Device Admin</string>
    
    <!-- App Selection -->
    <string name="select_apps_to_block">Select Apps to Block</string>
    <string name="show_all">All</string>
    <string name="show_user_apps">User</string>
    <string name="show_system_apps">System</string>
    <string name="search_apps">Search apps...</string>
    
    <!-- Block Overlay -->
    <string name="app_blocked_title">App Blocked</string>
    <string name="app_blocked_message">This app is currently blocked by parental controls</string>
    <string name="enter_pin">Enter PIN</string>
    <string name="go_home">Go Home</string>
    <string name="request_time">Request Time</string>

    <!-- Website blocking strings -->
    <string name="blocked_websites">Blocked Websites</string>
    <string name="network_blocking">Network-Level Blocking</string>
    <string name="add_website">Add Website</string>
    <string name="website_hint">Enter website (e.g., facebook.com)</string>
    
    <!-- PIN Authentication -->
    <string name="enter_parent_pin">Enter Parent PIN</string>
    <string name="verify">Verify</string>
    <string name="cancel">Cancel</string>
    <string name="clear">Clear</string>
    
    <!-- Accessibility Service -->
    <string name="accessibility_service_description">App Block uses this service to monitor which apps are opened and block restricted apps by showing an overlay screen. This service does not collect or store any personal data.</string>
    
    <!-- Notifications -->
    <string name="notification_title">App Block Active</string>
    <string name="notification_text">Parental controls are monitoring app usage</string>
    
    <!-- Menu -->
    <string name="action_search">Search</string>
    <string name="action_select_all">Select All</string>
    <string name="action_deselect_all">Deselect All</string>
    <string name="action_blocked_count">Blocked Count</string>
    <string name="action_logs">Logs</string>
    <string name="action_about">About</string>
    
    <!-- Common -->
    <string name="ok">OK</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="enable">Enable</string>
    <string name="disable">Disable</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
</resources>
