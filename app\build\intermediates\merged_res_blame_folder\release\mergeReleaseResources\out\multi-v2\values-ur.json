{"logs": [{"outputFile": "com.appblock.app-mergeReleaseResources-54:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78c237f2989eef627b8efaab812c395e\\transformed\\core-1.10.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3457,3555,3657,3759,3863,3966,4064,9342", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3550,3652,3754,3858,3961,4059,4173,9438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aa3c339c6f6dd0717546ddbb353d969b\\transformed\\navigation-ui-2.6.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,114", "endOffsets": "162,277"}, "to": {"startLines": "103,104", "startColumns": "4,4", "startOffsets": "8732,8844", "endColumns": "111,114", "endOffsets": "8839,8954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6034af7fb018680d3c013049f167ec16\\transformed\\preference-1.2.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,483,652,734", "endColumns": "72,87,77,138,168,81,75", "endOffsets": "173,261,339,478,647,729,805"}, "to": {"startLines": "48,50,105,107,110,111,112", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4480,4618,8959,9117,9443,9612,9694", "endColumns": "72,87,77,138,168,81,75", "endOffsets": "4548,4701,9032,9251,9607,9689,9765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\94c90974a14a16e04a1c279783b656e5\\transformed\\material-1.9.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1047,1140,1210,1269,1359,1423,1492,1550,1619,1679,1743,1855,1914,1973,2028,2103,2226,2306,2390,2523,2605,2686,2773,2831,2887,2953,3028,3108,3193,3260,3335,3412,3476,3570,3640,3729,3822,3896,3971,4061,4117,4184,4268,4352,4414,4478,4541,4641,4748,4842,4951,5013,5073", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,86,57,55,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,61,59,79", "endOffsets": "254,332,410,488,586,675,775,894,977,1042,1135,1205,1264,1354,1418,1487,1545,1614,1674,1738,1850,1909,1968,2023,2098,2221,2301,2385,2518,2600,2681,2768,2826,2882,2948,3023,3103,3188,3255,3330,3407,3471,3565,3635,3724,3817,3891,3966,4056,4112,4179,4263,4347,4409,4473,4536,4636,4743,4837,4946,5008,5068,5148"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3114,3192,3270,3368,4178,4278,4397,4553,4706,4799,4869,4928,5018,5082,5151,5209,5278,5338,5402,5514,5573,5632,5687,5762,5885,5965,6049,6182,6264,6345,6432,6490,6546,6612,6687,6767,6852,6919,6994,7071,7135,7229,7299,7388,7481,7555,7630,7720,7776,7843,7927,8011,8073,8137,8200,8300,8407,8501,8610,8672,9037", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,106", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,86,57,55,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,61,59,79", "endOffsets": "304,3109,3187,3265,3363,3452,4273,4392,4475,4613,4794,4864,4923,5013,5077,5146,5204,5273,5333,5397,5509,5568,5627,5682,5757,5880,5960,6044,6177,6259,6340,6427,6485,6541,6607,6682,6762,6847,6914,6989,7066,7130,7224,7294,7383,7476,7550,7625,7715,7771,7838,7922,8006,8068,8132,8195,8295,8402,8496,8605,8667,8727,9112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\01183fe8ec5b70d0914d8fb6bc2ec50c\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,9256", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,9337"}}]}]}