<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.2" type="baseline" client="gradle" dependencies="false" name="AGP (8.8.2)" variant="all" version="8.8.2">

    <issue
        id="InlinedApi"
        message="Field requires API level 23 (current min is 21): `android.provider.Settings#ACTION_MANAGE_OVERLAY_PERMISSION`"
        errorLine1="            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/utils/PermissionUtils.kt"
            line="87"
            column="13"/>
    </issue>

    <issue
        id="InlinedApi"
        message="Field requires API level 22 (current min is 21): `android.content.Context#USAGE_STATS_SERVICE`"
        errorLine1="        usageStatsManager = getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/service/UsageStatsMonitorService.kt"
            line="53"
            column="46"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="ForegroundServicePermission"
        message="foregroundServiceType:specialUse requires permission:[android.permission.FOREGROUND_SERVICE_SPECIAL_USE]"
        errorLine1="        &lt;service"
        errorLine2="        ^">
        <location
            file="src/main/AndroidManifest.xml"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        message="Permission is only granted to system apps"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.PACKAGE_USAGE_STATS&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="6"
            column="22"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;App Block&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0"
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="42"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1"
        errorLine1="    implementation &apos;androidx.appcompat:appcompat:1.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="43"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.9.0 is available: 1.12.0"
        errorLine1="    implementation &apos;com.google.android.material:material:1.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="44"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        errorLine1="    implementation &apos;androidx.constraintlayout:constraintlayout:2.1.4&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="45"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.6.1 is available: 2.9.1"
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-livedata-ktx:2.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="46"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.1 is available: 2.9.1"
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.6.0 is available: 2.9.1"
        errorLine1="    implementation &apos;androidx.navigation:navigation-fragment-ktx:2.6.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="48"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.6.0 is available: 2.9.1"
        errorLine1="    implementation &apos;androidx.navigation:navigation-ui-ktx:2.6.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="49"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.work:work-runtime-ktx than 2.8.1 is available: 2.10.2"
        errorLine1="    implementation &apos;androidx.work:work-runtime-ktx:2.8.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="50"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="55"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="56"
            column="31"/>
    </issue>

    <issue
        id="SwitchIntDef"
        message="Switch statement on an `int` with known associated constant missing case `AccessibilityEvent.TYPE_ANNOUNCEMENT`, `AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_END`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_START`, `AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED`, `AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_END`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_START`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED`, `AccessibilityEvent.TYPE_VIEW_CLICKED`, `AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED`, `AccessibilityEvent.TYPE_VIEW_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_HOVER_ENTER`, `AccessibilityEvent.TYPE_VIEW_HOVER_EXIT`, `AccessibilityEvent.TYPE_VIEW_LONG_CLICKED`, `AccessibilityEvent.TYPE_VIEW_SCROLLED`, `AccessibilityEvent.TYPE_VIEW_SELECTED`, `AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL`, `AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY`, `AccessibilityEvent.TYPE_WINDOWS_CHANGED`"
        errorLine1="            when (accessibilityEvent.eventType) {"
        errorLine2="            ~~~~">
        <location
            file="src/main/java/com/appblock/service/AppBlockAccessibilityService.kt"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of `scheduleAtFixedRate` is strongly discouraged because it can lead to unexpected behavior when Android processes become cached (tasks may unexpectedly execute hundreds or thousands of times in quick succession when a process changes from cached to uncached); prefer using `scheduleWithFixedDelay`"
        errorLine1="        scheduler.scheduleAtFixedRate("
        errorLine2="        ^">
        <location
            file="src/main/java/com/appblock/service/UsageStatsMonitorService.kt"
            line="81"
            column="9"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/error_red&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/block_overlay.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseCompatTextViewDrawableXml"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`"
        errorLine1="                        android:drawableStart=&quot;@drawable/ic_security&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="100"
            column="25"/>
    </issue>

    <issue
        id="UseCompatTextViewDrawableXml"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`"
        errorLine1="                        android:drawableStart=&quot;@drawable/ic_block&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="139"
            column="25"/>
    </issue>

    <issue
        id="UseCompatTextViewDrawableXml"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`"
        errorLine1="                        android:drawableStart=&quot;@drawable/ic_settings&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="178"
            column="25"/>
    </issue>

    <issue
        id="QueryAllPackagesPermission"
        message="A `&lt;queries>` declaration should generally be used instead of QUERY_ALL_PACKAGES; see https://g.co/dev/packagevisibility for details"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.QUERY_ALL_PACKAGES&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="11"
            column="22"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                appListAdapter.notifyDataSetChanged()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/AppSelectionActivity.kt"
            line="271"
            column="17"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                appListAdapter.notifyDataSetChanged()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/AppSelectionActivity.kt"
            line="295"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 21"
        errorLine1="        &lt;item name=&quot;android:statusBarColor&quot; tools:targetApi=&quot;l&quot;>?attr/colorPrimaryVariant&lt;/item>"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="14"
            column="45"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `AppBlockAccessibilityService` which has field `overlayView` pointing to `View`); this is a memory leak"
        errorLine1="        @Volatile"
        errorLine2="        ^">
        <location
            file="src/main/java/com/appblock/service/AppBlockAccessibilityService.kt"
            line="31"
            column="9"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (907 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="        android:pathData=&quot;M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z&quot; />"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_settings.xml"
            line="10"
            column="27"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/block_overlay_background` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)"
        errorLine1="    android:background=&quot;@color/block_overlay_background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_block_overlay.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)"
        errorLine1="    android:background=&quot;@color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/block_overlay_background` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)"
        errorLine1="    android:background=&quot;@color/block_overlay_background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/block_overlay.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@android:style/Theme.Material.Light`)"
        errorLine1="    android:background=&quot;?android:attr/selectableItemBackground&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_app.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.xml.backup_rules` appears to be unused"
        errorLine1="&lt;full-backup-content>"
        errorLine2="^">
        <location
            file="src/main/res/xml/backup_rules.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.block_overlay` appears to be unused"
        errorLine1="&lt;FrameLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/block_overlay.xml"
            line="3"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_dark&quot;>#FF1976D2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.accent` appears to be unused"
        errorLine1="    &lt;color name=&quot;accent&quot;>#FFFF5722&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.success_green` appears to be unused"
        errorLine1="    &lt;color name=&quot;success_green&quot;>#FF4CAF50&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error_red` appears to be unused"
        errorLine1="    &lt;color name=&quot;error_red&quot;>#FFF44336&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.xml.data_extraction_rules` appears to be unused"
        errorLine1="&lt;data-extraction-rules>"
        errorLine2="^">
        <location
            file="src/main/res/xml/data_extraction_rules.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_monitor` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_monitor.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_shield` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_shield.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_name` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_name&quot;>App Block&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="3"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.select_apps_to_block` appears to be unused"
        errorLine1="    &lt;string name=&quot;select_apps_to_block&quot;>Select Apps to Block&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.search_apps` appears to be unused"
        errorLine1="    &lt;string name=&quot;search_apps&quot;>Search apps...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_blocked_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_blocked_title&quot;>App Blocked&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.notification_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;notification_title&quot;>App Block Active&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.notification_text` appears to be unused"
        errorLine1="    &lt;string name=&quot;notification_text&quot;>Parental controls are monitoring app usage&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.ok` appears to be unused"
        errorLine1="    &lt;string name=&quot;ok&quot;>OK&lt;/string>"
        errorLine2="            ~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.yes` appears to be unused"
        errorLine1="    &lt;string name=&quot;yes&quot;>Yes&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no` appears to be unused"
        errorLine1="    &lt;string name=&quot;no&quot;>No&lt;/string>"
        errorLine2="            ~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.enable` appears to be unused"
        errorLine1="    &lt;string name=&quot;enable&quot;>Enable&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.disable` appears to be unused"
        errorLine1="    &lt;string name=&quot;disable&quot;>Disable&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.save` appears to be unused"
        errorLine1="    &lt;string name=&quot;save&quot;>Save&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.delete` appears to be unused"
        errorLine1="    &lt;string name=&quot;delete&quot;>Delete&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_AppBlock` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.AppBlock&quot; parent=&quot;Theme.Material3.DayNight&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_AppBlock_Fullscreen` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.AppBlock.Fullscreen&quot; parent=&quot;Theme.AppBlock&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_AppBlock_Dialog` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.AppBlock.Dialog&quot; parent=&quot;Theme.Material3.DayNight.Dialog&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;search_apps&quot;>Search apps...&lt;/string>"
        errorLine2="                               ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="32"/>
    </issue>

    <issue
        id="MissingApplicationIcon"
        message="Should explicitly set `android:icon`, there is no default"
        errorLine1="    &lt;application"
        errorLine2="     ~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="14"
            column="6"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `11sp`: `10sp`"
        errorLine1="                android:textSize=&quot;10sp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_app.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        errorLine1="        overlayView.setOnTouchListener { _, _ -> true }"
        errorLine2="                                       ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/service/AppBlockAccessibilityService.kt"
            line="169"
            column="40"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_block_overlay.xml"
            line="20"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/block_overlay.xml"
            line="21"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_app.xml"
            line="12"
            column="6"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    tvSystemApp.text = &quot;SYSTEM&quot;"
        errorLine2="                                        ~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/adapter/AppListAdapter.kt"
            line="36"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.tvAppCount.text = &quot;${filteredApps.size} apps&quot;"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/AppSelectionActivity.kt"
            line="154"
            column="35"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvAppCount.text = &quot;${filteredApps.size} apps&quot;"
        errorLine2="                                                       ~~~~~">
        <location
            file="src/main/java/com/appblock/ui/AppSelectionActivity.kt"
            line="154"
            column="56"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                    binding.tvRemainingTime.text = &quot;Remaining time today: $minutes minutes&quot;"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="111"
            column="52"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    binding.tvRemainingTime.text = &quot;Remaining time today: $minutes minutes&quot;"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="111"
            column="53"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    binding.tvRemainingTime.text = &quot;Remaining time today: $minutes minutes&quot;"
        errorLine2="                                                                                  ~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="111"
            column="83"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    binding.tvMessage.text = &quot;This app is currently blocked&quot;"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="115"
            column="47"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvMessage.text = &quot;This app is currently blocked&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="123"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.tvSource.text = &quot;Detected by: $source&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="128"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvSource.text = &quot;Detected by: $source&quot;"
        errorLine2="                                 ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/BlockOverlayActivity.kt"
            line="128"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            text = &quot;App Block - Parental Control&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/MainActivity.kt"
            line="49"
            column="21"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            text = &quot;Select Apps to Block&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/MainActivity.kt"
            line="58"
            column="21"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            text = &quot;Status: Checking...&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/MainActivity.kt"
            line="69"
            column="21"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            text = &quot;Blocked apps: 0&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/MainActivity.kt"
            line="77"
            column="21"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.tvBlockedAppsCount.text = &quot;$blockedAppsCount apps blocked&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/MainActivity.kt"
            line="303"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.tvBlockedAppsCount.text = &quot;$blockedAppsCount apps blocked&quot;"
        errorLine2="                                                            ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/MainActivity.kt"
            line="303"
            column="61"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                    binding.etPin.setText(currentText + index)"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="88"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvTitle.text = &quot;Enter Parent PIN&quot;"
        errorLine2="                                        ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="110"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvMessage.text = &quot;Enter your PIN to temporarily allow this app&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="111"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvTitle.text = &quot;Request More Time&quot;"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="114"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvMessage.text = &quot;Enter parent PIN to request additional time&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="115"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvTitle.text = &quot;Enter Parent PIN&quot;"
        errorLine2="                                        ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="118"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvMessage.text = &quot;PIN required to continue&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="119"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                binding.tvAppName.text = &quot;App: $appName&quot;"
        errorLine2="                                         ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="128"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                binding.tvAppName.text = &quot;App: $appName&quot;"
        errorLine2="                                          ~~~~~">
        <location
            file="src/main/java/com/appblock/ui/PinAuthActivity.kt"
            line="128"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            text = &quot;Select Apps to Block&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/appblock/ui/SimpleAppSelectionActivity.kt"
            line="53"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;0 apps&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;0 apps&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_app_selection.xml"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;App Name&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;App Name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_block_overlay.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Detected by: unknown&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Detected by: unknown&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_block_overlay.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Enable parental controls&quot;, should use `@string` resource"
        errorLine1="                            android:text=&quot;Enable parental controls&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="64"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Checking permissions...&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Checking permissions...&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="111"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;0 apps blocked&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;0 apps blocked&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="150"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Configure app settings&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Configure app settings&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="188"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Setup&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Setup&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="202"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Enter your PIN to continue&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Enter your PIN to continue&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="28"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Enter PIN&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Enter PIN&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;1&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;1&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="79"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;2&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;2&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="84"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;3&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;3&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="89"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;4&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;4&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;5&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;5&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="107"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;6&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;6&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="112"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;7&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;7&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="125"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;8&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;8&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="130"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;9&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;9&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="135"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;0&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="153"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;⌫&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;⌫&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_pin_auth.xml"
            line="158"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Enter PIN (4-6 digits)&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Enter PIN (4-6 digits)&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_pin_setup.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Confirm PIN&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Confirm PIN&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_pin_setup.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Enter PIN&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Enter PIN&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_pin_verify.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;App Name&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;App Name&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_app.xml"
            line="38"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;SYSTEM&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;SYSTEM&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_app.xml"
            line="49"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;com.example.app&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;com.example.app&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_app.xml"
            line="61"
            column="13"/>
    </issue>

</issues>
