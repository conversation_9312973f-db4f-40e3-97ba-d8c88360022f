<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:orientation="horizontal"
    android:padding="16dp">

    <!-- App Icon -->
    <ImageView
        android:id="@+id/iv_app_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_android" />

    <!-- App Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_app_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="App Name"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_system_app"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/system_app_badge"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="SYSTEM"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_package_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="com.example.app"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- Checkbox -->
    <CheckBox
        android:id="@+id/checkbox_blocked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="8dp" />

</LinearLayout>
