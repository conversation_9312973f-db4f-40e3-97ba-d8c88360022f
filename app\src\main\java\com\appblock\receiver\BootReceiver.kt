package com.appblock.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.appblock.data.SettingsManager
import com.appblock.service.UsageStatsMonitorService
import com.appblock.utils.PermissionUtils

class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.d(TAG, "Received broadcast: $action")

        when (action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                handleBootCompleted(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                if (intent.dataString?.contains(context.packageName) == true) {
                    handleAppUpdated(context)
                }
            }
        }
    }

    private fun handleBootCompleted(context: Context) {
        Log.d(TAG, "Device boot completed - starting protection services")
        
        val settingsManager = SettingsManager(context)
        
        // Only start services if app blocking is enabled
        if (!settingsManager.isAppBlockingEnabled()) {
            Log.d(TAG, "App blocking disabled - not starting services")
            return
        }
        
        // Record boot event
        settingsManager.recordBootEvent(System.currentTimeMillis())
        
        // Start monitoring services with delay to ensure system is ready
        startProtectionServicesDelayed(context)
    }

    private fun handleAppUpdated(context: Context) {
        Log.d(TAG, "App updated - restarting protection services")
        startProtectionServicesDelayed(context)
    }

    private fun startProtectionServicesDelayed(context: Context) {
        // Use a separate thread to avoid blocking the broadcast receiver
        Thread {
            try {
                // Small delay to ensure system is ready
                Thread.sleep(5000)
                
                startProtectionServices(context)
                
            } catch (e: InterruptedException) {
                Log.e(TAG, "Interrupted while starting services", e)
            }
        }.start()
    }

    private fun startProtectionServices(context: Context) {
        try {
            // Start UsageStats monitoring service
            if (PermissionUtils.hasUsageStatsPermission(context)) {
                val usageStatsIntent = Intent(context, UsageStatsMonitorService::class.java)
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(usageStatsIntent)
                } else {
                    context.startService(usageStatsIntent)
                }
                
                Log.d(TAG, "Started UsageStatsMonitorService")
            } else {
                Log.w(TAG, "Usage stats permission not available")
            }
            
            // Check accessibility service status
            if (!PermissionUtils.isAccessibilityServiceEnabled(context)) {
                Log.w(TAG, "Accessibility service not enabled")
                // Could send notification to re-enable
            }
            
            // Verify device admin status
            if (!AppBlockDeviceAdminReceiver.isDeviceAdminActive(context)) {
                Log.w(TAG, "Device admin not active")
                // Could send notification to re-enable
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error starting protection services", e)
        }
    }
}
