<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.2" type="conditional_incidents">

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffe00000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/AppBlockAccessibilityService.kt"
            line="327"
            column="24"
            startOffset="12914"
            endLine="327"
            endColumn="77"
            endOffset="12967"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 22 (current min is %1$s): `android.view.WindowManager.LayoutParams#TYPE_ACCESSIBILITY_OVERLAY`"/>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <entry
                name="name"
                string="TYPE_ACCESSIBILITY_OVERLAY"/>
            <entry
                name="owner"
                string="android.view.WindowManager.LayoutParams"/>
            <api-levels id="requiresApi"
                value="ffffffffffe00000"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/utils/PermissionUtils.kt"
            line="87"
            column="13"
            startOffset="2658"
            endLine="87"
            endColumn="54"
            endOffset="2699"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 23 (current min is %1$s): `android.provider.Settings#ACTION_MANAGE_OVERLAY_PERMISSION`"/>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <entry
                name="name"
                string="ACTION_MANAGE_OVERLAY_PERMISSION"/>
            <entry
                name="owner"
                string="android.provider.Settings"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffe00000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/UsageStatsMonitorService.kt"
            line="53"
            column="46"
            startOffset="1784"
            endLine="53"
            endColumn="73"
            endOffset="1811"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 22 (current min is %1$s): `android.content.Context#USAGE_STATS_SERVICE`"/>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <entry
                name="name"
                string="USAGE_STATS_SERVICE"/>
            <entry
                name="owner"
                string="android.content.Context"/>
            <api-levels id="requiresApi"
                value="ffffffffffe00000"/>
        </map>
    </incident>

    <incident
        id="ForegroundServicePermission"
        severity="error"
        message="foregroundServiceType:specialUse requires permission:[android.permission.FOREGROUND_SERVICE_SPECIAL_USE]">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="59"
            column="9"
            startOffset="2453"
            endLine="63"
            endColumn="58"
            endOffset="2656"/>
        <map>
            <condition targetGE="34"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/AppSelectionActivity.kt"
            line="106"
            column="44"
            startOffset="3574"
            endLine="106"
            endColumn="68"
            endOffset="3598"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/ui/SimpleAppSelectionActivity.kt"
            line="102"
            column="44"
            startOffset="2959"
            endLine="102"
            endColumn="68"
            endOffset="2983"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_access_time.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_access_time.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_admin_panel_settings.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_admin_panel_settings.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_android.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="37"
            endOffset="256"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_android.xml"
            line="9"
            column="28"
            startOffset="296"
            endLine="9"
            endColumn="48"
            endOffset="316"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_block.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="37"
            endOffset="256"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_block.xml"
            line="9"
            column="28"
            startOffset="296"
            endLine="9"
            endColumn="48"
            endOffset="316"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_deselect_all.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_deselect_all.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_list.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_list.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_lock.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_lock.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_monitor.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_monitor.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_security.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="37"
            endOffset="256"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_security.xml"
            line="9"
            column="28"
            startOffset="296"
            endLine="9"
            endColumn="48"
            endOffset="316"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_select_all.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_select_all.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="37"
            endOffset="256"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="28"
            startOffset="296"
            endLine="9"
            endColumn="48"
            endOffset="316"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_shield.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_shield.xml"
            line="9"
            column="28"
            startOffset="298"
            endLine="9"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of `scheduleAtFixedRate` is strongly discouraged because it can lead to unexpected behavior when Android processes become cached (tasks may unexpectedly execute hundreds or thousands of times in quick succession when a process changes from cached to uncached); prefer using `scheduleWithFixedDelay`">
        <fix-replace
            description="Replace with scheduleWithFixedDelay"
            independent="true"
            oldString="scheduleAtFixedRate"
            replacement="scheduleWithFixedDelay"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/appblock/service/UsageStatsMonitorService.kt"
            line="81"
            column="9"
            startOffset="2698"
            endLine="86"
            endColumn="10"
            endOffset="2860"/>
        <map>
            <condition android="true"/>
        </map>
    </incident>

    <incident
        id="QueryAllPackagesPermission"
        severity="error"
        message="A `&lt;queries>` declaration should generally be used instead of QUERY_ALL_PACKAGES; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="11"
            column="22"
            startOffset="629"
            endLine="11"
            endColumn="74"
            endOffset="681"/>
        <map>
            <condition targetGE="30"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="952"
                endOffset="972"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="19"
            column="9"
            startOffset="952"
            endLine="19"
            endColumn="29"
            endOffset="972"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="723"
                endOffset="742"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="14"
            column="45"
            startOffset="723"
            endLine="14"
            endColumn="64"
            endOffset="742"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

</incidents>
