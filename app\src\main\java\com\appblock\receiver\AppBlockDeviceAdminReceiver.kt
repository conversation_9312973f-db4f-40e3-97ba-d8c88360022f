package com.appblock.receiver

import android.app.admin.DeviceAdminReceiver
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import com.appblock.data.SettingsManager

class AppBlockDeviceAdminReceiver : DeviceAdminReceiver() {

    companion object {
        private const val TAG = "DeviceAdminReceiver"

        fun getComponentName(context: Context): ComponentName {
            return ComponentName(context, AppBlockDeviceAdminReceiver::class.java)
        }

        fun isDeviceAdminActive(context: Context): Boolean {
            val devicePolicyManager = context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            return devicePolicyManager.isAdminActive(getComponentName(context))
        }

        fun requestDeviceAdminActivation(context: Context): Intent {
            val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN)
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, getComponentName(context))
            intent.putExtra(
                DevicePolicyManager.EXTRA_ADD_EXPLANATION,
                "Enable device administrator to prevent unauthorized app uninstallation and enhance parental controls security."
            )
            return intent
        }
    }

    override fun onEnabled(context: Context, intent: Intent) {
        super.onEnabled(context, intent)
        Log.d(TAG, "Device admin enabled")

        Toast.makeText(
            context,
            "Device administrator enabled. App is now protected against unauthorized removal.",
            Toast.LENGTH_LONG
        ).show()

        // Update settings to reflect admin status
        val settingsManager = SettingsManager(context)
        settingsManager.setDeviceAdminEnabled(true)
    }

    override fun onDisabled(context: Context, intent: Intent) {
        super.onDisabled(context, intent)
        Log.w(TAG, "Device admin disabled - potential tampering attempt!")

        // Update settings
        val settingsManager = SettingsManager(context)
        settingsManager.setDeviceAdminEnabled(false)

        // Immediate response to tampering attempt
        handleTamperingAttempt(context)
    }

    override fun onDisableRequested(context: Context, intent: Intent): CharSequence {
        Log.w(TAG, "Device admin disable requested")

        // Return a warning message to the user
        return "Disabling device administrator will reduce parental control security. " +
               "Are you sure you want to continue? This action will be logged."
    }

    private fun handleTamperingAttempt(context: Context) {
        Log.w(TAG, "Handling tampering attempt")

        val settingsManager = SettingsManager(context)

        // Record the tampering attempt
        settingsManager.recordTamperingAttempt(System.currentTimeMillis())

        // Check if we should lock the device
        if (settingsManager.shouldLockOnTampering()) {
            lockDevice(context)
        }

        // Send notification to parent (if configured)
        sendTamperingNotification(context)

        // Try to restart protection services
        restartProtectionServices(context)
    }

    private fun lockDevice(context: Context) {
        try {
            val devicePolicyManager = context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            val adminComponent = getComponentName(context)

            if (devicePolicyManager.isAdminActive(adminComponent)) {
                Log.d(TAG, "Locking device due to tampering attempt")
                devicePolicyManager.lockNow()
            } else {
                Log.w(TAG, "Cannot lock device - admin not active")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error locking device", e)
        }
    }

    private fun sendTamperingNotification(context: Context) {
        // This could send an email, SMS, or push notification to parent
        // For now, we'll just log it
        Log.w(TAG, "Tampering attempt detected and logged")

        // Could integrate with notification service here
        // NotificationHelper.sendTamperingAlert(context)
    }

    private fun restartProtectionServices(context: Context) {
        try {
            // Try to restart the monitoring services
            val serviceIntent = Intent(context, com.appblock.service.UsageStatsMonitorService::class.java)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            Log.d(TAG, "Attempted to restart protection services")
        } catch (e: Exception) {
            Log.e(TAG, "Error restarting protection services", e)
        }
    }

    override fun onPasswordChanged(context: Context, intent: Intent, user: android.os.UserHandle) {
        super.onPasswordChanged(context, intent, user)
        Log.d(TAG, "Device password changed")
    }

    override fun onPasswordFailed(context: Context, intent: Intent, user: android.os.UserHandle) {
        super.onPasswordFailed(context, intent, user)
        Log.d(TAG, "Device password failed")
    }

    override fun onPasswordSucceeded(context: Context, intent: Intent, user: android.os.UserHandle) {
        super.onPasswordSucceeded(context, intent, user)
        Log.d(TAG, "Device password succeeded")
    }
}
