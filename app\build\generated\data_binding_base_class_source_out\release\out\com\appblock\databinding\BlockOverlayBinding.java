// Generated by view binder compiler. Do not edit!
package com.appblock.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.appblock.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BlockOverlayBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final MaterialButton btnEnterPin;

  @NonNull
  public final MaterialButton btnGoHome;

  private BlockOverlayBinding(@NonNull FrameLayout rootView, @NonNull MaterialButton btnEnterPin,
      @NonNull MaterialButton btnGoHome) {
    this.rootView = rootView;
    this.btnEnterPin = btnEnterPin;
    this.btnGoHome = btnGoHome;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BlockOverlayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BlockOverlayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.block_overlay, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BlockOverlayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_enter_pin;
      MaterialButton btnEnterPin = ViewBindings.findChildViewById(rootView, id);
      if (btnEnterPin == null) {
        break missingId;
      }

      id = R.id.btn_go_home;
      MaterialButton btnGoHome = ViewBindings.findChildViewById(rootView, id);
      if (btnGoHome == null) {
        break missingId;
      }

      return new BlockOverlayBinding((FrameLayout) rootView, btnEnterPin, btnGoHome);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
