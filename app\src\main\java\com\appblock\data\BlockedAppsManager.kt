package com.appblock.data

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.concurrent.ConcurrentHashMap

class BlockedAppsManager(private val context: Context) {

    companion object {
        private const val TAG = "BlockedAppsManager"
        private const val PREFS_NAME = "blocked_apps"
        private const val KEY_BLOCKED_APPS = "blocked_apps_list"
        private const val KEY_TEMPORARY_ALLOWS = "temporary_allows"
        private const val KEY_TIME_LIMITS = "time_limits"
        private const val KEY_SETTINGS_BLOCKED = "settings_blocked"
        private const val KEY_BLOCKED_WEBSITES = "blocked_websites_list"
        private const val KEY_WEBSITE_TEMPORARY_ALLOWS = "website_temporary_allows"
        // VPN service control
        private const val KEY_VPN_ENABLED = "vpn_enabled"

        fun getCurrentDateStatic(): String {
            return java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                .format(java.util.Date())
        }
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    // Cache for performance
    private val blockedAppsCache = ConcurrentHashMap<String, Boolean>()
    private val temporaryAllowsCache = ConcurrentHashMap<String, Long>()
    private val timeLimitsCache = ConcurrentHashMap<String, TimeLimitInfo>()
    // Website blocking cache
    private val blockedWebsitesCache = ConcurrentHashMap<String, Boolean>()
    private val websiteTemporaryAllowsCache = ConcurrentHashMap<String, Long>()

    init {
        loadCaches()
    }

    data class TimeLimitInfo(
        val dailyLimitMs: Long,
        val usedTodayMs: Long = 0L,
        val lastResetDate: String = getCurrentDateStatic()
    )

    private fun loadCaches() {
        try {
            // Load blocked apps
            val blockedAppsJson = prefs.getString(KEY_BLOCKED_APPS, "[]")
            val blockedAppsList: List<String> = gson.fromJson(
                blockedAppsJson,
                object : TypeToken<List<String>>() {}.type
            ) ?: emptyList()

            blockedAppsCache.clear()
            blockedAppsList.forEach { packageName ->
                blockedAppsCache[packageName] = true
            }

            // Load temporary allows
            val temporaryAllowsJson = prefs.getString(KEY_TEMPORARY_ALLOWS, "{}")
            val temporaryAllowsMap: Map<String, Long> = gson.fromJson(
                temporaryAllowsJson,
                object : TypeToken<Map<String, Long>>() {}.type
            ) ?: emptyMap()

            temporaryAllowsCache.clear()
            temporaryAllowsCache.putAll(temporaryAllowsMap)

            // Load time limits
            val timeLimitsJson = prefs.getString(KEY_TIME_LIMITS, "{}")
            val timeLimitsMap: Map<String, TimeLimitInfo> = gson.fromJson(
                timeLimitsJson,
                object : TypeToken<Map<String, TimeLimitInfo>>() {}.type
            ) ?: emptyMap()

            timeLimitsCache.clear()
            timeLimitsCache.putAll(timeLimitsMap)

            Log.d(TAG, "Loaded ${blockedAppsCache.size} blocked apps, ${temporaryAllowsCache.size} temporary allows")

        } catch (e: Exception) {
            Log.e(TAG, "Error loading caches", e)
        }
        loadWebsiteBlockingData()
    }

    fun isAppBlocked(packageName: String): Boolean {
        // HARDCODED TEST: Block expenditure tracker specifically
        if (packageName == "com,pinterest") {
            Log.d(TAG, "🔴 HARDCODED BLOCKING: $packageName")
            return true
        }

        // Check if temporarily allowed
        val allowedUntil = temporaryAllowsCache[packageName]
        if (allowedUntil != null && System.currentTimeMillis() < allowedUntil) {
            return false
        } else if (allowedUntil != null) {
            // Temporary allow expired, remove it
            temporaryAllowsCache.remove(packageName)
            saveTemporaryAllows()
        }

        // Simple check: if app is in blocked list, it's blocked. Period.
        val isBlocked = blockedAppsCache[packageName] == true
        Log.d(TAG, "📋 Checking blocked status for $packageName: $isBlocked")
        return isBlocked
    }

    private fun isAppOverTimeLimit(packageName: String): Boolean {
        val timeLimitInfo = timeLimitsCache[packageName] ?: return true // Block if no time limit set

        // Check if we need to reset daily usage (new day)
        val currentDate = getCurrentDate()
        if (timeLimitInfo.lastResetDate != currentDate) {
            // Reset usage for new day
            val resetInfo = timeLimitInfo.copy(
                usedTodayMs = 0L,
                lastResetDate = currentDate
            )
            timeLimitsCache[packageName] = resetInfo
            saveTimeLimits()
            return false // Allow usage for new day
        }

        // Check if over daily limit
        return timeLimitInfo.usedTodayMs >= timeLimitInfo.dailyLimitMs
    }

    fun addBlockedApp(packageName: String) {
        blockedAppsCache[packageName] = true
        saveBlockedApps()
        Log.d(TAG, "Added blocked app: $packageName")
    }

    fun removeBlockedApp(packageName: String) {
        blockedAppsCache.remove(packageName)
        timeLimitsCache.remove(packageName)
        temporaryAllowsCache.remove(packageName)

        saveBlockedApps()
        saveTimeLimits()
        saveTemporaryAllows()

        Log.d(TAG, "Removed blocked app: $packageName")
    }

    fun getBlockedApps(): List<String> {
        return blockedAppsCache.keys.toList()
    }

    fun temporarilyAllowApp(packageName: String, durationMs: Long) {
        val allowedUntil = System.currentTimeMillis() + durationMs
        temporaryAllowsCache[packageName] = allowedUntil
        saveTemporaryAllows()

        Log.d(TAG, "Temporarily allowed $packageName for ${durationMs / 1000} seconds")
    }

    fun setTimeLimit(packageName: String, dailyLimitMs: Long) {
        val currentInfo = timeLimitsCache[packageName]
        val newInfo = TimeLimitInfo(
            dailyLimitMs = dailyLimitMs,
            usedTodayMs = currentInfo?.usedTodayMs ?: 0L,
            lastResetDate = currentInfo?.lastResetDate ?: getCurrentDate()
        )

        timeLimitsCache[packageName] = newInfo
        saveTimeLimits()

        Log.d(TAG, "Set time limit for $packageName: ${dailyLimitMs / (1000 * 60)} minutes")
    }

    fun addUsageTime(packageName: String, usageTimeMs: Long) {
        val currentInfo = timeLimitsCache[packageName] ?: return
        val currentDate = getCurrentDate()

        val updatedInfo = if (currentInfo.lastResetDate != currentDate) {
            // New day, reset usage
            currentInfo.copy(
                usedTodayMs = usageTimeMs,
                lastResetDate = currentDate
            )
        } else {
            // Same day, add usage
            currentInfo.copy(
                usedTodayMs = currentInfo.usedTodayMs + usageTimeMs
            )
        }

        timeLimitsCache[packageName] = updatedInfo
        saveTimeLimits()
    }

    fun getRemainingTime(packageName: String): Long {
        val timeLimitInfo = timeLimitsCache[packageName] ?: return 0L
        return maxOf(0L, timeLimitInfo.dailyLimitMs - timeLimitInfo.usedTodayMs)
    }

    fun isSettingsBlocked(): Boolean {
        return prefs.getBoolean(KEY_SETTINGS_BLOCKED, false)
    }

    fun setSettingsBlocked(blocked: Boolean) {
        prefs.edit().putBoolean(KEY_SETTINGS_BLOCKED, blocked).apply()
        Log.d(TAG, "Settings blocking: $blocked")
    }

    private fun saveBlockedApps() {
        val blockedAppsList = blockedAppsCache.keys.toList()
        val json = gson.toJson(blockedAppsList)
        prefs.edit().putString(KEY_BLOCKED_APPS, json).apply()
    }

    private fun saveTemporaryAllows() {
        // Clean expired allows before saving
        val currentTime = System.currentTimeMillis()
        val validAllows = temporaryAllowsCache.filterValues { it > currentTime }
        temporaryAllowsCache.clear()
        temporaryAllowsCache.putAll(validAllows)

        val json = gson.toJson(temporaryAllowsCache.toMap())
        prefs.edit().putString(KEY_TEMPORARY_ALLOWS, json).apply()
    }

    private fun saveTimeLimits() {
        val json = gson.toJson(timeLimitsCache.toMap())
        prefs.edit().putString(KEY_TIME_LIMITS, json).apply()
    }

    private fun getCurrentDate(): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
            .format(java.util.Date())
    }

    fun clearAllData() {
        blockedAppsCache.clear()
        temporaryAllowsCache.clear()
        timeLimitsCache.clear()
        blockedWebsitesCache.clear()
        websiteTemporaryAllowsCache.clear()

        prefs.edit().clear().apply()
        Log.d(TAG, "Cleared all blocked apps data")
    }

    // ==================== WEBSITE BLOCKING METHODS ====================

    // Check if a website/URL should be blocked
    fun isWebsiteBlocked(url: String): Boolean {
        if (url.isBlank()) return false
        
        // Check if temporarily allowed
        val domain = extractDomain(url)
        val allowedUntil = websiteTemporaryAllowsCache[domain]
        if (allowedUntil != null && System.currentTimeMillis() < allowedUntil) {
            return false
        } else if (allowedUntil != null) {
            // Temporary allow expired, remove it
            websiteTemporaryAllowsCache.remove(domain)
            saveWebsiteTemporaryAllows()
        }
        
        // Check against blocked websites patterns
        for (blockedPattern in blockedWebsitesCache.keys) {
            if (matchesPattern(url, blockedPattern)) {
                Log.d(TAG, "🌐 Website blocked: $url matches pattern: $blockedPattern")
                return true
            }
        }
        
        return false
    }

     // Add a website/domain to blocked list
    fun addBlockedWebsite(pattern: String) {
        val cleanPattern = cleanWebsitePattern(pattern)
        blockedWebsitesCache[cleanPattern] = true
        saveBlockedWebsites()
        Log.d(TAG, "Added blocked website: $cleanPattern")
    }


    // Remove a website/domain from blocked list
    fun removeBlockedWebsite(pattern: String) {
        val cleanPattern = cleanWebsitePattern(pattern)
        blockedWebsitesCache.remove(cleanPattern)
        websiteTemporaryAllowsCache.remove(cleanPattern)
        
        saveBlockedWebsites()
        saveWebsiteTemporaryAllows()
        
        Log.d(TAG, "Removed blocked website: $cleanPattern")
    }

    // Get list of all blocked websites
    fun getBlockedWebsites(): List<String> {
        return blockedWebsitesCache.keys.toList()
    }

    // Temporarily allow a website
    fun temporarilyAllowWebsite(url: String, durationMs: Long) {
        val domain = extractDomain(url)
        val allowedUntil = System.currentTimeMillis() + durationMs
        websiteTemporaryAllowsCache[domain] = allowedUntil
        saveWebsiteTemporaryAllows()
        
        Log.d(TAG, "Temporarily allowed website $domain for ${durationMs / 1000} seconds")
    }

    // Extract domain from URL
    private fun extractDomain(url: String): String {
        return try {
            val cleanUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
                "https://$url"
            } else {
                url
            }
            
            val uri = java.net.URI(cleanUrl)
            uri.host?.lowercase() ?: url.lowercase()
        } catch (e: Exception) {
            // If URL parsing fails, use the original string
            url.lowercase()
        }
    }

    // Clean and normalize website pattern
    private fun cleanWebsitePattern(pattern: String): String {
        var cleaned = pattern.trim().lowercase()
        
        // Remove protocol if present
        cleaned = cleaned.removePrefix("https://")
        cleaned = cleaned.removePrefix("http://")
        cleaned = cleaned.removePrefix("www.")
        
        // Remove trailing slash
        cleaned = cleaned.removeSuffix("/")
        
        return cleaned
    }

    // Check if URL matches a blocking pattern
    private fun matchesPattern(url: String, pattern: String): Boolean {
        val domain = extractDomain(url)
        val cleanUrl = url.lowercase()
        
        return when {
            // Exact domain match
            domain == pattern -> true
            
            // Subdomain match (e.g., pattern "facebook.com" blocks "m.facebook.com")
            domain.endsWith(".$pattern") -> true
            
            // URL contains pattern
            cleanUrl.contains(pattern) -> true
            
            // Wildcard support (basic)
            pattern.contains("*") -> {
                val regex = pattern.replace("*", ".*").toRegex()
                regex.matches(domain) || regex.matches(cleanUrl)
            }
            
            else -> false
        }
    }

    // Load website blocking data from SharedPreferences
    private fun loadWebsiteBlockingData() {
        try {
            // Load blocked websites
            val blockedWebsitesJson = prefs.getString(KEY_BLOCKED_WEBSITES, "[]")
            val blockedWebsitesList: List<String> = gson.fromJson(
                blockedWebsitesJson,
                object : TypeToken<List<String>>() {}.type
            ) ?: emptyList()
            
            blockedWebsitesCache.clear()
            blockedWebsitesList.forEach { pattern ->
                blockedWebsitesCache[pattern] = true
            }
            
            // Load website temporary allows
            val websiteTemporaryAllowsJson = prefs.getString(KEY_WEBSITE_TEMPORARY_ALLOWS, "{}")
            val websiteTemporaryAllowsMap: Map<String, Long> = gson.fromJson(
                websiteTemporaryAllowsJson,
                object : TypeToken<Map<String, Long>>() {}.type
            ) ?: emptyMap()
            
            websiteTemporaryAllowsCache.clear()
            websiteTemporaryAllowsCache.putAll(websiteTemporaryAllowsMap)
            
            Log.d(TAG, "Loaded ${blockedWebsitesCache.size} blocked websites, ${websiteTemporaryAllowsCache.size} website temporary allows")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading website blocking data", e)
        }
    }

    // Save blocked websites to SharedPreferences
    private fun saveBlockedWebsites() {
        val blockedWebsitesList = blockedWebsitesCache.keys.toList()
        val json = gson.toJson(blockedWebsitesList)
        prefs.edit().putString(KEY_BLOCKED_WEBSITES, json).apply()
    }

    // Save website temporary allows to SharedPreferences
    private fun saveWebsiteTemporaryAllows() {
        // Clean expired allows before saving
        val currentTime = System.currentTimeMillis()
        val validAllows = websiteTemporaryAllowsCache.filterValues { it > currentTime }
        websiteTemporaryAllowsCache.clear()
        websiteTemporaryAllowsCache.putAll(validAllows)
        
        val json = gson.toJson(websiteTemporaryAllowsCache.toMap())
        prefs.edit().putString(KEY_WEBSITE_TEMPORARY_ALLOWS, json).apply()
    }

    // ==================== VPN SERVICE CONTROL ====================

    /**
     * Check if VPN-based blocking is enabled
     */
    fun isVpnBlockingEnabled(): Boolean {
        return prefs.getBoolean(KEY_VPN_ENABLED, false)
    }

    /**
     * Enable or disable VPN-based blocking
     */
    fun setVpnBlockingEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_VPN_ENABLED, enabled).apply()
        Log.d(TAG, "VPN blocking enabled: $enabled")
        
        // Start or stop VPN service based on setting
        if (enabled) {
            com.appblock.service.WebBlockVpnService.startVpnService(context)
        } else {
            com.appblock.service.WebBlockVpnService.stopVpnService(context)
        }
    }

    /**
     * Check if VPN service is currently running
     */
    fun isVpnServiceRunning(): Boolean {
        return com.appblock.service.WebBlockVpnService.isServiceRunning()
    }
}
