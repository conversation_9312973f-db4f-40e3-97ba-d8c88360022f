<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="8dp"
        android:text="@string/enter_parent_pin"
        android:textSize="24sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="Enter your PIN to continue"
        android:textSize="16sp" />

    <!-- App Name (if applicable) -->
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:textColor="@color/primary"
        android:textSize="14sp"
        android:visibility="gone" />

    <!-- PIN Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_pin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:hint="Enter PIN"
            android:inputType="numberPassword"
            android:maxLength="6"
            android:textSize="18sp"
            android:textStyle="bold" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Number Pad -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:orientation="vertical">

        <!-- Row 1: 1, 2, 3 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_1"
                style="@style/PinButtonStyle"
                android:text="1" />

            <Button
                android:id="@+id/btn_2"
                style="@style/PinButtonStyle"
                android:text="2" />

            <Button
                android:id="@+id/btn_3"
                style="@style/PinButtonStyle"
                android:text="3" />

        </LinearLayout>

        <!-- Row 2: 4, 5, 6 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_4"
                style="@style/PinButtonStyle"
                android:text="4" />

            <Button
                android:id="@+id/btn_5"
                style="@style/PinButtonStyle"
                android:text="5" />

            <Button
                android:id="@+id/btn_6"
                style="@style/PinButtonStyle"
                android:text="6" />

        </LinearLayout>

        <!-- Row 3: 7, 8, 9 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_7"
                style="@style/PinButtonStyle"
                android:text="7" />

            <Button
                android:id="@+id/btn_8"
                style="@style/PinButtonStyle"
                android:text="8" />

            <Button
                android:id="@+id/btn_9"
                style="@style/PinButtonStyle"
                android:text="9" />

        </LinearLayout>

        <!-- Row 4: Clear, 0, Backspace -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_clear"
                style="@style/PinButtonStyle"
                android:text="@string/clear" />

            <Button
                android:id="@+id/btn_0"
                style="@style/PinButtonStyle"
                android:text="0" />

            <Button
                android:id="@+id/btn_backspace"
                style="@style/PinButtonStyle"
                android:text="⌫" />

        </LinearLayout>

    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="@string/cancel" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_verify"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="@string/verify" />

    </LinearLayout>

</LinearLayout>
