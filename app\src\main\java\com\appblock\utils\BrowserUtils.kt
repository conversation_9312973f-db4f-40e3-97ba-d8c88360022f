package com.appblock.utils

import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo

object BrowserUtils {
    
    private const val TAG = "BrowserUtils"
    
    // Common browser package names
    val BROWSER_PACKAGES = setOf(
        "com.android.chrome",           // Chrome
        "org.mozilla.firefox",          // Firefox
        "com.microsoft.emmx",           // Edge
        "com.opera.browser",            // Opera
        "com.opera.mini.native",        // Opera Mini
        "com.brave.browser",            // Brave
        "com.duckduckgo.mobile.android", // DuckDuckGo
        "com.samsung.android.app.sbrowser", // Samsung Internet
        "com.UCMobile.intl",            // UC Browser
        "com.android.browser",          // Stock Android Browser
        "com.kiwibrowser.browser",      // Kiwi Browser
        "com.vivaldi.browser"           // Vivaldi
    )
    
    /**
     * Check if package is a known browser
     */
    fun isBrowserApp(packageName: String): <PERSON><PERSON>an {
        return BROWSER_PACKAGES.contains(packageName)
    }
    
    /**
     * Extract URL from browser accessibility node
     */
    fun extractUrlFromBrowser(packageName: String, rootNode: AccessibilityNodeInfo?): String? {
        if (rootNode == null) return null
        
        return when (packageName) {
            "com.android.chrome" -> extractUrlFromChrome(rootNode)
            "org.mozilla.firefox" -> extractUrlFromFirefox(rootNode)
            "com.microsoft.emmx" -> extractUrlFromEdge(rootNode)
            "com.samsung.android.app.sbrowser" -> extractUrlFromSamsungBrowser(rootNode)
            else -> extractUrlGeneric(rootNode)
        }
    }
    
    /**
     * Extract URL from Chrome browser
     */
    private fun extractUrlFromChrome(rootNode: AccessibilityNodeInfo): String? {
        return try {
            // Chrome typically has the URL in a node with resource ID "url_bar" or similar
            val urlNodes = findNodesByResourceId(rootNode, "com.android.chrome:id/url_bar")
            if (urlNodes.isNotEmpty()) {
                val urlText = urlNodes[0].text?.toString()
                Log.d(TAG, "Chrome URL extracted: $urlText")
                return urlText
            }
            
            // Fallback: look for nodes that might contain URLs
            val urlFromContent = findUrlInNodeContent(rootNode)
            Log.d(TAG, "Chrome URL from content: $urlFromContent")
            urlFromContent
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting URL from Chrome", e)
            null
        }
    }
    
    /**
     * Extract URL from Firefox browser
     */
    private fun extractUrlFromFirefox(rootNode: AccessibilityNodeInfo): String? {
        return try {
            // Firefox URL bar resource ID
            val urlNodes = findNodesByResourceId(rootNode, "org.mozilla.firefox:id/mozac_browser_toolbar_url_view")
            if (urlNodes.isNotEmpty()) {
                val urlText = urlNodes[0].text?.toString()
                Log.d(TAG, "Firefox URL extracted: $urlText")
                return urlText
            }
            
            // Fallback
            val urlFromContent = findUrlInNodeContent(rootNode)
            Log.d(TAG, "Firefox URL from content: $urlFromContent")
            urlFromContent
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting URL from Firefox", e)
            null
        }
    }
    
    /**
     * Extract URL from Edge browser
     */
    private fun extractUrlFromEdge(rootNode: AccessibilityNodeInfo): String? {
        return try {
            // Edge URL bar - try common resource IDs
            val possibleIds = listOf(
                "com.microsoft.emmx:id/url_bar",
                "com.microsoft.emmx:id/address_bar"
            )
            
            for (resourceId in possibleIds) {
                val urlNodes = findNodesByResourceId(rootNode, resourceId)
                if (urlNodes.isNotEmpty()) {
                    val urlText = urlNodes[0].text?.toString()
                    Log.d(TAG, "Edge URL extracted: $urlText")
                    return urlText
                }
            }
            
            // Fallback
            val urlFromContent = findUrlInNodeContent(rootNode)
            Log.d(TAG, "Edge URL from content: $urlFromContent")
            urlFromContent
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting URL from Edge", e)
            null
        }
    }
    
    /**
     * Extract URL from Samsung Internet browser
     */
    private fun extractUrlFromSamsungBrowser(rootNode: AccessibilityNodeInfo): String? {
        return try {
            val urlNodes = findNodesByResourceId(rootNode, "com.samsung.android.app.sbrowser:id/location_bar_edit_text")
            if (urlNodes.isNotEmpty()) {
                val urlText = urlNodes[0].text?.toString()
                Log.d(TAG, "Samsung Browser URL extracted: $urlText")
                return urlText
            }
            
            // Fallback
            val urlFromContent = findUrlInNodeContent(rootNode)
            Log.d(TAG, "Samsung Browser URL from content: $urlFromContent")
            urlFromContent
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting URL from Samsung Browser", e)
            null
        }
    }
    
    /**
     * Generic URL extraction for unknown browsers
     */
    private fun extractUrlGeneric(rootNode: AccessibilityNodeInfo): String? {
        return try {
            // Look for common URL bar indicators
            val commonUrlBarIds = listOf(
                "url_bar", "address_bar", "location_bar", "omnibox"
            )
            
            for (idSuffix in commonUrlBarIds) {
                val nodes = findNodesByPartialResourceId(rootNode, idSuffix)
                if (nodes.isNotEmpty()) {
                    val urlText = nodes[0].text?.toString()
                    if (urlText != null && isValidUrl(urlText)) {
                        Log.d(TAG, "Generic URL extracted: $urlText")
                        return urlText
                    }
                }
            }
            
            // Last resort: scan all text content for URLs
            val urlFromContent = findUrlInNodeContent(rootNode)
            Log.d(TAG, "Generic URL from content: $urlFromContent")
            urlFromContent
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting URL generically", e)
            null
        }
    }
    
    /**
     * Find nodes by exact resource ID
     */
    private fun findNodesByResourceId(rootNode: AccessibilityNodeInfo, resourceId: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        
        fun searchRecursively(node: AccessibilityNodeInfo) {
            if (node.viewIdResourceName == resourceId) {
                result.add(node)
            }
            
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    searchRecursively(child)
                }
            }
        }
        
        searchRecursively(rootNode)
        return result
    }
    
    /**
     * Find nodes by partial resource ID match
     */
    private fun findNodesByPartialResourceId(rootNode: AccessibilityNodeInfo, partialId: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        
        fun searchRecursively(node: AccessibilityNodeInfo) {
            val resourceId = node.viewIdResourceName
            if (resourceId != null && resourceId.contains(partialId, ignoreCase = true)) {
                result.add(node)
            }
            
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    searchRecursively(child)
                }
            }
        }
        
        searchRecursively(rootNode)
        return result
    }
    
    /**
     * Search for URL-like text in all node content
     */
    private fun findUrlInNodeContent(rootNode: AccessibilityNodeInfo): String? {
        val urlPattern = Regex("""https?://[^\s]+|www\.[^\s]+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}""")
        
        fun searchRecursively(node: AccessibilityNodeInfo): String? {
            // Check current node text
            val text = node.text?.toString()
            if (text != null) {
                val match = urlPattern.find(text)
                if (match != null && isValidUrl(match.value)) {
                    return match.value
                }
            }
            
            // Check content description
            val contentDesc = node.contentDescription?.toString()
            if (contentDesc != null) {
                val match = urlPattern.find(contentDesc)
                if (match != null && isValidUrl(match.value)) {
                    return match.value
                }
            }
            
            // Search children
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    val childResult = searchRecursively(child)
                    if (childResult != null) return childResult
                }
            }
            
            return null
        }
        
        return searchRecursively(rootNode)
    }
    
    /**
     * Basic URL validation
     */
    private fun isValidUrl(text: String): Boolean {
        if (text.isBlank()) return false
        
        // Must contain a dot and be reasonable length
        return text.contains(".") && 
               text.length > 3 && 
               text.length < 2000 &&
               !text.contains(" ") // URLs shouldn't have spaces
    }
}