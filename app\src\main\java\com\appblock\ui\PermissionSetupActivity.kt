package com.appblock.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import com.appblock.utils.PermissionUtils

/**
 * Activity to guide users through setting up all required permissions
 */
class PermissionSetupActivity : Activity() {

    companion object {
        private const val TAG = "PermissionSetupActivity"
        private const val REQUEST_USAGE_STATS = 1001
        private const val REQUEST_ACCESSIBILITY = 1002
        private const val REQUEST_OVERLAY = 1003
        private const val REQUEST_DEVICE_ADMIN = 1004
        private const val REQUEST_NOTIFICATIONS = 1005
    }

    private lateinit var scrollView: ScrollView
    private lateinit var permissionContainer: LinearLayout
    private lateinit var btnContinue: Button
    private lateinit var tvStatus: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "PermissionSetupActivity created")
        
        createUI()
        updatePermissionStatus()
    }

    override fun onResume() {
        super.onResume()
        // Update status when returning from settings
        updatePermissionStatus()
    }

    private fun createUI() {
        // Main container
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(24, 24, 24, 24)
        }

        // Title
        val title = TextView(this).apply {
            text = "🔒 App Block Setup"
            textSize = 28f
            gravity = android.view.Gravity.CENTER
            setPadding(0, 0, 0, 32)
            setTextColor(ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.black))
        }
        mainLayout.addView(title)

        // Description
        val description = TextView(this).apply {
            text = "To block apps effectively, App Block needs special permissions. Please grant the following permissions:"
            textSize = 16f
            setPadding(0, 0, 0, 24)
            setTextColor(ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.black))
        }
        mainLayout.addView(description)

        // Scroll view for permissions
        scrollView = ScrollView(this)
        permissionContainer = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
        }
        scrollView.addView(permissionContainer)
        mainLayout.addView(scrollView)

        // Status text
        tvStatus = TextView(this).apply {
            text = "Checking permissions..."
            textSize = 14f
            setPadding(0, 16, 0, 16)
            gravity = android.view.Gravity.CENTER
            setTextColor(ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.black))
        }
        mainLayout.addView(tvStatus)

        // Continue button
        btnContinue = Button(this).apply {
            text = "Continue to App"
            textSize = 18f
            setPadding(16, 16, 16, 16)
            isEnabled = false
            setOnClickListener {
                finishSetup()
            }
        }
        mainLayout.addView(btnContinue)

        setContentView(mainLayout)
    }

    private fun updatePermissionStatus() {
        Log.d(TAG, "Updating permission status...")
        
        // Clear existing permission views
        permissionContainer.removeAllViews()
        
        // Create permission cards
        createPermissionCard(
            "📊 Usage Stats Access",
            "Required to detect when blocked apps are opened",
            PermissionUtils.hasUsageStatsPermission(this),
            "This permission allows App Block to monitor which apps are being used. It's essential for detecting when a blocked app is opened.",
            REQUEST_USAGE_STATS
        )

        createPermissionCard(
            "♿ Accessibility Service",
            "Required to immediately block apps with overlay",
            PermissionUtils.isAccessibilityServiceEnabled(this),
            "The Accessibility Service allows App Block to instantly detect app launches and show blocking overlays. This is the core blocking mechanism.",
            REQUEST_ACCESSIBILITY
        )

        createPermissionCard(
            "🔝 Draw Over Other Apps",
            "Required to show blocking overlays",
            PermissionUtils.canDrawOverlays(this),
            "This permission allows App Block to display blocking screens over other apps. Without this, blocked apps cannot be properly covered.",
            REQUEST_OVERLAY
        )

        createPermissionCard(
            "🛡️ Device Administrator",
            "Recommended for tamper protection",
            PermissionUtils.isDeviceAdminActive(this),
            "Device Admin makes it harder to uninstall App Block and can lock the device if tampering is detected. This is optional but recommended.",
            REQUEST_DEVICE_ADMIN
        )

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            createPermissionCard(
                "🔔 Notifications",
                "Required for service notifications (Android 13+)",
                PermissionUtils.hasNotificationPermission(this),
                "Notification permission is required on Android 13+ to show the monitoring service notification.",
                REQUEST_NOTIFICATIONS
            )
        }

        // Update overall status
        updateOverallStatus()
    }

    private fun createPermissionCard(
        title: String,
        subtitle: String,
        isGranted: Boolean,
        description: String,
        requestCode: Int
    ) {
        val card = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
            setBackgroundColor(if (isGranted) 
                ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.holo_green_light)
                else ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.holo_orange_light))
            
            // Add margin
            val params = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            params.setMargins(0, 0, 0, 16)
            layoutParams = params
        }

        // Title with status
        val titleView = TextView(this).apply {
            text = "$title ${if (isGranted) "✅" else "❌"}"
            textSize = 18f
            setTextColor(ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.black))
        }
        card.addView(titleView)

        // Subtitle
        val subtitleView = TextView(this).apply {
            text = subtitle
            textSize = 14f
            setPadding(0, 4, 0, 8)
            setTextColor(ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.black))
        }
        card.addView(subtitleView)

        // Description
        val descView = TextView(this).apply {
            text = description
            textSize = 12f
            setPadding(0, 0, 0, 12)
            setTextColor(ContextCompat.getColor(this@PermissionSetupActivity, android.R.color.black))
        }
        card.addView(descView)

        // Grant button (only if not granted)
        if (!isGranted) {
            val grantButton = Button(this).apply {
                text = "Grant Permission"
                setOnClickListener {
                    requestPermission(requestCode)
                }
            }
            card.addView(grantButton)
        }

        permissionContainer.addView(card)
    }

    private fun requestPermission(requestCode: Int) {
        Log.d(TAG, "Requesting permission with code: $requestCode")
        
        try {
            val intent = when (requestCode) {
                REQUEST_USAGE_STATS -> {
                    Toast.makeText(this, "Please find and enable 'App Block' in the Usage Access settings", Toast.LENGTH_LONG).show()
                    PermissionUtils.openUsageStatsSettings(this)
                }
                REQUEST_ACCESSIBILITY -> {
                    Toast.makeText(this, "Please find and enable 'App Block' in the Accessibility settings", Toast.LENGTH_LONG).show()
                    PermissionUtils.openAccessibilitySettings(this)
                }
                REQUEST_OVERLAY -> {
                    Toast.makeText(this, "Please enable 'Allow display over other apps' for App Block", Toast.LENGTH_LONG).show()
                    PermissionUtils.openOverlaySettings(this)
                }
                REQUEST_DEVICE_ADMIN -> {
                    Toast.makeText(this, "Please activate App Block as Device Administrator", Toast.LENGTH_LONG).show()
                    PermissionUtils.requestDeviceAdmin(this)
                }
                REQUEST_NOTIFICATIONS -> {
                    // For notifications, we need to request the runtime permission
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                        requestPermissions(arrayOf(android.Manifest.permission.POST_NOTIFICATIONS), REQUEST_NOTIFICATIONS)
                        return
                    } else {
                        return
                    }
                }
                else -> return
            }
            
            startActivityForResult(intent, requestCode)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting permission", e)
            Toast.makeText(this, "Error opening settings: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Log.d(TAG, "Permission request result: $requestCode")
        
        // Update status after returning from settings
        updatePermissionStatus()
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        Log.d(TAG, "Runtime permission result: $requestCode")
        
        // Update status after runtime permission request
        updatePermissionStatus()
    }

    private fun updateOverallStatus() {
        val status = PermissionUtils.getPermissionStatusSummary(this)
        val grantedCount = status.values.count { it }
        val totalCount = status.size
        
        val criticalPermissions = listOf("Usage Stats", "Accessibility Service")
        val hasCriticalPermissions = criticalPermissions.all { status[it] == true }
        
        tvStatus.text = "Permissions: $grantedCount/$totalCount granted"
        
        if (hasCriticalPermissions) {
            btnContinue.isEnabled = true
            btnContinue.text = "✅ Continue to App"
            tvStatus.text = "${tvStatus.text} - Ready to block apps!"
        } else {
            btnContinue.isEnabled = false
            btnContinue.text = "❌ Need Critical Permissions"
            tvStatus.text = "${tvStatus.text} - Missing critical permissions"
        }
        
        Log.d(TAG, "Permission status: $grantedCount/$totalCount, Critical: $hasCriticalPermissions")
    }

    private fun finishSetup() {
        Log.d(TAG, "Permission setup completed")
        
        Toast.makeText(this, "🎉 App Block is ready to protect your device!", Toast.LENGTH_LONG).show()
        
        // Return to main activity
        finish()
    }

    override fun onBackPressed() {
        // Warn user about incomplete setup
        if (!btnContinue.isEnabled) {
            Toast.makeText(this, "⚠️ App Block needs permissions to work properly", Toast.LENGTH_LONG).show()
        }
        super.onBackPressed()
    }
}
