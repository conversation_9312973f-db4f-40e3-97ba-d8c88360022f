1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appblock"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions for app blocking functionality -->
12    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
12-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:6:5-78
12-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:7:22-75
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
17-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
19-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
20
21    <permission
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
22        android:name="com.appblock.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.appblock.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:14:5-96:19
28        android:allowBackup="true"
28-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:15:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
30        android:extractNativeLibs="true"
31        android:label="App Block"
31-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:16:9-34
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@android:style/Theme.Material.Light" >
33-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:18:9-60
34
35        <!-- Main Activity -->
36        <activity
36-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:22:9-31:20
37            android:name="com.appblock.MainActivity"
37-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:23:13-41
38            android:exported="true"
38-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:24:13-36
39            android:label="App Block" >
39-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:25:13-38
40
41            <!-- Removed theme reference -->
42            <intent-filter>
42-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:27:13-30:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:28:17-69
43-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:28:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:29:17-77
45-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:29:27-74
46            </intent-filter>
47        </activity>
48
49        <!-- App Selection Activity -->
50        <activity
50-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:34:9-37:52
51            android:name="com.appblock.ui.SimpleAppSelectionActivity"
51-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:35:13-58
52            android:exported="false"
52-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:36:13-37
53            android:label="Select Apps to Block" />
53-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:37:13-49
54
55        <!-- Permission Setup Activity -->
56        <activity
56-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:40:9-43:47
57            android:name="com.appblock.ui.PermissionSetupActivity"
57-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:41:13-55
58            android:exported="false"
58-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:42:13-37
59            android:label="App Block Setup" />
59-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:43:13-44
60
61        <!-- Accessibility Service for app monitoring -->
62        <service
62-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:46:9-56:19
63            android:name="com.appblock.service.AppBlockAccessibilityService"
63-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:47:13-65
64            android:exported="false"
64-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:48:13-37
65            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
65-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:49:13-79
66            <intent-filter>
66-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:50:13-52:29
67                <action android:name="android.accessibilityservice.AccessibilityService" />
67-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:51:17-92
67-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:51:25-89
68            </intent-filter>
69
70            <meta-data
70-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:53:13-55:72
71                android:name="android.accessibilityservice"
71-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:54:17-60
72                android:resource="@xml/accessibility_service_config" />
72-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:55:17-69
73        </service>
74
75        <!-- Usage Stats Monitoring Service -->
76        <service
76-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:59:9-63:58
77            android:name="com.appblock.service.UsageStatsMonitorService"
77-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:60:13-61
78            android:exported="false"
78-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:61:13-37
79            android:foregroundServiceType="specialUse"
79-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:63:13-55
80            android:process=":monitor" />
80-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:62:13-39
81
82        <!-- Device Admin Receiver -->
83        <receiver
83-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:66:9-77:20
84            android:name="com.appblock.receiver.AppBlockDeviceAdminReceiver"
84-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:67:13-65
85            android:exported="true"
85-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:68:13-36
86            android:permission="android.permission.BIND_DEVICE_ADMIN" >
86-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:69:13-70
87            <meta-data
87-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:70:13-72:56
88                android:name="android.app.device_admin"
88-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:71:17-56
89                android:resource="@xml/device_admin" />
89-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:72:17-53
90
91            <intent-filter>
91-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:73:13-76:29
92                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
92-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:74:17-82
92-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:74:25-79
93                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
93-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:75:17-83
93-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:75:25-80
94            </intent-filter>
95        </receiver>
96
97        <!-- Boot Receiver -->
98        <receiver
98-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:80:9-89:20
99            android:name="com.appblock.receiver.BootReceiver"
99-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:81:13-50
100            android:exported="true" >
100-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:82:13-36
101            <intent-filter android:priority="1000" >
101-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:83:13-88:29
101-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:83:28-51
102                <action android:name="android.intent.action.BOOT_COMPLETED" />
102-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:84:17-79
102-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:84:25-76
103                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
103-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:85:17-84
103-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:85:25-81
104                <action android:name="android.intent.action.PACKAGE_REPLACED" />
104-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:86:17-81
104-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:86:25-78
105
106                <data android:scheme="package" />
106-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:87:17-50
106-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:87:23-47
107            </intent-filter>
108        </receiver>
109
110        <!-- Permission Monitor Receiver -->
111        <receiver
111-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:92:9-94:40
112            android:name="com.appblock.receiver.PermissionMonitorReceiver"
112-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:93:13-63
113            android:exported="false" />
113-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:94:13-37
114
115        <provider
115-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
116            android:name="androidx.startup.InitializationProvider"
116-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
117            android:authorities="com.appblock.androidx-startup"
117-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
118            android:exported="false" >
118-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
119            <meta-data
119-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.emoji2.text.EmojiCompatInitializer"
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
121                android:value="androidx.startup" />
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
122            <meta-data
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
123                android:name="androidx.work.WorkManagerInitializer"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
124                android:value="androidx.startup" />
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
125            <meta-data
125-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
126-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
127                android:value="androidx.startup" />
127-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
130                android:value="androidx.startup" />
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
131        </provider>
132
133        <service
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
134            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
136            android:enabled="@bool/enable_system_alarm_service_default"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
137            android:exported="false" />
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
138        <service
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
139            android:name="androidx.work.impl.background.systemjob.SystemJobService"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
141            android:enabled="@bool/enable_system_job_service_default"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
142            android:exported="true"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
143            android:permission="android.permission.BIND_JOB_SERVICE" />
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
144        <service
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
145            android:name="androidx.work.impl.foreground.SystemForegroundService"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
147            android:enabled="@bool/enable_system_foreground_service_default"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
148            android:exported="false" />
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
149
150        <receiver
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
151            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
153            android:enabled="true"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
154            android:exported="false" />
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
155        <receiver
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
156            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
158            android:enabled="false"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
159            android:exported="false" >
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
160            <intent-filter>
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
161                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
162                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
163            </intent-filter>
164        </receiver>
165        <receiver
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
166            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
168            android:enabled="false"
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
169            android:exported="false" >
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
170            <intent-filter>
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
171                <action android:name="android.intent.action.BATTERY_OKAY" />
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
172                <action android:name="android.intent.action.BATTERY_LOW" />
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
173            </intent-filter>
174        </receiver>
175        <receiver
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
176            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
177            android:directBootAware="false"
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
178            android:enabled="false"
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
179            android:exported="false" >
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
180            <intent-filter>
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
181                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
182                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
183            </intent-filter>
184        </receiver>
185        <receiver
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
186            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
188            android:enabled="false"
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
190            <intent-filter>
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
191                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
192            </intent-filter>
193        </receiver>
194        <receiver
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
195            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
196            android:directBootAware="false"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
197            android:enabled="false"
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
198            android:exported="false" >
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
199            <intent-filter>
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
200                <action android:name="android.intent.action.BOOT_COMPLETED" />
200-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:84:17-79
200-->C:\Users\<USER>\Documents\augment-projects\appblock\app\src\main\AndroidManifest.xml:84:25-76
201                <action android:name="android.intent.action.TIME_SET" />
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
202                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
203            </intent-filter>
204        </receiver>
205        <receiver
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
206            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
208            android:enabled="@bool/enable_system_alarm_service_default"
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
209            android:exported="false" >
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
210            <intent-filter>
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
211                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
212            </intent-filter>
213        </receiver>
214        <receiver
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
215            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
217            android:enabled="true"
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
218            android:exported="true"
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
219            android:permission="android.permission.DUMP" >
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
220            <intent-filter>
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
221                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
222            </intent-filter>
223        </receiver>
224
225        <uses-library
225-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
226            android:name="androidx.window.extensions"
226-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
227            android:required="false" />
227-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
228        <uses-library
228-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
229            android:name="androidx.window.sidecar"
229-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
230            android:required="false" />
230-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
231
232        <service
232-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
233            android:name="androidx.room.MultiInstanceInvalidationService"
233-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
234            android:directBootAware="true"
234-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
235            android:exported="false" />
235-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
236
237        <receiver
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
238            android:name="androidx.profileinstaller.ProfileInstallReceiver"
238-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
239            android:directBootAware="false"
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
240            android:enabled="true"
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
241            android:exported="true"
241-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
244                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
244-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
247                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
247-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
247-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
250                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
250-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
253                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
254            </intent-filter>
255        </receiver>
256    </application>
257
258</manifest>
